import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../auth/presentation/services/auth_guard_service.dart';
import 'cart_screen.dart';

/// Wrapper for CartScreen that handles authentication before showing the cart
class CartScreenWrapper extends ConsumerStatefulWidget {
  const CartScreenWrapper({super.key});

  @override
  ConsumerState<CartScreenWrapper> createState() => _CartScreenWrapperState();
}

class _CartScreenWrapperState extends ConsumerState<CartScreenWrapper> {
  bool _isCheckingAuth = true;
  bool _isAuthenticated = false;

  @override
  void initState() {
    super.initState();
    _checkAuthentication();
  }

  Future<void> _checkAuthentication() async {
    debugPrint('[CartScreenWrapper] Fast authentication check...');

    // Use fast local auth check for immediate navigation
    final isAuthenticated = await AuthGuardService.requireAuthForCart(
      context,
      ref,
      onSuccess: () {
        debugPrint('[CartScreenWrapper] Fast auth check successful');
        if (mounted) {
          setState(() {
            _isAuthenticated = true;
            _isCheckingAuth = false;
          });
        }
      },
    );

    // If authentication failed or was cancelled, redirect to home
    if (!isAuthenticated && mounted) {
      debugPrint('[CartScreenWrapper] Fast auth check failed, redirecting to home');
      context.go('/home');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isCheckingAuth) {
      // Show loading screen while checking authentication
      return Scaffold(
        backgroundColor: const Color(0xFFF3F4F6),
        body: const Center(
          child: CircularProgressIndicator(
            color: Color(0xFFFF6982),
          ),
        ),
      );
    }

    if (_isAuthenticated) {
      // Show cart screen if authenticated
      return const CartScreen();
    }

    // This should not happen as we redirect on auth failure
    // But just in case, show a fallback
    return Scaffold(
      backgroundColor: const Color(0xFFF3F4F6),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'Authentication required',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1F2937),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF6982),
                foregroundColor: Colors.white,
              ),
              child: const Text('Go to Home'),
            ),
          ],
        ),
      ),
    );
  }
}
