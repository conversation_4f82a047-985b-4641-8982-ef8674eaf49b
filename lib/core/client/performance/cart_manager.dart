import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/client_models.dart';
import '../cart/cart_operations.dart';
import '../travelgator_client.dart';

/// High-performance cart manager with debouncing and bulk operations
class CartManager {
  final CartOperations _cartOps;
  final Duration _debounceDelay;
  
  // Debouncing state
  Timer? _debounceTimer;
  final Map<String, CartItemUpdate> _pendingItemUpdates = {};
  String? _pendingPaymentMethod;
  
  // Request deduplication
  final Map<String, Future<CartResponse>> _activeRequests = {};
  
  // Cache
  CartResponse? _cachedCart;
  DateTime? _cacheTimestamp;
  final Duration _cacheExpiry;
  
  // Callbacks
  void Function(CartResponse cart)? onCartUpdated;
  void Function(ClientException error)? onError;

  CartManager({
    required CartOperations cartOperations,
    Duration debounceDelay = const Duration(milliseconds: 300),
    Duration cacheExpiry = const Duration(seconds: 30),
  }) : _cartOps = cartOperations,
       _debounceDelay = debounceDelay,
       _cacheExpiry = cacheExpiry;

  /// Get cart with caching
  Future<CartResponse> getCart({bool forceRefresh = false}) async {
    // Check cache first
    if (!forceRefresh && _isCacheValid()) {
      if (kDebugMode) {
        debugPrint('[CartManager] Returning cached cart');
      }
      return _cachedCart!;
    }

    // Deduplicate concurrent requests
    const requestKey = 'getCart';
    if (_activeRequests.containsKey(requestKey)) {
      if (kDebugMode) {
        debugPrint('[CartManager] Returning existing getCart request');
      }
      return _activeRequests[requestKey]!;
    }

    // Execute request
    final future = _executeGetCart();
    _activeRequests[requestKey] = future;

    try {
      final result = await future;
      _updateCache(result);
      _activeRequests.remove(requestKey);
      return result;
    } catch (e) {
      _activeRequests.remove(requestKey);
      rethrow;
    }
  }

  /// Schedule cart update with debouncing
  Future<CartResponse> scheduleUpdate({
    List<CartItemUpdate>? items,
    String? paymentMethod,
  }) async {
    // Merge with pending updates
    if (items != null) {
      for (final item in items) {
        _pendingItemUpdates[item.variantId] = item;
      }
    }

    if (paymentMethod != null) _pendingPaymentMethod = paymentMethod;

    // Cancel existing timer
    _debounceTimer?.cancel();
    
    // Create completer for this update
    final completer = Completer<CartResponse>();
    
    // Schedule execution
    _debounceTimer = Timer(_debounceDelay, () async {
      try {
        final result = await _executePendingUpdates();
        completer.complete(result);
      } catch (e) {
        completer.completeError(e);
      }
    });

    return completer.future;
  }

  /// Execute pending updates
  Future<CartResponse> _executePendingUpdates() async {
    if (_pendingItemUpdates.isEmpty &&
        _pendingPaymentMethod == null) {
      // No pending updates, return current cart
      return await getCart();
    }

    try {
      if (kDebugMode) {
        debugPrint('[CartManager] Executing pending updates: ${_pendingItemUpdates.length} items');
      }

      final result = await _cartOps.comprehensiveUpdate(
        items: _pendingItemUpdates.values.toList(),
        paymentMethod: _pendingPaymentMethod,
      );

      // Clear pending updates
      _clearPendingUpdates();
      
      // Update cache and notify
      _updateCache(result);
      onCartUpdated?.call(result);
      
      return result;
    } catch (e) {
      final error = e is ClientException ? e : ClientException(
        message: 'Cart update failed: $e',
        type: ClientErrorType.unknown,
        originalError: e,
      );
      
      onError?.call(error);
      rethrow;
    }
  }

  /// Bulk operations for multiple items
  Future<CartResponse> bulkUpdateItems(List<CartItemUpdate> items) async {
    if (kDebugMode) {
      debugPrint('[CartManager] Bulk updating ${items.length} items');
    }

    return await scheduleUpdate(items: items);
  }

  /// Optimized quantity updates
  Future<CartResponse> updateQuantity(String variantId, int quantity) async {
    return await scheduleUpdate(items: [
      CartItemUpdate(variantId: variantId, quantity: quantity),
    ]);
  }



  /// Optimized payment method setting
  Future<CartResponse> setPaymentMethod(String paymentMethod) async {
    return await scheduleUpdate(paymentMethod: paymentMethod);
  }

  /// Batch item operations
  Future<CartResponse> batchItemOperations({
    List<String>? itemsToRemove,
    List<String>? itemsToSaveForLater,
    List<String>? itemsToMoveToCart,
    Map<String, int>? quantityUpdates,
  }) async {
    final updates = <CartItemUpdate>[];

    // Remove items
    if (itemsToRemove != null) {
      updates.addAll(itemsToRemove.map((variantId) => 
        CartItemUpdate(variantId: variantId, quantity: 0)));
    }

    // Save for later
    if (itemsToSaveForLater != null) {
      updates.addAll(itemsToSaveForLater.map((variantId) => 
        CartItemUpdate(variantId: variantId, quantity: 1, selected: false)));
    }

    // Move to cart
    if (itemsToMoveToCart != null) {
      updates.addAll(itemsToMoveToCart.map((variantId) => 
        CartItemUpdate(variantId: variantId, quantity: 1, selected: true)));
    }

    // Quantity updates
    if (quantityUpdates != null) {
      updates.addAll(quantityUpdates.entries.map((entry) => 
        CartItemUpdate(variantId: entry.key, quantity: entry.value)));
    }

    return await bulkUpdateItems(updates);
  }

  /// Execute get cart request
  Future<CartResponse> _executeGetCart() async {
    try {
      final result = await _cartOps.getCart();
      onCartUpdated?.call(result);
      return result;
    } catch (e) {
      final error = e is ClientException ? e : ClientException(
        message: 'Failed to get cart: $e',
        type: ClientErrorType.unknown,
        originalError: e,
      );
      
      onError?.call(error);
      rethrow;
    }
  }

  /// Check if cache is valid
  bool _isCacheValid() {
    return _cachedCart != null && 
           _cacheTimestamp != null && 
           DateTime.now().difference(_cacheTimestamp!) < _cacheExpiry;
  }

  /// Update cache
  void _updateCache(CartResponse cart) {
    _cachedCart = cart;
    _cacheTimestamp = DateTime.now();
  }

  /// Clear pending updates
  void _clearPendingUpdates() {
    _pendingItemUpdates.clear();
    _pendingPaymentMethod = null;
  }

  /// Clear cache
  void clearCache() {
    _cachedCart = null;
    _cacheTimestamp = null;
  }

  /// Get cached cart if available
  CartResponse? getCachedCart() {
    return _isCacheValid() ? _cachedCart : null;
  }

  /// Check if there are pending updates
  bool hasPendingUpdates() {
    return _pendingItemUpdates.isNotEmpty ||
           _pendingPaymentMethod != null;
  }

  /// Force execute pending updates immediately
  Future<CartResponse> flushPendingUpdates() async {
    _debounceTimer?.cancel();
    return await _executePendingUpdates();
  }

  /// Dispose resources
  void dispose() {
    _debounceTimer?.cancel();
    _activeRequests.clear();
    _clearPendingUpdates();
    clearCache();
  }
}

/// Provider for cart manager
final cartManagerProvider = Provider<CartManager>((ref) {
  final client = ref.watch(travelGatorClientProvider);
  final cartOps = CartOperations(client);
  
  final manager = CartManager(
    cartOperations: cartOps,
    debounceDelay: const Duration(milliseconds: 300),
    cacheExpiry: const Duration(seconds: 30),
  );

  ref.onDispose(() {
    manager.dispose();
  });

  return manager;
});
