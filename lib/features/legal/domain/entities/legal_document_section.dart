/// Entity representing a section within a legal document
class LegalDocumentSection {
  final String title;
  final String content;
  final int order;
  final List<LegalDocumentSection>? subsections;

  const LegalDocumentSection({
    required this.title,
    required this.content,
    required this.order,
    this.subsections,
  });

  /// Create a copy of this section with updated fields
  LegalDocumentSection copyWith({
    String? title,
    String? content,
    int? order,
    List<LegalDocumentSection>? subsections,
  }) {
    return LegalDocumentSection(
      title: title ?? this.title,
      content: content ?? this.content,
      order: order ?? this.order,
      subsections: subsections ?? this.subsections,
    );
  }

  /// Check if this section has subsections
  bool get hasSubsections => subsections != null && subsections!.isNotEmpty;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LegalDocumentSection &&
        other.title == title &&
        other.content == content &&
        other.order == order &&
        _listEquals(other.subsections, subsections);
  }

  @override
  int get hashCode {
    return title.hashCode ^
        content.hashCode ^
        order.hashCode ^
        subsections.hashCode;
  }

  /// Helper method to compare lists
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'LegalDocumentSection(title: $title, order: $order, hasSubsections: $hasSubsections)';
  }
}
