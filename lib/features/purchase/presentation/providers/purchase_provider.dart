import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/purchase_config.dart';
import '../../../../l10n/app_localizations.dart';

class PurchaseNotifier extends StateNotifier<PurchaseConfig> {
  PurchaseNotifier(super.initialConfig);

  void updateData(String data) {
    debugPrint('🔄 Data changed to: $data');

    // When data changes, we need to reset days to the first available option for this data
    state = state.copyWith(selectedData: data);

    // Find the first available day option for this data amount using selectedOptions
    if (_savedVariants != null && _savedVariants!.isNotEmpty) {
      String? firstAvailableDay;

      for (final variant in _savedVariants!) {
        // Use selectedOptions for more accurate matching
        String? variantData;
        String? variantDays;

        for (final option in variant.selectedOptions) {
          if (option.name.toLowerCase() == 'gb') {
            variantData = option.value;
          } else if (option.name.toLowerCase() == 'days') {
            variantDays = option.value;
          }
        }

        // Check if this variant matches the selected data
        if (variantData == data && variantDays != null) {
          if (firstAvailableDay == null) {
            firstAvailableDay = variantDays;
            break;
          }
        }
      }

      // Update to first available day for this data amount
      if (firstAvailableDay != null) {
        state = state.copyWith(selectedDays: firstAvailableDay);
        debugPrint('🔄 Data changed to $data, auto-selected first available day: $firstAvailableDay');
      }
    }

    // Trigger variant update when data changes
    _updateVariantFromDataAndDays();
  }

  void updateDays(String days) {
    state = state.copyWith(selectedDays: days);
    // Trigger variant update when days change
    _updateVariantFromDataAndDays();
  }

  // Store saved variants for automatic selection and dropdown updates
  List<dynamic>? _savedVariants;

  void setAvailableVariants(List<dynamic> variants) {
    debugPrint('🔄 Saving ${variants.length} variants for dropdown updates');
    _savedVariants = variants;
    _updateVariantFromDataAndDays();
  }

  void _updateVariantFromDataAndDays() {
    if (_savedVariants == null || _savedVariants!.isEmpty) return;

    final selectedData = state.selectedData;
    final selectedDays = state.selectedDays;

    debugPrint('🔍 Looking for variant with data: $selectedData, days: $selectedDays');

    // Find variant that matches the selected data and days using selectedOptions
    dynamic matchingVariant;

    for (final variant in _savedVariants!) {
      debugPrint('🔍 Checking variant: ${variant.displayName}');

      // Check selectedOptions for exact matches
      String? variantData;
      String? variantDays;

      for (final option in variant.selectedOptions) {
        debugPrint('🔍   Option: ${option.name} = ${option.value}');

        if (option.name.toLowerCase() == 'gb' || option.name.toLowerCase() == 'data') {
          variantData = option.value;
        } else if (option.name.toLowerCase() == 'days' || option.name.toLowerCase() == 'validity') {
          variantDays = option.value;
        }
      }

      // debugPrint('🔍   Variant data: $variantData, days: $variantDays');

      // Check for exact matches
      final dataMatch = variantData?.toLowerCase() == selectedData.toLowerCase();
      final daysMatch = variantDays?.toLowerCase() == selectedDays.toLowerCase();

      if (dataMatch && daysMatch) {
        matchingVariant = variant;
        // debugPrint('✅ Found exact matching variant: ${variant.displayName}');
        break;
      }
    }

    // If exact match not found, try partial matching with selectedOptions
    if (matchingVariant == null) {
      // debugPrint('🔍 No exact match found, trying partial matching...');

      for (final variant in _savedVariants!) {
        String? variantData;
        String? variantDays;

        for (final option in variant.selectedOptions) {
          if (option.name.toLowerCase() == 'gb' || option.name.toLowerCase() == 'data') {
            variantData = option.value;
          } else if (option.name.toLowerCase() == 'days' || option.name.toLowerCase() == 'validity') {
            variantDays = option.value;
          }
        }

        // Extract numeric values for comparison
        final selectedDataNum = selectedData.replaceAll(RegExp(r'[^0-9]'), '');
        final selectedDaysNum = selectedDays.replaceAll(RegExp(r'[^0-9]'), '');
        final variantDataNum = variantData?.replaceAll(RegExp(r'[^0-9]'), '') ?? '';
        final variantDaysNum = variantDays?.replaceAll(RegExp(r'[^0-9]'), '') ?? '';

        final dataMatch = selectedDataNum.isNotEmpty && variantDataNum == selectedDataNum;
        final daysMatch = selectedDaysNum.isNotEmpty && variantDaysNum == selectedDaysNum;

        // debugPrint('🔍   Comparing: $selectedDataNum/$selectedDaysNum vs $variantDataNum/$variantDaysNum');

        if (dataMatch && daysMatch) {
          matchingVariant = variant;
          // debugPrint('✅ Found partial matching variant: ${variant.displayName}');
          break;
        }
      }
    }

    // If still no match, try fallback to title/displayName matching
    if (matchingVariant == null) {
      // debugPrint('🔍 No selectedOptions match found, trying title matching...');

      for (final variant in _savedVariants!) {
        final variantTitle = variant.title?.toLowerCase() ?? '';
        final variantDisplayName = variant.displayName?.toLowerCase() ?? '';

        // Extract data amount from selected data (e.g., "5GB" -> "5")
        final dataAmount = selectedData.replaceAll(RegExp(r'[^0-9]'), '');
        // Extract days amount from selected days (e.g., "7 days" -> "7")
        final daysAmount = selectedDays.replaceAll(RegExp(r'[^0-9]'), '');

        final dataMatch = variantTitle.contains(dataAmount) ||
                         variantDisplayName.contains(dataAmount);
        final daysMatch = variantTitle.contains(daysAmount) ||
                         variantDisplayName.contains(daysAmount);

        if (dataMatch && daysMatch) {
          matchingVariant = variant;
          // debugPrint('✅ Found title matching variant: ${variant.displayName}');
          break;
        }
      }
    }

    // Update the selected variant if found
    if (matchingVariant != null) {
      final variantPrice = double.tryParse(matchingVariant.price.amount) ?? 0.0;
      state = state.copyWith(
        selectedVariantId: matchingVariant.id,
        selectedVariantTitle: matchingVariant.displayName,
        unitPrice: variantPrice,
      );
      debugPrint('🔄 Auto-selected variant: ${matchingVariant.displayName} - \$$variantPrice');
    } else {
      debugPrint('❌ No matching variant found for $selectedData / $selectedDays');
    }
  }

  void updateESimType(ESimType type) {
    debugPrint('🔄 PurchaseNotifier: updateESimType called with ${type.displayName}');
    debugPrint('🔄 PurchaseNotifier: Previous state - eSimType: ${state.eSimType.displayName}, selectedIccid: ${state.selectedIccid}');

    state = state.copyWith(
      eSimType: type,
      // Set default ICCID when switching to top up, clear when switching to new eSIM
      selectedIccid: type == ESimType.topUp
          ? (state.selectedIccid ?? 'Select your TravelGator eSIM')
          : null,
    );

    debugPrint('🔄 PurchaseNotifier: New state - eSimType: ${state.eSimType.displayName}, selectedIccid: ${state.selectedIccid}');
  }

  void updateIccid(String? iccid) {
    debugPrint('🔄 PurchaseNotifier: updateIccid called with: $iccid');
    debugPrint('🔄 PurchaseNotifier: Previous selectedIccid: ${state.selectedIccid}');

    state = state.copyWith(selectedIccid: iccid);

    debugPrint('🔄 PurchaseNotifier: New selectedIccid: ${state.selectedIccid}');
  }

  void updateQuantity(int quantity) {
    if (quantity > 0) {
      debugPrint('🔢 PurchaseNotifier: updateQuantity called with: $quantity');
      debugPrint('🔢 PurchaseNotifier: Previous quantity: ${state.quantity}, unitPrice: ${state.unitPrice}, totalPrice: ${state.totalPrice}');

      state = state.copyWith(quantity: quantity);

      debugPrint('🔢 PurchaseNotifier: New quantity: ${state.quantity}, totalPrice: ${state.totalPrice}');
    }
  }

  void incrementQuantity() {
    final newQuantity = state.quantity + 1;
    debugPrint('🔢 PurchaseNotifier: incrementQuantity from ${state.quantity} to $newQuantity');
    debugPrint('🔢 PurchaseNotifier: Previous totalPrice: ${state.totalPrice}');

    state = state.copyWith(quantity: newQuantity);

    debugPrint('🔢 PurchaseNotifier: New totalPrice: ${state.totalPrice}');
  }

  void decrementQuantity() {
    if (state.quantity > 1) {
      final newQuantity = state.quantity - 1;
      // debugPrint('🔢 PurchaseNotifier: decrementQuantity from ${state.quantity} to $newQuantity');
      // debugPrint('🔢 PurchaseNotifier: Previous totalPrice: ${state.totalPrice}');

      state = state.copyWith(quantity: newQuantity);

      // debugPrint('🔢 PurchaseNotifier: New totalPrice: ${state.totalPrice}');
    } else {
      // debugPrint('🔢 PurchaseNotifier: Cannot decrement quantity below 1');
    }
  }

  void toggleDetails() {
    // debugPrint('🔄 toggleDetails called - current: ${state.showDetails}');
    state = state.copyWith(showDetails: !state.showDetails);
    // debugPrint('🔄 toggleDetails completed - new: ${state.showDetails}');
  }

  void updateVariant(String variantId, String variantTitle, double variantPrice) {
    debugPrint('🔄 PurchaseNotifier: updateVariant called');
    debugPrint('🔄 PurchaseNotifier: variantId: $variantId');
    debugPrint('🔄 PurchaseNotifier: variantTitle: $variantTitle');
    debugPrint('🔄 PurchaseNotifier: variantPrice: $variantPrice');
    debugPrint('🔄 PurchaseNotifier: Previous unitPrice: ${state.unitPrice}, totalPrice: ${state.totalPrice}');

    state = state.copyWith(
      selectedVariantId: variantId,
      selectedVariantTitle: variantTitle,
      unitPrice: variantPrice,
    );

    debugPrint('🔄 PurchaseNotifier: New unitPrice: ${state.unitPrice}, totalPrice: ${state.totalPrice}');
  }
}

// Provider for purchase configuration
final purchaseProvider = StateNotifierProvider.family<PurchaseNotifier, PurchaseConfig, PurchaseConfig>(
  (ref, initialConfig) => PurchaseNotifier(initialConfig),
);

// Dynamic data options provider based on product variants
final dataOptionsFromVariantsProvider = Provider.family<List<String>, List<dynamic>>((ref, variants) {
  final dataOptions = <String>{};

  for (final variant in variants) {
    // Extract data from variant title/displayName since selectedOptions might not have the data field
    final variantTitle = variant.displayName ?? variant.title ?? '';

    // Look for data pattern like "1 GB", "3 GB", etc.
    final dataMatch = RegExp(r'(\d+)\s*GB', caseSensitive: false).firstMatch(variantTitle);
    if (dataMatch != null) {
      dataOptions.add('${dataMatch.group(1)} GB');
    }

    // Also check selectedOptions as fallback
    for (final option in variant.selectedOptions) {
      if (option.name.toLowerCase() == 'gb' || option.name.toLowerCase() == 'data') {
        dataOptions.add(option.value);
      }
    }
  }

  // Sort data options by numeric value
  final sortedOptions = dataOptions.toList();
  sortedOptions.sort((a, b) {
    final aNum = int.tryParse(a.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0;
    final bNum = int.tryParse(b.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0;
    return aNum.compareTo(bNum);
  });

  debugPrint('🔍 Extracted data options: $sortedOptions');
  return sortedOptions;
});

// Dynamic days options provider based on product variants
final daysOptionsFromVariantsProvider = Provider.family<List<String>, List<dynamic>>((ref, variants) {
  final daysOptions = <String>{};

  for (final variant in variants) {
    // Extract days from variant title/displayName since selectedOptions might not have the days field
    final variantTitle = variant.displayName ?? variant.title ?? '';

    // Look for days pattern like "1 Day", "3 Days", etc.
    final daysMatch = RegExp(r'(\d+)\s*Days?', caseSensitive: false).firstMatch(variantTitle);
    if (daysMatch != null) {
      final dayNum = daysMatch.group(1);
      daysOptions.add(dayNum == '1' ? '1 Day' : '$dayNum Days');
    }

    // Also check selectedOptions as fallback
    for (final option in variant.selectedOptions) {
      if (option.name.toLowerCase() == 'days' || option.name.toLowerCase() == 'validity') {
        daysOptions.add(option.value);
      }
    }
  }

  // Sort days options by numeric value
  final sortedOptions = daysOptions.toList();
  sortedOptions.sort((a, b) {
    final aNum = int.tryParse(a.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0;
    final bNum = int.tryParse(b.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0;
    return aNum.compareTo(bNum);
  });

  debugPrint('🔍 Extracted days options: $sortedOptions');
  return sortedOptions;
});

// Smart data options provider that only shows data amounts with available variants
final smartDataOptionsProvider = Provider.family<List<String>, List<dynamic>>((ref, variants) {
  final availableDataOptions = <String>{};

  // debugPrint('🔍 === ANALYZING ALL VARIANTS FOR DATA OPTIONS ===');
  // debugPrint('🔍 Total variants: ${variants.length}');

  // Extract all data options using selectedOptions for accuracy
  for (int i = 0; i < variants.length; i++) {
    final variant = variants[i];
    // debugPrint('🔍 Variant $i: "${variant.displayName ?? variant.title}"');
    // debugPrint('🔍   selectedOptions: ${variant.selectedOptions?.length ?? 0}');

    // Use selectedOptions for more accurate data extraction
    for (final option in variant.selectedOptions) {
      // debugPrint('🔍     Option: ${option.name} = ${option.value}');

      if (option.name.toLowerCase() == 'gb') {
        availableDataOptions.add(option.value);
        // debugPrint('🔍   ✅ Found data: ${option.value}');
      }
    }
  }

  // Sort by numeric value
  final sortedOptions = availableDataOptions.toList();
  sortedOptions.sort((a, b) {
    final aNum = int.tryParse(a.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0;
    final bNum = int.tryParse(b.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0;
    return aNum.compareTo(bNum);
  });

  // debugPrint('🔍 Final smart data options: $sortedOptions');
  return sortedOptions;
});

// Smart days options provider that only shows days with available variants for selected data
final smartDaysOptionsProvider = Provider.family<List<String>, ({List<dynamic> variants, String selectedData})>((ref, params) {
  final availableDaysOptions = <String>{};

  // debugPrint('🔍 === ANALYZING VARIANTS FOR DAYS OPTIONS (${params.selectedData}) ===');
  // debugPrint('🔍 Total variants: ${params.variants.length}');

  // Only show days that have variants for the selected data amount
  for (int i = 0; i < params.variants.length; i++) {
    final variant = params.variants[i];
    // debugPrint('🔍 Variant $i: "${variant.displayName ?? variant.title}"');

    // Use selectedOptions for more accurate matching
    String? variantData;
    String? variantDays;

    for (final option in variant.selectedOptions) {
      if (option.name.toLowerCase() == 'gb') {
        variantData = option.value;
      } else if (option.name.toLowerCase() == 'days') {
        variantDays = option.value;
      }
    }

    // debugPrint('🔍   Variant data: $variantData');

    // If this variant matches the selected data, add its days option
    if (variantData == params.selectedData && variantDays != null) {
      // debugPrint('🔍   ✅ Matches selected data: ${params.selectedData}');
      availableDaysOptions.add(variantDays);
      // debugPrint('🔍   ✅ Found days: $variantDays');
    } else {
      // debugPrint('🔍   ❌ Does not match selected data: ${params.selectedData}');
    }
  }

  // Sort by numeric value
  final sortedOptions = availableDaysOptions.toList();
  sortedOptions.sort((a, b) {
    final aNum = int.tryParse(a.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0;
    final bNum = int.tryParse(b.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0;
    return aNum.compareTo(bNum);
  });

  // debugPrint('🔍 Final smart days options for ${params.selectedData}: $sortedOptions');
  return sortedOptions;
});

// Fallback data options provider (for backward compatibility)
final dataOptionsProvider = Provider<List<String>>((ref) {
  return ['1GB', '2GB', '3GB', '5GB', '10GB', '12GB'];
});

// Fallback days options provider (for backward compatibility)
final daysOptionsProvider = Provider<List<String>>((ref) {
  return ['1 day', '3 days', '7 days', '15 days', '30 days'];
});

// Day options mapping - maps internal values to localized display values
final dayOptionsMapProvider = Provider.family<Map<String, String>, AppLocalizations>((ref, l10n) {
  return {
    '1 day': l10n.oneDay,
    '3 days': l10n.threeDays,
    '7 days': l10n.sevenDays,
    '15 days': l10n.fifteenDays,
    '30 days': l10n.thirtyDays,
  };
});

// Localized days options provider - returns display values
final localizedDaysOptionsProvider = Provider.family<List<String>, AppLocalizations>((ref, l10n) {
  final dayMap = ref.watch(dayOptionsMapProvider(l10n));
  return dayMap.values.toList();
});

// Helper to get localized display value for internal value
final getLocalizedDayProvider = Provider.family<String, ({AppLocalizations l10n, String internalValue})>((ref, params) {
  final dayMap = ref.watch(dayOptionsMapProvider(params.l10n));
  return dayMap[params.internalValue] ?? params.internalValue;
});

// Mock ICCID options provider (in real app, this would come from API)
final iccidOptionsProvider = Provider<List<String>>((ref) {
  return [
    'Select your TravelGator eSIM',
    '8901234567890123456',
    '8901234567890123457',
    '8901234567890123458',
  ];
});
