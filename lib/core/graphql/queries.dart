class GraphQLQueries {
  static String getProducts = '''
    query Products(\$first: Int!, \$query: String, \$after: String) {
      products(first: \$first, query: \$query, after: \$after) {
        pageInfo {
          hasNextPage
          endCursor
        }
        edges {
          node {
            id
            title
            handle
            description
            descriptionHtml
            featuredImage {
              url
              altText
            }
            tags
            metafields(identifiers: [
              {namespace: "custom", key: "travel_region"},
              {namespace: "custom", key: "travel_country"}
            ]) {
              key
              namespace
              value
              type
              reference {
                ... on Metaobject {
                  id
                  handle
                  type
                  fields {
                    key
                    value
                  }
                }
              }
            }
            variants(first: 50) {
              edges {
                node {
                  id
                  title
                  price {
                    amount
                    currencyCode
                  }
                  compareAtPrice {
                    amount
                    currencyCode
                  }
                  selectedOptions {
                    name
                    value
                  }
                  availableForSale
                }
              }
            }
          }
        }
      }
    }
  ''';

  static String getProductsByRegionAndCountry = '''
    query ProductsByRegionAndCountry(
      \$first: Int!,
      \$after: String,
      \$query: String
    ) {
      products(
        first: \$first,
        after: \$after,
        query: \$query
      ) {
        pageInfo {
          hasNextPage
          endCursor
        }
        edges {
          node {
            id
            title
            handle
            description
            descriptionHtml
            featuredImage {
              url
              altText
            }
            tags
            metafields(identifiers: [
              {namespace: "custom", key: "travel_region"},
              {namespace: "custom", key: "travel_country"}
            ]) {
              key
              namespace
              value
              type
              reference {
                ... on Metaobject {
                  id
                  handle
                  type
                  fields {
                    key
                    value
                  }
                }
              }
            }
            variants(first: 50) {
              edges {
                node {
                  id
                  title
                  price {
                    amount
                    currencyCode
                  }
                  compareAtPrice {
                    amount
                    currencyCode
                  }
                  selectedOptions {
                    name
                    value
                  }
                  availableForSale
                }
              }
            }
          }
        }
      }
    }
  ''';

  static String getRegions = '''
    query Regions {
      metaobjects(type: "travel_region", first: 20) {
        nodes {
          id
          handle
          fields {
            key
            value
          }
        }
      }
    }
  ''';

  static String getCountries = '''
    query Countries {
      metaobjects(type: "travel_country", first: 50) {
        nodes {
          id
          handle
          fields {
            key
            value
          }
        }
      }
    }
  ''';

  static String getBanners = '''
    query GetBanners(\$first: Int!) {
      metaobjects(type: "banner", first: \$first) {
        edges {
          node {
            id
            handle
            imageUrl: field(key: "image_url") {
              value
            }
            width: field(key: "width") {
              value
            }
            height: field(key: "height") {
              value
            }
            linkUrl: field(key: "link_url") {
              value
            }
          }
        }
      }
    }
  ''';

  static String getBannerByHandle = '''
    query GetBannerByHandle(\$handle: String!) {
      metaobject(handle: {type: "banner", handle: \$handle}) {
        id
        handle
        imageUrl: field(key: "image_url") {
          value
        }
        width: field(key: "width") {
          value
        }
        height: field(key: "height") {
          value
        }
        linkUrl: field(key: "link_url") {
          value
        }
      }
    }
  ''';
}
