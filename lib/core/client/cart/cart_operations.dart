import 'package:flutter/foundation.dart';
import '../models/client_models.dart';
import '../travelgator_client.dart';

/// Cart operations helper providing simplified methods for cart management
class CartOperations {
  final TravelGatorClient _client;

  CartOperations(this._client);

  /// Get current cart
  Future<CartResponse> getCart() async {
    return await _client.getCart();
  }



  /// Update item quantity in cart
  Future<CartResponse> updateItemQuantity({
    required String variantId,
    required int quantity,
    String? productId,
  }) async {
    if (kDebugMode) {
      debugPrint('[CartOperations] Updating item $variantId quantity to $quantity');
    }

    final request = CartUpdateRequest(
      items: [
        CartItemUpdate(
          variantId: variantId,
          productId: productId,
          quantity: quantity,
        ),
      ],
    );
    return await _client.updateCart(request);
  }

  /// Add item to cart
  Future<CartResponse> addItem({
    required String variantId,
    required int quantity,
    String? productId,
    bool? selected,
  }) async {
    if (kDebugMode) {
      debugPrint('[CartOperations] Adding item $variantId with quantity $quantity');
    }

    final request = CartUpdateRequest(
      items: [
        CartItemUpdate(
          variantId: variantId,
          productId: productId,
          quantity: quantity,
          selected: selected ?? true,
        ),
      ],
    );
    return await _client.updateCart(request);
  }

  /// Remove item from cart
  Future<CartResponse> removeItem({
    required String variantId,
    String? productId,
  }) async {
    if (kDebugMode) {
      debugPrint('[CartOperations] Removing item $variantId');
    }

    final request = CartUpdateRequest(
      items: [
        CartItemUpdate(
          variantId: variantId,
          productId: productId,
          quantity: 0,
        ),
      ],
    );
    return await _client.updateCart(request);
  }

  /// Save item for later (unselect item)
  Future<CartResponse> saveForLater({
    required String variantId,
    String? productId,
  }) async {
    if (kDebugMode) {
      debugPrint('[CartOperations] Saving item $variantId for later');
    }

    final request = CartUpdateRequest(
      items: [
        CartItemUpdate(
          variantId: variantId,
          productId: productId,
          quantity: 1,
          selected: false,
        ),
      ],
    );
    return await _client.updateCart(request);
  }

  /// Move item to cart (select item)
  Future<CartResponse> moveToCart({
    required String variantId,
    String? productId,
  }) async {
    if (kDebugMode) {
      debugPrint('[CartOperations] Moving item $variantId to cart');
    }

    final request = CartUpdateRequest(
      items: [
        CartItemUpdate(
          variantId: variantId,
          productId: productId,
          quantity: 1,
          selected: true,
        ),
      ],
    );
    return await _client.updateCart(request);
  }

  /// Set payment method preference
  Future<CartResponse> setPaymentMethod(String paymentMethod) async {
    if (kDebugMode) {
      debugPrint('[CartOperations] Setting payment method: $paymentMethod');
    }

    final request = CartUpdateRequest(preferredPaymentMethod: paymentMethod);
    return await _client.updateCart(request);
  }

  /// Update multiple items at once
  Future<CartResponse> updateMultipleItems(List<CartItemUpdate> items) async {
    if (kDebugMode) {
      debugPrint('[CartOperations] Updating ${items.length} items');
    }

    final request = CartUpdateRequest(items: items);
    return await _client.updateCart(request);
  }

  /// Comprehensive cart update (items + payment method)
  Future<CartResponse> comprehensiveUpdate({
    List<CartItemUpdate>? items,
    String? paymentMethod,
  }) async {
    if (kDebugMode) {
      debugPrint('[CartOperations] Comprehensive cart update');
    }

    final request = CartUpdateRequest(
      items: items,
      preferredPaymentMethod: paymentMethod,
    );
    return await _client.updateCart(request);
  }

  /// Clear entire cart
  Future<CartResponse> clearCart(List<String> variantIds) async {
    if (kDebugMode) {
      debugPrint('[CartOperations] Clearing cart with ${variantIds.length} items');
    }

    final items = variantIds.map((variantId) => CartItemUpdate(
      variantId: variantId,
      quantity: 0,
    )).toList();

    final request = CartUpdateRequest(items: items);
    return await _client.updateCart(request);
  }

  /// Select all items in cart
  Future<CartResponse> selectAllItems(List<String> variantIds) async {
    if (kDebugMode) {
      debugPrint('[CartOperations] Selecting all ${variantIds.length} items');
    }

    final items = variantIds.map((variantId) => CartItemUpdate(
      variantId: variantId,
      quantity: 1,
      selected: true,
    )).toList();

    final request = CartUpdateRequest(items: items);
    return await _client.updateCart(request);
  }

  /// Unselect all items in cart (save all for later)
  Future<CartResponse> saveAllForLater(List<String> variantIds) async {
    if (kDebugMode) {
      debugPrint('[CartOperations] Saving all ${variantIds.length} items for later');
    }

    final items = variantIds.map((variantId) => CartItemUpdate(
      variantId: variantId,
      quantity: 1,
      selected: false,
    )).toList();

    final request = CartUpdateRequest(items: items);
    return await _client.updateCart(request);
  }

  /// Increment item quantity
  Future<CartResponse> incrementQuantity({
    required String variantId,
    required int currentQuantity,
    String? productId,
  }) async {
    return await updateItemQuantity(
      variantId: variantId,
      quantity: currentQuantity + 1,
      productId: productId,
    );
  }

  /// Decrement item quantity
  Future<CartResponse> decrementQuantity({
    required String variantId,
    required int currentQuantity,
    String? productId,
  }) async {
    final newQuantity = (currentQuantity - 1).clamp(0, double.infinity).toInt();
    return await updateItemQuantity(
      variantId: variantId,
      quantity: newQuantity,
      productId: productId,
    );
  }

  /// Toggle item selection
  Future<CartResponse> toggleItemSelection({
    required String variantId,
    required bool currentlySelected,
    String? productId,
  }) async {
    if (currentlySelected) {
      return await saveForLater(variantId: variantId, productId: productId);
    } else {
      return await moveToCart(variantId: variantId, productId: productId);
    }
  }


}
