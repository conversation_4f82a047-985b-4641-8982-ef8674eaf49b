import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_travelgator/l10n/app_localizations.dart';
import 'package:flutter_travelgator/l10n/app_localizations_vi.dart';
import 'package:flutter_travelgator/l10n/app_localizations_en.dart';

void main() {
  group('Payment Error Message Localization', () {
    late AppLocalizations viLocalizations;
    late AppLocalizations enLocalizations;

    setUpAll(() {
      // Initialize Vietnamese localizations
      viLocalizations = AppLocalizationsVi();
      // Initialize English localizations
      enLocalizations = AppLocalizationsEn();
    });

    group('Vietnamese Localization', () {
      test('should return correct Vietnamese payment success message', () {
        expect(viLocalizations.paymentSuccessful, equals('🎉 Thanh toán thành công!'));
      });

      test('should return correct Vietnamese payment canceled message', () {
        expect(viLocalizations.paymentCanceled, equals('Thanh toán đã bị hủy'));
      });

      test('should return correct Vietnamese payment failed message', () {
        expect(viLocalizations.paymentFailed, equals('Thanh toán thất bại'));
      });

      test('should return correct Vietnamese unknown error message', () {
        expect(viLocalizations.paymentErrorUnknown, equals('Lỗi thanh toán không xác định'));
      });

      test('should return correct Vietnamese payment intent creation failure message', () {
        expect(viLocalizations.paymentIntentCreationFailed,
               equals('Không thể tạo yêu cầu thanh toán. Vui lòng thử lại.'));
      });

      test('should return correct Vietnamese backend verification failure message', () {
        expect(viLocalizations.paymentVerificationFailed,
               equals('Xác minh thanh toán thất bại. Vui lòng liên hệ hỗ trợ.'));
      });

      test('should return correct Vietnamese order completion failure message', () {
        expect(viLocalizations.paymentSucceededOrderFailed,
               equals('Thanh toán thành công nhưng không thể hoàn tất đơn hàng. Vui lòng liên hệ hỗ trợ.'));
      });

      test('should return correct Vietnamese backend system error message', () {
        expect(viLocalizations.backendSystemError,
               equals('Lỗi hệ thống backend. Vui lòng liên hệ hỗ trợ kỹ thuật.'));
      });
    });

    group('English Localization', () {
      test('should return correct English payment success message', () {
        expect(enLocalizations.paymentSuccessful, equals('🎉 Payment successful!'));
      });

      test('should return correct English payment canceled message', () {
        expect(enLocalizations.paymentCanceled, equals('Payment canceled'));
      });

      test('should return correct English payment failed message', () {
        expect(enLocalizations.paymentFailed, equals('Payment failed'));
      });

      test('should return correct English unknown error message', () {
        expect(enLocalizations.paymentErrorUnknown, equals('Unknown payment error'));
      });

      test('should return correct English payment intent creation failure message', () {
        expect(enLocalizations.paymentIntentCreationFailed,
               equals('Unable to create payment request. Please try again.'));
      });

      test('should return correct English backend verification failure message', () {
        expect(enLocalizations.paymentVerificationFailed,
               equals('Payment verification failed. Please contact support.'));
      });

      test('should return correct English order completion failure message', () {
        expect(enLocalizations.paymentSucceededOrderFailed,
               equals('Payment successful but unable to complete order. Please contact support.'));
      });

      test('should return correct English backend system error message', () {
        expect(enLocalizations.backendSystemError,
               equals('Backend system error. Please contact technical support.'));
      });
    });

    group('Localization Consistency', () {
      test('should have all payment error messages defined in both languages', () {
        // Test that all methods exist and return non-empty strings
        expect(viLocalizations.paymentSuccessful.isNotEmpty, isTrue);
        expect(enLocalizations.paymentSuccessful.isNotEmpty, isTrue);

        expect(viLocalizations.paymentCanceled.isNotEmpty, isTrue);
        expect(enLocalizations.paymentCanceled.isNotEmpty, isTrue);

        expect(viLocalizations.paymentFailed.isNotEmpty, isTrue);
        expect(enLocalizations.paymentFailed.isNotEmpty, isTrue);

        expect(viLocalizations.paymentErrorUnknown.isNotEmpty, isTrue);
        expect(enLocalizations.paymentErrorUnknown.isNotEmpty, isTrue);

        expect(viLocalizations.paymentIntentCreationFailed.isNotEmpty, isTrue);
        expect(enLocalizations.paymentIntentCreationFailed.isNotEmpty, isTrue);

        expect(viLocalizations.paymentVerificationFailed.isNotEmpty, isTrue);
        expect(enLocalizations.paymentVerificationFailed.isNotEmpty, isTrue);

        expect(viLocalizations.paymentSucceededOrderFailed.isNotEmpty, isTrue);
        expect(enLocalizations.paymentSucceededOrderFailed.isNotEmpty, isTrue);

        expect(viLocalizations.backendSystemError.isNotEmpty, isTrue);
        expect(enLocalizations.backendSystemError.isNotEmpty, isTrue);
      });
    });
  });
}
