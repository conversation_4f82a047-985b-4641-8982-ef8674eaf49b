import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/cart_provider.dart';
import '../../../../l10n/app_localizations.dart';

/// Service class to handle cart item operations
class CartItemService {
  final BuildContext context;
  final WidgetRef ref;
  final VoidCallback onCacheInvalidate;

  CartItemService(this.context, this.ref, this.onCacheInvalidate);

  /// Handle quantity change for an item
  void handleQuantityChange(item, int newQuantity) {
    if (newQuantity <= 0) {
      showRemoveConfirmationDialog(item);
    } else {
      ref.read(cartProvider.notifier).updateItem(
        variantId: item.variantId,
        quantity: newQuantity,
      );
      // Don't invalidate cache immediately for quantity changes to avoid refresh feeling
      // The cache will be invalidated when the cart state updates from the provider
      // onCacheInvalidate();
    }
  }

  /// Show confirmation dialog for item removal
  void showRemoveConfirmationDialog(item) {
    final l10n = AppLocalizations.of(context);
    
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text(
          l10n.remove,
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          l10n.removeItemConfirmation(item.title),
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
        ),
        actions: [
          _buildDialogButton(
            text: l10n.cancel,
            onPressed: () => Navigator.of(context).pop(),
            color: Colors.grey[600],
          ),
          _buildDialogButton(
            text: l10n.remove,
            onPressed: () => _confirmRemoveItem(item),
            color: Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildDialogButton({
    required String text,
    required VoidCallback onPressed,
    Color? color,
  }) {
    return TextButton(
      onPressed: onPressed,
      child: Text(
        text,
        style: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  void _confirmRemoveItem(item) {
    Navigator.of(context).pop();
    
    // Remove from provider
    ref.read(cartProvider.notifier).removeItem(
      variantId: item.variantId,
    );

    // Invalidate cache
    onCacheInvalidate();

    // Show success message
    _showRemovalSuccessMessage();
  }

  void _showRemovalSuccessMessage() {
    final l10n = AppLocalizations.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(l10n.itemRemovedFromCart),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
