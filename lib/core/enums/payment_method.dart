/// Payment method options for dynamic switching
enum PaymentMethod {
  stripe,
  shopify;

  /// Create PaymentMethod from string value
  static PaymentMethod fromString(String value) {
    switch (value.toLowerCase()) {
      case 'stripe':
        return PaymentMethod.stripe;
      case 'shopify':
        return PaymentMethod.shopify;
      default:
        return PaymentMethod.stripe; // Default fallback to Stripe
    }
  }

  /// Detect payment method from payment intent ID format
  /// Stripe payment intents start with specific prefixes (pi_, cs_, seti_, src_)
  /// Shopify orders typically use numeric or custom formats
  static PaymentMethod detectFromPaymentIntentId(String paymentIntentId) {
    // Stripe payment intent patterns
    if (paymentIntentId.startsWith('pi_') ||     // Payment Intent
        paymentIntentId.startsWith('cs_') ||     // Checkout Session
        paymentIntentId.startsWith('seti_') ||   // Setup Intent
        paymentIntentId.startsWith('src_')) {    // Source
      return PaymentMethod.stripe;
    }

    // Shopify order patterns
    if (paymentIntentId.contains('shopify') ||
        paymentIntentId.contains('order_') ||
        paymentIntentId.startsWith('gid://shopify/') ||
        RegExp(r'^\d+$').hasMatch(paymentIntentId)) {  // Pure numeric
      return PaymentMethod.shopify;
    }

    // Default fallback to Stripe for unknown patterns
    return PaymentMethod.stripe;
  }

  /// Get display name for UI
  String get displayName {
    switch (this) {
      case PaymentMethod.stripe:
        return 'Stripe';
      case PaymentMethod.shopify:
        return 'Shopify';
    }
  }

  /// Get string value for API calls
  String get value {
    switch (this) {
      case PaymentMethod.stripe:
        return 'stripe';
      case PaymentMethod.shopify:
        return 'shopify';
    }
  }

  /// Check if this is Stripe payment method
  bool get isStripe => this == PaymentMethod.stripe;

  /// Check if this is Shopify payment method
  bool get isShopify => this == PaymentMethod.shopify;
}