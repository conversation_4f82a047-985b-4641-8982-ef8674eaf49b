import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/cart_provider.dart';
import '../widgets/checkout_webview.dart';
import '../../domain/entities/checkout_item.dart';
import '../../domain/entities/cart.dart'; // Fixed import path
import '../../../payment/presentation/screens/checkout_screen.dart';
import '../../../payment/presentation/providers/stripe_providers.dart';
import '../../../../l10n/app_localizations.dart';

/// Service class to handle checkout operations
class CartCheckoutService {
  final BuildContext context;
  final WidgetRef ref;

  CartCheckoutService(this.context, this.ref);

  /// Handle the checkout process with all items
  Future<void> handleCheckout() async {
    try {
      _showCheckoutLoadingIndicator();
      
      final checkoutResult = await ref.read(cartProvider.notifier).checkout();
      
      _hideLoadingIndicator();
      
      if (checkoutResult != null && context.mounted) {
        await _processCheckoutResult(checkoutResult);
      } else if (context.mounted) {
        _showCheckoutFailedMessage();
      }
    } catch (e) {
      _hideLoadingIndicator();
      if (context.mounted) {
        _showCheckoutErrorMessage(e.toString());
      }
    }
  }

  /// Handle the checkout process with selected items only (legacy - full quantities)
  Future<void> handleCheckoutSelected({
    required List<String> selectedVariantIds,
  }) async {
    try {
      _showCheckoutLoadingIndicator();

      final checkoutResult = await ref.read(cartProvider.notifier).checkoutSelected(
        selectedVariantIds: selectedVariantIds,
      );

      _hideLoadingIndicator();

      if (checkoutResult != null && context.mounted) {
        await _processCheckoutResult(checkoutResult);
      } else if (context.mounted) {
        _showCheckoutFailedMessage();
      }
    } catch (e) {
      _hideLoadingIndicator();
      if (context.mounted) {
        _showCheckoutErrorMessage(e.toString());
      }
    }
  }

  /// Handle the checkout process with specific items and quantities
  Future<void> handleCheckoutSelectedWithQuantities({
    required List<CheckoutItem> items,
  }) async {
    try {
      _showCheckoutLoadingIndicator();

      final checkoutResult = await ref.read(cartProvider.notifier).checkoutSelectedWithQuantities(
        items: items,
      );

      _hideLoadingIndicator();

      if (checkoutResult != null && context.mounted) {
        await _processCheckoutResult(checkoutResult);
      } else if (context.mounted) {
        _showCheckoutFailedMessage();
      }
    } catch (e) {
      _hideLoadingIndicator();
      if (context.mounted) {
        _showCheckoutErrorMessage(e.toString());
      }
    }
  }

  /// Handle Stripe checkout process - UPDATED TO USE IN-APP CHECKOUT SCREEN
  Future<void> handleStripeCheckout() async {
    try {
      final cartState = ref.read(cartProvider);
      if (cartState.cart == null) {
        _showCheckoutErrorMessage('Cart is empty');
        return;
      }

      debugPrint('[CartCheckoutService] Starting in-app Stripe checkout');
      debugPrint('[CartCheckoutService] Cart total: \$${cartState.cart!.totalPrice}');
      debugPrint('[CartCheckoutService] Cart items: ${cartState.cart!.items.length}');

      // Check and initialize Stripe before proceeding
      final stripeState = ref.read(stripeInitializationProvider);
      if (!stripeState.isInitialized) {
        debugPrint('[CartCheckoutService] Stripe not initialized, initializing now...');
        _showCheckoutLoadingIndicator();
        
        try {
          await ref.read(stripeInitializationProvider.notifier).initialize();
          final newStripeState = ref.read(stripeInitializationProvider);
          
          if (!newStripeState.isInitialized) {
            _hideLoadingIndicator();
            _showCheckoutErrorMessage('Failed to initialize payment system. Please try again.');
            return;
          }
          
          debugPrint('[CartCheckoutService] ✅ Stripe initialized successfully');
        } catch (e) {
          _hideLoadingIndicator();
          _showCheckoutErrorMessage('Failed to initialize payment system: $e');
          return;
        }
        
        _hideLoadingIndicator();
      }

      // Navigate to in-app checkout screen (same as before, but now with real Stripe)
      if (!context.mounted) return;
      final result = await Navigator.of(context).push<dynamic>(
        MaterialPageRoute(
          builder: (context) => CheckoutScreen(
            cart: cartState.cart!,
          ),
        ),
      );

      if (result != null && context.mounted) {
        debugPrint('[CartCheckoutService] ✅ In-app payment completed successfully!');
        
        // Payment successful - cart is automatically handled by Stripe backend
        // NO need to call clearCart() API - this eliminates certificate errors
        _showFallbackSuccessMessage('Payment completed successfully!');
        
        // Optional: Refresh cart to get updated state (no clearing needed)
        // The backend will have updated the cart state when payment succeeded
        ref.read(cartProvider.notifier).getCart();
        
      } else {
        debugPrint('[CartCheckoutService] Payment was cancelled or failed');
      }
      
    } catch (e) {
      debugPrint('[CartCheckoutService] ❌ Stripe checkout exception: $e');
      if (context.mounted) {
        _showCheckoutErrorMessage('Stripe checkout failed: $e');
      }
    }
  }

  /// Handle Stripe checkout process with SELECTED ITEMS ONLY
  Future<void> handleStripeCheckoutWithSelectedItems(Cart filteredCart) async {
    try {
      debugPrint('[CartCheckoutService] Starting in-app Stripe checkout with selected items');
      debugPrint('[CartCheckoutService] Selected items total: \$${filteredCart.totalPrice}');
      debugPrint('[CartCheckoutService] Selected items count: ${filteredCart.items.length}');

      // Check and initialize Stripe before proceeding
      final stripeState = ref.read(stripeInitializationProvider);
      if (!stripeState.isInitialized) {
        debugPrint('[CartCheckoutService] Stripe not initialized, initializing now...');
        _showCheckoutLoadingIndicator();
        
        try {
          await ref.read(stripeInitializationProvider.notifier).initialize();
          final newStripeState = ref.read(stripeInitializationProvider);
          
          if (!newStripeState.isInitialized) {
            _hideLoadingIndicator();
            _showCheckoutErrorMessage('Failed to initialize payment system. Please try again.');
            return;
          }
          
          debugPrint('[CartCheckoutService] ✅ Stripe initialized successfully');
        } catch (e) {
          _hideLoadingIndicator();
          _showCheckoutErrorMessage('Failed to initialize payment system: $e');
          return;
        }
        
        _hideLoadingIndicator();
      }

      // Navigate to in-app checkout screen with filtered cart (selected items only)
      if (!context.mounted) return;
      final result = await Navigator.of(context).push<dynamic>(
        MaterialPageRoute(
          builder: (context) => CheckoutScreen(
            cart: filteredCart, // ✅ Only selected items
          ),
        ),
      );

      if (result == true) {
        // Payment was successful, refresh cart to remove purchased items
        debugPrint('[CartCheckoutService] Payment successful, refreshing cart...');

        // Add a small delay before refreshing cart to ensure backend has processed the payment
        await Future.delayed(const Duration(milliseconds: 500));

        try {
          await ref.read(cartProvider.notifier).getCart();
          debugPrint('[CartCheckoutService] Cart refreshed successfully after payment');
        } catch (e) {
          debugPrint('[CartCheckoutService] Error refreshing cart after payment: $e');

          // If cart refresh fails, try one more time after a longer delay
          debugPrint('[CartCheckoutService] Retrying cart refresh after longer delay...');
          await Future.delayed(const Duration(seconds: 2));

          try {
            await ref.read(cartProvider.notifier).getCart();
            debugPrint('[CartCheckoutService] Cart refresh retry successful');
          } catch (retryError) {
            debugPrint('[CartCheckoutService] Cart refresh retry also failed: $retryError');
            // Don't show error to user as payment was successful
            // The cart screen will handle the error state appropriately
          }
        }
      }
    } catch (e) {
      debugPrint('[CartCheckoutService] Error during selected items checkout: $e');
      if (context.mounted) {
        _showCheckoutErrorMessage('Checkout failed: ${e.toString()}');
      }
    }
  }

  void _showCheckoutLoadingIndicator() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 12),
            Text(AppLocalizations.of(context).processingCheckout),
          ],
        ),
        backgroundColor: const Color(0xFFFF6982),
        duration: const Duration(seconds: 30),
      ),
    );
  }

  void _hideLoadingIndicator() {
    if (context.mounted) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
    }
  }

  Future<void> _processCheckoutResult(String checkoutResult) async {
    try {
      final Map<String, dynamic> result = json.decode(checkoutResult);
      if (result['success'] == true && result['data']?['checkout_url'] != null) {
        final checkoutUrl = result['data']['checkout_url'] as String;
        await _navigateToCheckoutWebView(checkoutUrl);
      } else {
        _showBackendErrorMessage(result['message'] ?? 'Checkout failed');
      }
    } catch (e) {
      _showFallbackSuccessMessage(checkoutResult);
    }
  }

  Future<void> _navigateToCheckoutWebView(String checkoutUrl) async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CheckoutWebView(
          checkoutUrl: checkoutUrl,
          onCheckoutComplete: _handleCheckoutSuccess,
          onCheckoutError: _handleCheckoutError,
        ),
      ),
    );
  }

  void _handleCheckoutSuccess() {
    debugPrint('[CartCheckoutService] Checkout completed, navigating to home first');
    
    // Refresh cart after successful checkout
    ref.read(cartProvider.notifier).getCart();
    
    // Capture root context before navigation to avoid disposal issues
    final rootContext = Navigator.of(context, rootNavigator: true).context;
    final rootMessenger = ScaffoldMessenger.of(rootContext);
    final router = GoRouter.of(rootContext);
    
    // Navigate to home screen first
    router.go('/');
    
    // Show success notification
    _showSuccessNotification(rootContext, rootMessenger);
  }

  void _handleCheckoutError(String errorMessage) {
    debugPrint('[CartCheckoutService] Checkout error: $errorMessage');
    
    // Capture root context before navigation to avoid disposal issues
    final rootContext = Navigator.of(context, rootNavigator: true).context;
    final rootMessenger = ScaffoldMessenger.of(rootContext);
    final router = GoRouter.of(rootContext);
    
    // Navigate to home first, then show error notification
    router.go('/');
    
    // Show error notification
    _showErrorNotification(rootContext, rootMessenger, errorMessage);
  }

  void _showSuccessNotification(BuildContext rootContext, ScaffoldMessengerState rootMessenger) {
    // debugPrint('[CartCheckoutService] Showing success notification on home screen');
    try {
      rootMessenger.clearSnackBars();
      rootMessenger.showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(rootContext).orderCompletedSuccessfully),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
      // debugPrint('[CartCheckoutService] ✅ Success SnackBar shown successfully');
    } catch (e) {
      // debugPrint('[CartCheckoutService] ❌ Failed to show success SnackBar: $e');
    }
  }

  void _showErrorNotification(BuildContext rootContext, ScaffoldMessengerState rootMessenger, String errorMessage) {
    debugPrint('[CartCheckoutService] Showing error notification on home screen');
    try {
      rootMessenger.clearSnackBars();
      rootMessenger.showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(rootContext).checkoutFailed(errorMessage)),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: AppLocalizations.of(rootContext).retry,
            textColor: Colors.white,
            onPressed: () => handleCheckout(),
          ),
        ),
      );
      debugPrint('[CartCheckoutService] ✅ Error SnackBar shown successfully');
    } catch (e) {
      debugPrint('[CartCheckoutService] ❌ Failed to show error SnackBar: $e');
    }
  }

  void _showBackendErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showFallbackSuccessMessage(String checkoutResult) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(checkoutResult),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showCheckoutFailedMessage() {
    final l10n = AppLocalizations.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.checkoutFailedTitle,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(
              l10n.checkoutFailedMessage,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: l10n.retry,
          textColor: Colors.white,
          onPressed: () => handleCheckout(),
        ),
      ),
    );
  }

  void _showCheckoutErrorMessage(String error) {
    final l10n = AppLocalizations.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.checkoutErrorTitle,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(
              'Error: $error',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }
}
