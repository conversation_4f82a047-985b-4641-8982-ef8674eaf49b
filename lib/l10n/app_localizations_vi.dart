// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Vietnamese (`vi`).
class AppLocalizationsVi extends AppLocalizations {
  AppLocalizationsVi([String locale = 'vi']) : super(locale);

  @override
  String get appTitle => 'TravelGator';

  @override
  String get welcomeMessage => 'Chào mừng đến với TravelGator';

  @override
  String get signIn => 'Đăng Nhập';

  @override
  String get signUp => 'Đăng Ký';

  @override
  String get email => 'Email';

  @override
  String get password => 'Mật Khẩu';

  @override
  String get name => 'Tên';

  @override
  String get forgotPassword => 'Quên Mật Khẩu?';

  @override
  String get orContinueWith => 'hoặc tiếp tục với';

  @override
  String get dontHaveAccount => 'Bạn chưa có tài khoản?';

  @override
  String get alreadyHaveAccount => 'Bạn đã có tài k<PERSON>?';

  @override
  String get createAccount => 'Tạo Tà<PERSON>';

  @override
  String get getStarted => 'Bắt Đầu';

  @override
  String get legalDocuments => 'Tài Liệu Pháp Lý';

  @override
  String get userAgreement => 'Thỏa Thuận Người Dùng';

  @override
  String get privacyPolicy => 'Chính Sách Bảo Mật';

  @override
  String get termsAndConditions => 'Điều Khoản & Điều Kiện';

  @override
  String lastUpdated(String date) {
    return 'Cập nhật lần cuối: $date';
  }

  @override
  String get close => 'Đóng';

  @override
  String get languages => 'Ngôn Ngữ';

  @override
  String get selectLanguage => 'Chọn Ngôn Ngữ';

  @override
  String get languageDescription => 'Chọn ngôn ngữ ưa thích cho giao diện ứng dụng.';

  @override
  String get languageChangeNote => 'Giao diện ứng dụng sẽ cập nhật ngay lập tức khi bạn chọn ngôn ngữ mới.';

  @override
  String languageChanged(String language) {
    return 'Đã thay đổi ngôn ngữ thành $language';
  }

  @override
  String get accountInformation => 'Thông Tin Tài Khoản';

  @override
  String get transactionHistory => 'Lịch Sử Giao Dịch';

  @override
  String get paymentSettings => 'Cài Đặt Thanh Toán';

  @override
  String get currency => 'Tiền Tệ';

  @override
  String get helpCenter => 'Trung Tâm Trợ Giúp';

  @override
  String get logout => 'Đăng Xuất';

  @override
  String get logoutConfirmTitle => 'Đăng Xuất';

  @override
  String get logoutConfirmMessage => 'Bạn có chắc chắn muốn đăng xuất?';

  @override
  String get cancel => 'Hủy';

  @override
  String appVersion(String version) {
    return 'TravelGator phiên bản $version';
  }

  @override
  String get cart => 'Giỏ Hàng';

  @override
  String get cartIsEmpty => 'Giỏ hàng trống';

  @override
  String get addSomeProducts => 'Thêm một số sản phẩm để bắt đầu';

  @override
  String get startShopping => 'Bắt Đầu Mua Sắm';

  @override
  String get authenticationRequired => 'Yêu Cầu Xác Thực';

  @override
  String get pleaseSignInToCart => 'Vui lòng đăng nhập để truy cập giỏ hàng';

  @override
  String get unableToLoadCart => 'Không thể tải giỏ hàng';

  @override
  String get failedToLoadCart => 'Không thể tải giỏ hàng';

  @override
  String get tryAgain => 'Thử Lại';

  @override
  String get total => 'Tổng cộng:';

  @override
  String get addToCart => 'Thêm Vào Giỏ';

  @override
  String get buyNow => 'Mua Ngay';

  @override
  String get hideDetails => 'Ẩn chi tiết';

  @override
  String get remove => 'Xóa';

  @override
  String get itemRemovedFromCart => 'Đã xóa sản phẩm khỏi giỏ hàng';

  @override
  String get variant => 'Variant';

  @override
  String get noVariantsAvailable => 'Không có biến thể nào cho sản phẩm này';

  @override
  String failedToAddToCart(String error) {
    return 'Không thể thêm vào giỏ hàng: $error';
  }

  @override
  String get gb => 'GB';

  @override
  String get day => 'Ngày';

  @override
  String get days => 'Ngày';

  @override
  String get signedInSuccessfully => 'Đăng nhập thành công!';

  @override
  String signInFailed(String error) {
    return 'Đăng nhập thất bại: $error';
  }

  @override
  String get retry => 'Thử lại';

  @override
  String get orderCompletedSuccessfully => '🎉 Đặt hàng thành công!';

  @override
  String checkoutFailed(String error) {
    return '❌ Thanh toán thất bại: $error';
  }

  @override
  String buyNowFailed(String error) {
    return '❌ Mua ngay thất bại: $error';
  }

  @override
  String get processingCheckout => 'Đang xử lý thanh toán...';

  @override
  String get checkOut => 'Thanh toán';

  @override
  String get proceedToCheckout => 'Tiến hành thanh toán';

  @override
  String get home => 'Trang chủ';

  @override
  String get account => 'Tài khoản';

  @override
  String get sims => 'SIM';

  @override
  String get travelgator => 'TravelGator';

  @override
  String get rewards => 'Phần thưởng';

  @override
  String get travelgatorScreen => 'Màn hình TravelGator';

  @override
  String get welcomeToTravelgator => 'Chào mừng đến với\nTravelGator';

  @override
  String get stayConnected => 'Luôn kết nối';

  @override
  String get stayConnectedGlobally => 'Luôn kết nối\ntoàn cầu';

  @override
  String get discoverExplore => 'Khám phá & Tìm hiểu\nmột cách dễ dàng';

  @override
  String get discoverAmazingDestinations => 'Khám phá những\nđiểm đến tuyệt vời';

  @override
  String get details => 'Chi tiết';

  @override
  String get yourPurchase => 'Giao dịch của bạn';

  @override
  String get yourData => 'Dữ liệu của bạn';

  @override
  String get newESim => 'eSIM mới';

  @override
  String get topUp => 'Nạp tiền';

  @override
  String get totalPayment => 'Tổng thanh toán';

  @override
  String get seeDetails => 'Xem chi tiết';

  @override
  String get selectYourTravelgatorESim => 'Chọn eSIM TravelGator của bạn';

  @override
  String get dataAmount => 'Lượng dữ liệu';

  @override
  String get duration => 'Thời hạn';

  @override
  String get quantity => 'Số lượng';

  @override
  String get eSimType => 'Loại eSIM';

  @override
  String get reviewYourPurchase => 'Xem lại giao dịch';

  @override
  String get yourDataLabel => 'Dữ liệu của bạn';

  @override
  String get dayLabel => 'Ngày';

  @override
  String get oneDay => '1 ngày';

  @override
  String get threeDays => '3 ngày';

  @override
  String get sevenDays => '7 ngày';

  @override
  String get fifteenDays => '15 ngày';

  @override
  String get thirtyDays => '30 ngày';

  @override
  String removeItemConfirmation(String itemTitle) {
    return 'Bạn có chắc chắn muốn xóa \"$itemTitle\" khỏi giỏ hàng?';
  }

  @override
  String get checkoutFailedTitle => 'Thanh toán thất bại';

  @override
  String get checkoutFailedMessage => 'Có vấn đề khi xử lý thanh toán của bạn. Đây có thể là vấn đề cấu hình backend với Shopify.';

  @override
  String get checkoutErrorTitle => 'Lỗi thanh toán';

  @override
  String get deleteMyAccount => 'Xóa Tài Khoản';

  @override
  String get deleteAccountConfirmTitle => 'Xóa Tài Khoản';

  @override
  String get deleteAccountConfirmMessage => 'Bạn có chắc chắn muốn xóa tài khoản? Điều này sẽ xóa vĩnh viễn tất cả dữ liệu của bạn khỏi hệ thống.';

  @override
  String get delete => 'Xóa';

  @override
  String get accountDeletionFailed => 'Xóa tài khoản thất bại';

  @override
  String get accountDeletedSuccessfully => 'Xóa tài khoản thành công';

  @override
  String get cannotDeleteAccount => 'Không thể xóa tài khoản';

  @override
  String get checkingAccountDeletion => 'Đang kiểm tra khả năng xóa tài khoản...';

  @override
  String get deletingAccount => 'Đang xóa tài khoản...';

  @override
  String get serverErrorContactSupport => 'Lỗi máy chủ xảy ra. Vui lòng thử lại sau hoặc liên hệ hỗ trợ để được trợ giúp.';

  @override
  String get errorLoadingProducts => 'Lỗi tải sản phẩm';

  @override
  String get noProductsFound => 'Không tìm thấy sản phẩm';

  @override
  String get tryAdjustingFilter => 'Thử điều chỉnh bộ lọc của bạn';

  @override
  String productAddedToCart(String productTitle) {
    return '$productTitle đã được thêm vào giỏ hàng!';
  }

  @override
  String get cartOperationMayHaveFailed => 'Thao tác giỏ hàng có thể đã thất bại. Vui lòng kiểm tra giỏ hàng của bạn.';

  @override
  String get processingAddToCart => 'Đang thêm vào giỏ hàng...';

  @override
  String get from => 'Từ';

  @override
  String get minPrice => 'Giá tối thiểu';

  @override
  String get maxPrice => 'Giá tối đa';

  @override
  String get applyFilter => 'Áp dụng bộ lọc';

  @override
  String get clearFilter => 'Xóa bộ lọc';

  @override
  String get priceFilter => 'Bộ lọc giá';

  @override
  String get under => 'Dưới';

  @override
  String get over => 'Trên';

  @override
  String get orSetCustomRange => 'Hoặc đặt khoảng tùy chỉnh:';

  @override
  String get to => 'đến';

  @override
  String get countryVietnam => 'Việt Nam';

  @override
  String get countryChina => 'Trung Quốc';

  @override
  String get countryUnitedStates => 'Hoa Kỳ';

  @override
  String get countryNewZealand => 'New Zealand';

  @override
  String get regionAsean => 'ASEAN';

  @override
  String get regionEastAsia => 'Đông Á';

  @override
  String get regionNorthAmerica => 'Bắc Mỹ';

  @override
  String get regionOceania => 'Châu Đại Dương';

  @override
  String get eSIMRoamingData => 'Dữ liệu eSIM Roaming';

  @override
  String get countries => 'Quốc gia';

  @override
  String get regions => 'Khu vực';

  @override
  String get authenticationSuccessful => 'Xác thực thành công!';

  @override
  String get paymentSuccessful => '🎉 Thanh toán thành công!';

  @override
  String get paymentCanceled => 'Thanh toán đã bị hủy';

  @override
  String get paymentFailed => 'Thanh toán thất bại';

  @override
  String get paymentErrorUnknown => 'Lỗi thanh toán không xác định';

  @override
  String get paymentIntentCreationFailed => 'Không thể tạo yêu cầu thanh toán. Vui lòng thử lại.';

  @override
  String get paymentVerificationFailed => 'Xác minh thanh toán thất bại. Vui lòng liên hệ hỗ trợ.';

  @override
  String get paymentSucceededOrderFailed => 'Thanh toán thành công nhưng không thể hoàn tất đơn hàng. Vui lòng liên hệ hỗ trợ.';

  @override
  String get backendSystemError => 'Lỗi hệ thống backend. Vui lòng liên hệ hỗ trợ kỹ thuật.';
}
