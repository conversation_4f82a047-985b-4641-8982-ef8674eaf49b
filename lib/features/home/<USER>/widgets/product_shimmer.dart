import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ProductShimmer extends StatelessWidget {
  const ProductShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: const Color(0xFFC88D94),
      highlightColor: Colors.white,
      period: const Duration(milliseconds: 1000),
      child: Container(
        width: 180,
        height: 360, // Increased height to match ProductCard
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xFFF3F4F6)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Skeleton image part
            Container(
              height: 140, // Match ProductCard image height
              width: double.infinity,
              decoration: BoxDecoration(
                color: const Color(0xFFC88D94).withAlpha(50),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
            ),

            // Skeleton text part - match ProductCard structure
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0), // Match ProductCard padding
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title area - use Expanded to take available space
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Title line 1
                          Container(
                            width: double.infinity,
                            height: 10,
                            decoration: BoxDecoration(
                              color: const Color(0xFFC88D94).withAlpha(30),
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(height: 3),
                          // Title line 2
                          Container(
                            width: double.infinity,
                            height: 10,
                            decoration: BoxDecoration(
                              color: const Color(0xFFC88D94).withAlpha(30),
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(height: 3),
                          // Title line 3
                          Container(
                            width: 140,
                            height: 10,
                            decoration: BoxDecoration(
                              color: const Color(0xFFC88D94).withAlpha(30),
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Price line - fixed at bottom
                    Row(
                      children: [
                        Container(
                          width: 50,
                          height: 14,
                          decoration: BoxDecoration(
                            color: const Color(0xFFC88D94).withAlpha(40),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          width: 60,
                          height: 10,
                          decoration: BoxDecoration(
                            color: const Color(0xFFC88D94).withAlpha(20),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}