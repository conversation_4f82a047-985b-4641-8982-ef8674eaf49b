import 'package:flutter/material.dart';
import '../../../../core/utils/modal_manager.dart';
import '../../domain/entities/legal_document_type.dart';
import '../widgets/legal_documents_modal.dart';

/// Service class for managing legal documents modal display
class LegalDocumentsService {
  /// Show the legal documents modal with slide-up animation
  static Future<void> showLegalDocumentsModal(
    BuildContext context, {
    LegalDocumentType? initialDocumentType,
  }) async {
    debugPrint('[LegalDocumentsService] Showing legal documents modal');
    debugPrint('[LegalDocumentsService] Initial document type: $initialDocumentType');
    debugPrint('[LegalDocumentsService] Current context: ${context.runtimeType}');
    debugPrint('[LegalDocumentsService] Context mounted: ${context.mounted}');

    try {
      await showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        barrierColor: Colors.black.withValues(alpha: 0.5),
        useSafeArea: false,
        enableDrag: true,
        isDismissible: true,
        builder: (context) {
          return ManagedModal(
            child: LegalDocumentsModal(
              initialDocumentType: initialDocumentType,
            ),
          );
        },
      );
      debugPrint('[LegalDocumentsService] Legal documents modal shown successfully');
    } catch (e) {
      debugPrint('[LegalDocumentsService] Error in showModalBottomSheet: $e');
      rethrow;
    }
  }

  /// Show the User Agreement modal specifically
  static Future<void> showUserAgreement(BuildContext context) async {
    await showLegalDocumentsModal(
      context,
      initialDocumentType: LegalDocumentType.userAgreement,
    );
  }

  /// Show the Privacy Policy modal specifically
  static Future<void> showPrivacyPolicy(BuildContext context) async {
    await showLegalDocumentsModal(
      context,
      initialDocumentType: LegalDocumentType.privacyPolicy,
    );
  }

  /// Check if the modal can be shown (context is valid)
  static bool canShowModal(BuildContext context) {
    return context.mounted;
  }

  /// Show legal documents modal with error handling
  static Future<void> showLegalDocumentsModalSafe(
    BuildContext context, {
    LegalDocumentType? initialDocumentType,
    VoidCallback? onError,
  }) async {
    try {
      if (!canShowModal(context)) {
        debugPrint('[LegalDocumentsService] Cannot show modal - context not mounted');
        onError?.call();
        return;
      }

      debugPrint('[LegalDocumentsService] Showing legal documents modal safely');
      await showLegalDocumentsModal(
        context,
        initialDocumentType: initialDocumentType,
      );
    } catch (e) {
      debugPrint('[LegalDocumentsService] Error showing legal documents modal: $e');
      onError?.call();
    }
  }

  /// Show User Agreement with error handling
  static Future<void> showUserAgreementSafe(
    BuildContext context, {
    VoidCallback? onError,
  }) async {
    await showLegalDocumentsModalSafe(
      context,
      initialDocumentType: LegalDocumentType.userAgreement,
      onError: onError,
    );
  }

  /// Show Privacy Policy with error handling
  static Future<void> showPrivacyPolicySafe(
    BuildContext context, {
    VoidCallback? onError,
  }) async {
    await showLegalDocumentsModalSafe(
      context,
      initialDocumentType: LegalDocumentType.privacyPolicy,
      onError: onError,
    );
  }
}
