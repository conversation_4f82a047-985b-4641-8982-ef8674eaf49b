import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import 'package:flutter_travelgator/core/network/api_config.dart';
import 'package:flutter_travelgator/features/auth/presentation/providers/auth_providers.dart';
import 'package:flutter_travelgator/features/auth/data/models/enhanced_user_model.dart';
import '../../data/repositories/cart_repository_impl.dart';
import '../../domain/entities/checkout_item.dart';
import '../../domain/repositories/cart_repository.dart';
import 'cart_state.dart';

/// Provider for cart repository
final cartRepositoryProvider = Provider<CartRepository?>((ref) {
  try {
    final authRepository = ref.watch(authRepositoryProvider);
    if (authRepository == null) {
      return null;
    }
    return CartRepositoryImpl(
      dio: ref.watch(dioProvider),
      authRepository: authRepository,
      ref: ref,
    );
  } catch (e) {
    return null;
  }
});

/// Provider for Dio instance
final dioProvider = Provider<Dio>((ref) {
  return ApiConfig.createDioClient();
});

/// Provider for cart state
final cartProvider = StateNotifierProvider<CartNotifier, CartState>((ref) {
  final repository = ref.watch(cartRepositoryProvider);

  if (repository == null) {
    return CartNotifier.unauthenticated();
  }

  final notifier = CartNotifier(repository, ref);
  return notifier;
});

/// Notifier for cart operations
class CartNotifier extends StateNotifier<CartState> {
  final CartRepository? _repository;
  final Ref? _ref;

  CartNotifier(this._repository, this._ref) : super(const CartState()) {
    // No automatic cart loading - cart will only be loaded when explicitly requested
    // This prevents unwanted API calls after authentication
  }

  CartNotifier.unauthenticated() : _repository = null, _ref = null, super(const CartState(
    status: CartStatus.error,
    error: 'Authentication required',
    isAuthenticated: false,
  ));

  /// Handle connection errors by clearing tokens and showing appropriate error
  Future<void> _handleConnectionError(Failure failure) async {
    if (_ref == null) return;

    // Check if this is a connection error
    final isConnectionError = failure.message.contains('Connection refused') ||
        failure.message.contains('connection error') ||
        failure.message.contains('Network is unreachable') ||
        failure.message.contains('Failed to connect');

    // Check if this is a server error that should trigger sign out (500 errors, database issues)
    final isServerError = failure.message.toLowerCase().contains('server error') ||
        failure.message.contains('500') ||
        failure.message.toLowerCase().contains('no primary_preferred server') ||
        failure.message.toLowerCase().contains('cluster topology=unknown') ||
        failure.message.toLowerCase().contains('mongodb') ||
        failure.message.toLowerCase().contains('database connection');

    if (isConnectionError || isServerError) {
      debugPrint('🔥 [CartNotifier] Connection/Server error detected: ${failure.message}');

      // For server errors (500, database issues), immediately clear tokens
      // For connection errors, only clear tokens if this is a persistent error
      final currentState = state;
      final shouldClearTokens = isServerError ||
          (currentState.status == CartStatus.error &&
           currentState.error != null &&
           currentState.error!.contains('Backend server is not available'));

      if (shouldClearTokens) {
        final errorType = isServerError ? 'Server error' : 'Persistent connection error';
        debugPrint('🧹 [CartNotifier] $errorType - clearing authentication tokens...');
        try {
          await _ref.read(authNotifierProvider.notifier).signOut();
          debugPrint('✅ [CartNotifier] Authentication tokens cleared successfully');
        } catch (e) {
          debugPrint('❌ [CartNotifier] Failed to clear tokens: $e');
        }
      } else {
        debugPrint('🔄 [CartNotifier] First connection attempt failed - keeping tokens for retry');
      }

      // Set error state with user-friendly message
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend server is not available. Please try again later.',
        isAuthenticated: !shouldClearTokens, // Keep auth state if not clearing tokens
        cart: null,
      );
    } else {
      // Handle other types of failures normally
      if (failure is AuthFailure) {
        state = state.copyWith(
          status: CartStatus.error,
          error: failure.message,
          isAuthenticated: false,
        );
      } else {
        state = state.copyWith(
          status: CartStatus.error,
          error: failure.message,
        );
      }
    }
  }

  /// Get the current cart - always fetches fresh data from API
  Future<void> getCart({bool forceRefresh = true}) async {
    debugPrint('[CartNotifier] getCart() called with forceRefresh: $forceRefresh');

    if (_repository == null || _ref == null) {
      debugPrint('[CartNotifier] Repository or ref is null');
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      debugPrint('[CartNotifier] No backend authentication - this should not happen if auth was checked before navigation');
      debugPrint('[CartNotifier] Auth state: ${authState.toString()}');
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Always show loading state for fresh data fetch
    debugPrint('[CartNotifier] Fetching fresh cart data from API (forceRefresh: $forceRefresh)');
    state = state.copyWith(status: CartStatus.loading);

    // Verify token before making API call (this handles token refresh if needed)
    try {
      debugPrint('[CartNotifier] Verifying backend token...');
      final authNotifier = _ref.read(authNotifierProvider.notifier);
      final isTokenValid = await authNotifier.verifyBackendToken();

      if (!isTokenValid) {
        debugPrint('[CartNotifier] Token verification failed');
        state = state.copyWith(
          status: CartStatus.error,
          error: 'Backend authentication required',
          isAuthenticated: false,
        );
        return;
      }
      debugPrint('[CartNotifier] Token verification successful');
    } catch (e) {
      debugPrint('[CartNotifier] Token verification error: $e');
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    debugPrint('[CartNotifier] Making API call to get cart...');
    final result = await _repository.getCart();
    result.fold(
      (failure) {
        debugPrint('[CartNotifier] Cart API call failed: ${failure.message}');
        _handleConnectionError(failure);
      },
      (cart) {
        debugPrint('[CartNotifier] Fresh cart data loaded: ${cart.items.length} items');
        state = state.copyWith(
          status: CartStatus.loaded,
          cart: cart,
          clearError: true, // Clear any previous errors
          isAuthenticated: true,
        );
      },
    );
  }

  /// Refresh cart data - always fetches fresh data from API
  Future<void> refreshCart() async {
    debugPrint('[CartNotifier] Refreshing cart data...');
    await getCart(forceRefresh: true);
  }

  /// Add an item to the cart
  Future<void> addItem({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.addItem(
      variantId: variantId,
      productId: productId,
      quantity: quantity,
      price: price,
      title: title,
      currency: currency,
      imageUrl: imageUrl,
    );
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) => state = state.copyWith(
        status: CartStatus.loaded,
        cart: cart,
        clearError: true, // Clear any previous errors
        isAuthenticated: true,
      ),
    );
  }

  /// Update an item's quantity in the cart
  Future<void> updateItem({
    required String variantId,
    required int quantity,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Don't show loading state for quantity updates to avoid refresh feeling
    // state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.updateItem(
      variantId: variantId,
      quantity: quantity,
    );
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) => state = state.copyWith(
        status: CartStatus.loaded,
        cart: cart,
        clearError: true, // Clear any previous errors
        isAuthenticated: true,
      ),
    );
  }

  /// Update an item's complete information in the cart
  Future<void> updateItemComplete({
    required String oldVariantId,
    required String newVariantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.updateItemComplete(
      oldVariantId: oldVariantId,
      newVariantId: newVariantId,
      productId: productId,
      quantity: quantity,
      price: price,
      title: title,
      currency: currency,
      imageUrl: imageUrl,
    );
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) => state = state.copyWith(
        status: CartStatus.loaded,
        cart: cart,
        clearError: true, // Clear any previous errors
        isAuthenticated: true,
      ),
    );
  }

  /// Remove an item from the cart
  Future<void> removeItem({
    required String variantId,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.removeItem(
      variantId: variantId,
    );
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) => state = state.copyWith(
        status: CartStatus.loaded,
        cart: cart,
        clearError: true, // Clear any previous errors
        isAuthenticated: true,
      ),
    );
  }

  /// Proceed to checkout with all items
  Future<String?> checkout() async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.checkout();
    return result.fold(
      (failure) {
        _handleConnectionError(failure);
        return null;
      },
      (successMessage) async {
        // Checkout succeeded, refetch cart to get updated state
        await getCart();
        return successMessage;
      },
    );
  }

  /// Proceed to checkout with selected items only
  Future<String?> checkoutSelected({
    required List<String> selectedVariantIds,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.checkoutSelected(
      selectedVariantIds: selectedVariantIds,
    );
    return result.fold(
      (failure) {
        _handleConnectionError(failure);
        return null;
      },
      (successMessage) async {
        // Checkout succeeded, refetch cart to get updated state
        await getCart();
        return successMessage;
      },
    );
  }

  /// Proceed to checkout with specific items and quantities
  Future<String?> checkoutSelectedWithQuantities({
    required List<CheckoutItem> items,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.checkoutSelectedWithQuantities(
      items: items,
    );
    return result.fold(
      (failure) {
        _handleConnectionError(failure);
        return null;
      },
      (successMessage) async {
        // Checkout succeeded, refetch cart to get updated state
        await getCart();
        return successMessage;
      },
    );
  }

  /// Clear the entire cart
  Future<void> clearCart() async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.clearCart();
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) => state = state.copyWith(
        status: CartStatus.loaded,
        cart: cart,
        clearError: true, // Clear any previous errors
        isAuthenticated: true,
      ),
    );
  }

  /// Apply voucher to cart
  Future<void> applyVoucher(String voucherCode) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.applyVoucher(voucherCode: voucherCode);
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) {
        debugPrint('[CartNotifier] Voucher applied successfully: ${cart.effectiveDiscountCode}');
        debugPrint('[CartNotifier] Discount amount: ${cart.effectiveDiscountAmount}');
        debugPrint('[CartNotifier] Discount type: ${cart.hasShopifyDiscount ? 'Shopify' : 'Legacy'}');
        state = state.copyWith(
          status: CartStatus.loaded,
          cart: cart,
          clearError: true,
          isAuthenticated: true,
        );
      },
    );
  }

  /// Remove voucher from cart
  Future<void> removeVoucher() async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.removeVoucher();
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) {
        debugPrint('[CartNotifier] Voucher removed successfully');
        state = state.copyWith(
          status: CartStatus.loaded,
          cart: cart,
          clearError: true,
          isAuthenticated: true,
        );
      },
    );
  }

  /// Validate voucher code
  Future<VoucherValidation?> validateVoucher(String code, double orderAmount) async {
    if (_repository == null || _ref == null) {
      return null;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      return null;
    }

    final result = await _repository.validateVoucher(
      code: code,
      orderAmount: orderAmount,
    );

    return result.fold(
      (failure) {
        debugPrint('[CartNotifier] Voucher validation failed: ${failure.message}');
        return null;
      },
      (validation) {
        debugPrint('[CartNotifier] Voucher validation result: ${validation.isValid}');
        return validation;
      },
    );
  }
}