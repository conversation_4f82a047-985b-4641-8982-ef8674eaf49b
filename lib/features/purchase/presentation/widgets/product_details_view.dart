import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/models/product_model.dart';
import '../../../../l10n/app_localizations.dart';

class ProductDetailsView extends StatelessWidget {
  final Product product;
  final VoidCallback onHideDetails;

  const ProductDetailsView({
    super.key,
    required this.product,
    required this.onHideDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_hasDescription()) ...[
          Text(
            'Description',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 8),
          _buildHtmlDescription(),
          const SizedBox(height: 16),
        ],
        
        // Hide details button
        Align(
          alignment: Alignment.centerLeft,
          child: TextButton(
            onPressed: onHideDetails,
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              AppLocalizations.of(context).hideDetails,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFFFF6982),
                decoration: TextDecoration.underline,
                decorationColor: const Color(0xFFFF6982),
              ),
            ),
          ),
        ),
      ],
    );
  }

  bool _hasDescription() {
    return (product.descriptionHtml?.isNotEmpty ?? false) ||
           (product.description?.isNotEmpty ?? false);
  }

  Widget _buildHtmlDescription() {
    final html = product.descriptionHtml ?? '<p>${product.description ?? ''}</p>';

    if (html.isEmpty) return const SizedBox.shrink();



    // Check if the content contains a table
    if (html.contains('<table')) {
      return _buildTableFromHtml(html);
    } else {
      // For non-table content, display as simple text
      final textContent = html
          .replaceAll(RegExp(r'<[^>]*>'), ' ')
          .replaceAll(RegExp(r'\s+'), ' ')
          .trim();

      return Text(
        textContent,
        style: GoogleFonts.inter(
          fontSize: 14,
          color: const Color(0xFF374151),
          height: 1.5,
        ),
      );
    }
  }

  Widget _buildTableFromHtml(String html) {
    // Parse the HTML table and extract rows
    final rows = _parseTableRows(html);

    if (rows.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
      ),
      child: Column(
        children: rows.asMap().entries.map((entry) {
          final rowIndex = entry.key;
          final row = entry.value;
          final isLastRow = rowIndex == rows.length - 1;

          return Container(
            decoration: BoxDecoration(
              border: Border(
                bottom: isLastRow
                  ? BorderSide.none
                  : const BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
            ),
            child: IntrinsicHeight(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: row.asMap().entries.map((cellEntry) {
                  final cellIndex = cellEntry.key;
                  final cell = cellEntry.value;
                  final isFirstCell = cellIndex == 0;
                  final isLastCell = cellIndex == row.length - 1;

                  return Expanded(
                    flex: cellIndex == 0 ? 1 : 2, // First column smaller, second column larger
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border(
                          right: isLastCell
                            ? BorderSide.none
                            : const BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                        color: isFirstCell
                          ? const Color(0xFFF9FAFB) // Light background for first column (labels)
                          : Colors.white,
                      ),
                      child: _buildCellContent(cell),
                    ),
                  );
                }).toList(),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  List<List<String>> _parseTableRows(String html) {
    final rows = <List<String>>[];

    // Extract table rows using regex
    final rowRegex = RegExp(r'<tr[^>]*>(.*?)</tr>', dotAll: true);
    final rowMatches = rowRegex.allMatches(html);

    for (final rowMatch in rowMatches) {
      final rowHtml = rowMatch.group(1) ?? '';
      final cells = <String>[];

      // Extract table cells using regex
      final cellRegex = RegExp(r'<td[^>]*>(.*?)</td>', dotAll: true);
      final cellMatches = cellRegex.allMatches(rowHtml);

      for (final cellMatch in cellMatches) {
        final cellContent = cellMatch.group(1) ?? '';
        cells.add(cellContent);
      }

      if (cells.isNotEmpty) {
        rows.add(cells);
      }
    }

    return rows;
  }

  Widget _buildCellContent(String cellHtml) {
    // Remove HTML tags but preserve strong tags for bold text
    String content = cellHtml;

    // Check if content has strong tags
    final strongRegex = RegExp(r'<strong>(.*?)</strong>', dotAll: true);
    final strongMatches = strongRegex.allMatches(content);

    if (strongMatches.isNotEmpty) {
      // Build rich text with bold parts
      final spans = <TextSpan>[];
      int lastEnd = 0;

      for (final match in strongMatches) {
        // Add text before the strong tag
        if (match.start > lastEnd) {
          final beforeText = content.substring(lastEnd, match.start)
              .replaceAll(RegExp(r'<[^>]*>'), '')
              .trim();
          if (beforeText.isNotEmpty) {
            spans.add(TextSpan(text: beforeText));
          }
        }

        // Add the strong text
        final strongText = match.group(1) ?? '';
        final cleanStrongText = strongText
            .replaceAll(RegExp(r'<[^>]*>'), '')
            .trim();
        if (cleanStrongText.isNotEmpty) {
          spans.add(TextSpan(
            text: cleanStrongText,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ));
        }

        lastEnd = match.end;
      }

      // Add remaining text after the last strong tag
      if (lastEnd < content.length) {
        final afterText = content.substring(lastEnd)
            .replaceAll(RegExp(r'<[^>]*>'), '')
            .trim();
        if (afterText.isNotEmpty) {
          spans.add(TextSpan(text: afterText));
        }
      }

      return RichText(
        text: TextSpan(
          style: GoogleFonts.inter(
            fontSize: 13,
            color: const Color(0xFF374151),
            height: 1.4,
          ),
          children: spans,
        ),
      );
    } else {
      // Simple text without bold parts
      final cleanText = content
          .replaceAll(RegExp(r'<[^>]*>'), '')
          .trim();

      return Text(
        cleanText,
        style: GoogleFonts.inter(
          fontSize: 13,
          color: const Color(0xFF374151),
          height: 1.4,
        ),
      );
    }
  }
}
