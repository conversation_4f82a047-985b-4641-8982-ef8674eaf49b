import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/payment_result.dart';

part 'payment_result_model.g.dart';

/// Data model for payment result from backend API
@JsonSerializable()
class PaymentResultModel {
  final String id;
  final String status;
  @Json<PERSON>ey(name: 'payment_intent_id')
  final String? paymentIntentId;
  @Json<PERSON>ey(name: 'payment_method_id')
  final String? paymentMethodId;
  final int? amount;
  final String? currency;
  @Json<PERSON>ey(name: 'receipt_url')
  final String? receiptUrl;
  @J<PERSON><PERSON>ey(name: 'failure_reason')
  final String? failureReason;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'error_message')
  final String? errorMessage;
  final Map<String, dynamic>? metadata;
  @Json<PERSON>ey(name: 'created_at')
  final String createdAt;

  const PaymentResultModel({
    required this.id,
    required this.status,
    this.paymentIntentId,
    this.paymentMethodId,
    this.amount,
    this.currency,
    this.receiptUrl,
    this.failureReason,
    this.errorMessage,
    this.metadata,
    required this.createdAt,
  });

  factory PaymentResultModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentResultModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentResultModelToJson(this);

  /// Convert to domain entity
  PaymentResult toDomain() {
    return PaymentResult(
      id: id,
      status: _parsePaymentStatus(status),
      paymentIntentId: paymentIntentId,
      paymentMethodId: paymentMethodId,
      amount: amount,
      currency: currency,
      receiptUrl: receiptUrl,
      failureReason: failureReason,
      errorMessage: errorMessage,
      metadata: metadata,
      createdAt: DateTime.parse(createdAt),
    );
  }

  /// Parse payment status string to enum
  PaymentStatus _parsePaymentStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return PaymentStatus.pending;
      case 'processing':
        return PaymentStatus.processing;
      case 'succeeded':
        return PaymentStatus.succeeded;
      case 'failed':
        return PaymentStatus.failed;
      case 'canceled':
        return PaymentStatus.canceled;
      case 'requires_action':
        return PaymentStatus.requiresAction;
      case 'requires_payment_method':
        return PaymentStatus.requiresPaymentMethod;
      default:
        return PaymentStatus.pending;
    }
  }

  /// Create from domain entity
  factory PaymentResultModel.fromDomain(PaymentResult result) {
    return PaymentResultModel(
      id: result.id,
      status: result.status.name,
      paymentIntentId: result.paymentIntentId,
      paymentMethodId: result.paymentMethodId,
      amount: result.amount,
      currency: result.currency,
      receiptUrl: result.receiptUrl,
      failureReason: result.failureReason,
      errorMessage: result.errorMessage,
      metadata: result.metadata,
      createdAt: result.createdAt.toIso8601String(),
    );
  }
}
