# API Integration Guide

## Overview

This guide provides comprehensive instructions for integrating with TravelGator's backend APIs, including authentication, payment processing, and testing procedures.

## Authentication

### Backend Authentication Flow

TravelGator uses a composite authentication system combining Firebase and backend authentication:

1. **Firebase Authentication**: Initial user authentication
2. **Backend Token Exchange**: Convert Firebase ID token to backend token
3. **API Authorization**: Use backend token for all API calls

### Implementation

```dart
// 1. Firebase Authentication
final userCredential = await FirebaseAuth.instance.signInWithGoogle();
final idToken = await userCredential.user?.getIdToken();

// 2. Backend Token Exchange
final response = await dio.post('/auth/firebase', data: {
  'firebase_token': idToken,
});

final backendToken = response.data['token'];

// 3. API Authorization
final apiResponse = await dio.get('/api/cart', options: Options(
  headers: {'Authorization': 'Bearer $backendToken'},
));
```

### Token Management

```dart
class AuthTokenManager {
  String? _backendToken;
  DateTime? _tokenExpiry;

  bool get isTokenValid =>
    _backendToken != null &&
    _tokenExpiry != null &&
    DateTime.now().isBefore(_tokenExpiry!);

  Future<String?> getValidToken() async {
    if (!isTokenValid) {
      await refreshToken();
    }
    return _backendToken;
  }

  Future<void> refreshToken() async {
    final firebaseUser = FirebaseAuth.instance.currentUser;
    if (firebaseUser == null) throw AuthException('No Firebase user');

    final idToken = await firebaseUser.getIdToken(true);
    // Exchange with backend...
  }
}
```

## API Endpoints

### Base Configuration

```dart
class ApiConfig {
  static const String baseUrl = 'https://api.travelgator.com';
  static const Duration timeout = Duration(seconds: 30);

  static Dio createDioClient() {
    final dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: timeout,
      receiveTimeout: timeout,
      sendTimeout: timeout,
    ));

    // Add interceptors
    dio.interceptors.add(AuthInterceptor());
    dio.interceptors.add(LoggingInterceptor());

    return dio;
  }
}
```

### Cart Management

#### Get Cart
```http
GET /api/cart
Authorization: Bearer <backend_token>
```

**Response**:
```json
{
  "id": "cart_123",
  "items": [
    {
      "variant_id": "gid://shopify/ProductVariant/123",
      "product_id": "gid://shopify/Product/456",
      "quantity": 1,
      "price": 29.99,
      "title": "eSIM Product",
      "image_url": "https://example.com/image.jpg"
    }
  ],
  "total_price": 29.99,
  "currency": "USD",
  "items_count": 1
}
```

#### Add Item to Cart
```http
POST /api/cart/add_item
Authorization: Bearer <backend_token>
Content-Type: application/json

{
  "variant_id": "gid://shopify/ProductVariant/123",
  "quantity": 1
}
```

#### Update Cart Item
```http
PATCH /api/cart/update_item
Authorization: Bearer <backend_token>
Content-Type: application/json

{
  "variant_id": "gid://shopify/ProductVariant/123",
  "quantity": 2
}
```

#### Remove Cart Item
```http
DELETE /api/cart/remove_item
Authorization: Bearer <backend_token>
Content-Type: application/json

{
  "variant_id": "gid://shopify/ProductVariant/123"
}
```

### Checkout & Payment

#### Unified Cart Checkout
```http
POST /api/cart/checkout
Authorization: Bearer <backend_token>
Content-Type: application/json

# Checkout all items
{}

# Checkout selected variants
{
  "selected_variant_ids": ["variant_1", "variant_2"]
}

# Checkout with custom quantities
{
  "items": [
    {"variant_id": "variant_1", "quantity": 1},
    {"variant_id": "variant_2", "quantity": 2}
  ]
}
```

**Response (Payment Intent)**:
```json
{
  "client_secret": "pi_1234567890_secret_abcdef",
  "payment_intent_id": "pi_1234567890",
  "order_id": "order_abc123",
  "amount": 59.98,
  "currency": "USD"
}
```

**Response (Checkout Session)**:
```json
{
  "checkout_url": "https://checkout.stripe.com/pay/cs_test_...",
  "order_id": "order_abc123",
  "stripe_session_id": "cs_test_...",
  "total_amount": 59.98,
  "currency": "USD",
  "expires_at": "2024-01-01T12:00:00Z"
}
```

#### Buy Now
```http
POST /api/buy_now
Authorization: Bearer <backend_token>
Content-Type: application/json

{
  "variant_id": "gid://shopify/ProductVariant/123",
  "quantity": 1,
  "product_id": "gid://shopify/Product/456", // optional
  "price": 29.99, // optional
  "title": "eSIM Product" // optional
}
```

#### Payment Status
```http
GET /api/cart/payment_status?payment_intent_id=pi_1234567890
Authorization: Bearer <backend_token>
```

**Response**:
```json
{
  "status": "succeeded",
  "order_id": "order_abc123",
  "amount_received": 2999,
  "charges": [
    {
      "id": "ch_1234567890",
      "amount": 2999,
      "currency": "usd",
      "status": "succeeded"
    }
  ]
}
```

### Stripe Configuration

#### Get Stripe Config
```http
GET /api/stripe/config
Authorization: Bearer <backend_token>
```

**Response**:
```json
{
  "publishable_key": "pk_test_...",
  "merchant_id": "merchant_123",
  "merchant_display_name": "TravelGator",
  "country_code": "US",
  "currency": "USD",
  "test_mode": true
}
```

## Stripe Integration

### Setup

1. **Install Stripe SDK**
   ```yaml
   dependencies:
     flutter_stripe: ^10.1.1
   ```

2. **Initialize Stripe**
   ```dart
   void main() async {
     WidgetsFlutterBinding.ensureInitialized();

     // Initialize Stripe
     Stripe.publishableKey = 'pk_test_...';
     Stripe.merchantIdentifier = 'merchant.com.travelgator';

     runApp(MyApp());
   }
   ```

3. **Configure Payment Sheet**
   ```dart
   await Stripe.instance.initPaymentSheet(
     paymentSheetParameters: SetupPaymentSheetParameters(
       paymentIntentClientSecret: clientSecret,
       merchantDisplayName: 'TravelGator',
       style: ThemeMode.system,
     ),
   );
   ```

### Payment Intent Flow

```dart
class PaymentService {
  Future<PaymentResult> processPayment({
    required String clientSecret,
    required String paymentIntentId,
  }) async {
    try {
      // Confirm payment with Stripe
      final paymentIntent = await Stripe.instance.confirmPayment(
        paymentIntentClientSecret: clientSecret,
        data: const PaymentMethodParams.card(
          paymentMethodData: PaymentMethodData(),
        ),
      );

      // Verify with backend
      final result = await _verifyPaymentWithBackend(paymentIntentId);

      return PaymentResult.success(result);
    } on StripeException catch (e) {
      return PaymentResult.failure(e.error.message);
    }
  }

  Future<PaymentStatus> _verifyPaymentWithBackend(String paymentIntentId) async {
    final response = await dio.get(
      '/api/cart/payment_status',
      queryParameters: {'payment_intent_id': paymentIntentId},
    );

    return PaymentStatus.fromJson(response.data);
  }
}
```

### Error Handling

```dart
class StripeErrorHandler {
  static String getLocalizedMessage(StripeException error, AppLocalizations l10n) {
    switch (error.error.code) {
      case FailureCode.Canceled:
        return l10n.paymentCanceled;
      case FailureCode.Failed:
        return error.error.localizedMessage ?? l10n.paymentFailed;
      case FailureCode.InvalidRequestError:
        return l10n.paymentErrorInvalidRequest;
      default:
        return error.error.localizedMessage ?? l10n.paymentErrorUnknown;
    }
  }
}
```

## Testing

### Unit Testing

```dart
// Test API client
class MockDio extends Mock implements Dio {}

void main() {
  group('CartRepository', () {
    late MockDio mockDio;
    late CartRepository repository;

    setUp(() {
      mockDio = MockDio();
      repository = CartRepositoryImpl(dio: mockDio);
    });

    test('should get cart successfully', () async {
      // Arrange
      when(mockDio.get('/api/cart')).thenAnswer(
        (_) async => Response(
          data: {'id': 'cart_123', 'items': []},
          statusCode: 200,
          requestOptions: RequestOptions(path: '/api/cart'),
        ),
      );

      // Act
      final result = await repository.getCart();

      // Assert
      expect(result.isRight(), true);
      verify(mockDio.get('/api/cart')).called(1);
    });
  });
}
```

### Integration Testing

```dart
void main() {
  group('Payment Integration Tests', () {
    testWidgets('should complete payment flow', (tester) async {
      // Setup test environment
      await tester.pumpWidget(TestApp());

      // Navigate to checkout
      await tester.tap(find.text('Checkout'));
      await tester.pumpAndSettle();

      // Enter payment details
      await tester.enterText(find.byType(CardFormField), '****************');

      // Submit payment
      await tester.tap(find.text('Pay Now'));
      await tester.pumpAndSettle();

      // Verify success
      expect(find.text('Payment Successful'), findsOneWidget);
    });
  });
}
```

### API Testing with Postman

```json
{
  "info": {
    "name": "TravelGator API Tests",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "Authentication",
      "item": [
        {
          "name": "Firebase Token Exchange",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"firebase_token\": \"{{firebase_token}}\"\n}"
            },
            "url": {
              "raw": "{{base_url}}/auth/firebase",
              "host": ["{{base_url}}"],
              "path": ["auth", "firebase"]
            }
          }
        }
      ]
    },
    {
      "name": "Cart Operations",
      "item": [
        {
          "name": "Get Cart",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{backend_token}}"
              }
            ],
            "url": {
              "raw": "{{base_url}}/api/cart",
              "host": ["{{base_url}}"],
              "path": ["api", "cart"]
            }
          }
        }
      ]
    }
  ]
}
```

## Environment Configuration

### Development Environment

```env
# API Configuration
BACKEND_API_URL=https://dev-api.travelgator.com
API_TIMEOUT_SECONDS=30

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_51234567890abcdef
STRIPE_TEST_MODE=true

# Feature Flags
ENABLE_DEBUG_LOGGING=true
ENABLE_NETWORK_LOGGING=true
```

### Production Environment

```env
# API Configuration
BACKEND_API_URL=https://api.travelgator.com
API_TIMEOUT_SECONDS=15

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_live_51234567890abcdef
STRIPE_TEST_MODE=false

# Feature Flags
ENABLE_DEBUG_LOGGING=false
ENABLE_NETWORK_LOGGING=false
```

## Error Codes & Troubleshooting

### Common Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| `AUTH_001` | Invalid backend token | Refresh authentication token |
| `CART_001` | Cart not found | Create new cart or refresh user session |
| `PAYMENT_001` | Payment intent creation failed | Check Stripe configuration |
| `PAYMENT_002` | Payment confirmation failed | Verify card details and try again |
| `NETWORK_001` | Connection timeout | Check network connectivity |

### Debugging Steps

1. **Check Authentication**
   ```dart
   final token = await AuthTokenManager.instance.getValidToken();
   debugPrint('Token valid: ${token != null}');
   ```

2. **Verify API Connectivity**
   ```dart
   try {
     final response = await dio.get('/health');
     debugPrint('API Status: ${response.statusCode}');
   } catch (e) {
     debugPrint('API Error: $e');
   }
   ```

3. **Test Stripe Configuration**
   ```dart
   try {
     final config = await StripeRepository.getConfig();
     debugPrint('Stripe Key: ${config.publishableKey.substring(0, 10)}...');
   } catch (e) {
     debugPrint('Stripe Config Error: $e');
   }
   ```

## Best Practices

### Security
1. **Never log sensitive data** (tokens, card numbers)
2. **Use HTTPS** for all API communications
3. **Validate tokens** on every request
4. **Implement proper timeout** handling

### Performance
1. **Cache API responses** when appropriate
2. **Use connection pooling** for HTTP clients
3. **Implement retry logic** for network failures
4. **Optimize payload sizes** for mobile networks

### Error Handling
1. **Provide meaningful error messages** to users
2. **Log errors with context** for debugging
3. **Implement graceful degradation** for non-critical failures
4. **Use localized error messages** for better UX

### Testing
1. **Test with real Stripe test cards**
2. **Verify error scenarios** thoroughly
3. **Test network failure conditions**
4. **Validate API contract compliance**