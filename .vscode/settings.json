{"dart.flutterSdkPath": ".fvm/versions/3.29.2", "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 500, "dart.flutterHotReloadOnSave": "all", "dart.hotReloadOnSave": "all", "dart.flutterSelectDeviceWhenConnected": false, "dart.defaultDevice": "00008140-00186D620286801C", "http.noProxy": ["default.exp-tas.com", "api.github.com", "update.code.visualstudio.com", "education.github.com", "marketplace.visualstudio.com", "main.vscode-cdn.net", "mobile.events.data.microsoft.com", "www.vscode-unpkg.net", "api.segment.io", "o4509262619082752.ingest.us.sentry.io", "api2.cursor.sh", "api3.cursor.sh", "shopify.dev", "cdnjs.cloudflare.com", "marketplace.cursorapi.com", "configs.gitkraken.dev", "otel.gitkraken.com", "repo42.cursor.sh", "pub.dev", "www.google-analytics.com", "www.schemastore.org", "json.schemastore.org", "circle-git.com", "avatars.githubusercontent.com", "www.gravatar.com", "img.shields.io", "user-images.githubusercontent.com", "secure.gravatar.com", "raw.githubusercontent.com", "github.gallerycdn.vsassets.io", "github.gallery.vsassets.io", "ms-azuretools.gallerycdn.vsassets.io", "ms-azuretools.gallery.vsassets.io", "tamasfe.gallerycdn.vsassets.io", "ms-vscode-remote.gallerycdn.vsassets.io", "dart-code.gallerycdn.vsassets.io", "tamasfe.gallery.vsassets.io", "dart-code.gallery.vsassets.io", "ms-vscode-remote.gallery.vsassets.io", "fill-labs.gallerycdn.azure.cn", "fill-labs.gallery.vsassets.io", "ms-edgedevtools.gallerycdn.vsassets.io", "dbaeumer.gallerycdn.vsassets.io", "ms-edgedevtools.gallery.vsassets.io", "dbaeumer.gallery.vsassets.io", "nicolasvuillamy.gallerycdn.vsassets.io", "febean.gallerycdn.vsassets.io", "rifi2k.gallerycdn.vsassets.io", "xaver.gallerycdn.vsassets.io", "foxundermoon.gallerycdn.vsassets.io", "mohd-akram.gallerycdn.vsassets.io", "cheshirekow.gallerycdn.vsassets.io", "mikeburgh.gallerycdn.vsassets.io", "ms-python.gallerycdn.vsassets.io", "rvest.gallerycdn.vsassets.io", "awwsky.gallerycdn.vsassets.io", "keroc.gallerycdn.vsassets.io", "lacroixdavid1.gallerycdn.vsassets.io", "rifi2k.gallery.vsassets.io", "febean.gallery.vsassets.io", "esbenp.gallerycdn.azure.cn", "nicolasvuillamy.gallery.vsassets.io", "foxundermoon.gallery.vsassets.io", "xaver.gallery.vsassets.io", "ms-python.gallery.vsassets.io", "keroc.gallery.vsassets.io", "mohd-akram.gallery.vsassets.io", "lacroixdavid1.gallery.vsassets.io", "cheshirekow.gallery.vsassets.io", "mikeburgh.gallery.vsassets.io", "rvest.gallery.vsassets.io", "awwsky.gallery.vsassets.io", "esbenp.gallery.vsassets.io", "esbenp.gallerycdn.vsassets.io", "docs.flutter.dev", "github.gallerycdn.azure.cn", "metrics.cursor.sh"]}