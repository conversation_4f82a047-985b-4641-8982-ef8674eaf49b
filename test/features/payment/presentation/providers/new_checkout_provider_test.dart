import 'package:flutter_test/flutter_test.dart';

import 'package:flutter_travelgator/features/cart/domain/entities/cart.dart';

void main() {
  group('NewCheckoutProvider Cart Items', () {
    test('should convert cart items to backend format correctly', () {
      // Create test cart with items
      final cart = Cart(
        id: 'test-cart-1',
        items: [
          CartItem(
            variantId: 'variant-123',
            productId: 'product-456',
            quantity: 2,
            price: 29.99,
            title: 'Test eSIM Product',
            imageUrl: 'https://example.com/image.jpg',
          ),
          CartItem(
            variantId: 'variant-789',
            productId: 'product-101',
            quantity: 1,
            price: 49.99,
            title: 'Another eSIM Product',
            imageUrl: 'https://example.com/image2.jpg',
          ),
        ],
        totalPrice: 109.97,
        currency: 'USD',
        itemsCount: 3,
      );

      // Test the cart item conversion logic
      final cartItems = cart.items.map((item) => {
        'variant_id': item.variantId,
        'product_id': item.productId,
        'quantity': item.quantity,
        'price': item.price,
        'title': item.title,
        'currency': cart.currency ?? 'USD',
        'image_url': item.imageUrl,
      }).toList();

      // Verify the conversion
      expect(cartItems.length, equals(2));
      
      // Check first item
      expect(cartItems[0]['variant_id'], equals('variant-123'));
      expect(cartItems[0]['product_id'], equals('product-456'));
      expect(cartItems[0]['quantity'], equals(2));
      expect(cartItems[0]['price'], equals(29.99));
      expect(cartItems[0]['title'], equals('Test eSIM Product'));
      expect(cartItems[0]['currency'], equals('USD'));
      expect(cartItems[0]['image_url'], equals('https://example.com/image.jpg'));

      // Check second item
      expect(cartItems[1]['variant_id'], equals('variant-789'));
      expect(cartItems[1]['product_id'], equals('product-101'));
      expect(cartItems[1]['quantity'], equals(1));
      expect(cartItems[1]['price'], equals(49.99));
      expect(cartItems[1]['title'], equals('Another eSIM Product'));
      expect(cartItems[1]['currency'], equals('USD'));
      expect(cartItems[1]['image_url'], equals('https://example.com/image2.jpg'));
    });

    test('should handle cart with no currency specified', () {
      final cart = Cart(
        id: 'test-cart-2',
        items: [
          CartItem(
            variantId: 'variant-999',
            productId: 'product-888',
            quantity: 1,
            price: 19.99,
            title: 'Test Product',
          ),
        ],
        totalPrice: 19.99,
        itemsCount: 1,
      );

      final cartItems = cart.items.map((item) => {
        'variant_id': item.variantId,
        'product_id': item.productId,
        'quantity': item.quantity,
        'price': item.price,
        'title': item.title,
        'currency': cart.currency ?? 'USD',
        'image_url': item.imageUrl,
      }).toList();

      expect(cartItems[0]['currency'], equals('USD')); // Should default to USD
      expect(cartItems[0]['image_url'], isNull); // Should handle null image URL
    });

    test('should handle empty cart', () {
      final cart = Cart(
        id: 'empty-cart',
        items: [],
        totalPrice: 0.0,
        itemsCount: 0,
      );

      final cartItems = cart.items.map((item) => {
        'variant_id': item.variantId,
        'product_id': item.productId,
        'quantity': item.quantity,
        'price': item.price,
        'title': item.title,
        'currency': cart.currency ?? 'USD',
        'image_url': item.imageUrl,
      }).toList();

      expect(cartItems.length, equals(0));
    });
  });
}
