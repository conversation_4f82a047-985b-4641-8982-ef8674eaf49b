import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../cart/domain/entities/cart.dart';
import 'discount_code_widget.dart';
import 'wallet_credits_widget.dart';

/// Bottom section widget for checkout screen matching Figma design
class CheckoutBottomSection extends StatelessWidget {
  final Cart cart;
  final bool isLoading;
  final VoidCallback? onPayPressed;

  const CheckoutBottomSection({
    super.key,
    required this.cart,
    this.isLoading = false,
    this.onPayPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Color(0xFFE5E7EB), // Figma border color
            width: 1,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Discount code section
          DiscountCodeWidget(
            onDiscountApplied: (discount) {
              // Handle discount application
              debugPrint('Discount applied: $discount');
            },
            currentDiscount: 'PROMO2',
          ),

          // Spend credits section
          WalletCreditsWidget(
            availableCredits: 0.00, // TODO: Get from user profile/wallet
            onCreditsApplied: (amount) {
              // Handle credits application
              debugPrint('Credits applied: \$${amount.toStringAsFixed(2)}');
            },
            currentCreditsUsed: 0.00, // No credits used by default
          ),

          // Main bottom section with total and pay button
          _buildMainBottomSection(),
        ],
      ),
    );
  }





  Widget _buildMainBottomSection() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
      child: Column(
        children: [
          // Grand total section
          Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 112,
                          height: 39,
                          alignment: Alignment.centerRight,
                          child: Text(
                            'Grand Total:',
                            style: GoogleFonts.inter(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF1F2937),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Divider line
              Container(
                width: 1,
                height: 23,
                color: const Color(0xFFE5E7EB),
              ),
              
              // Pricing breakdown
              Expanded(
                child: Container(
                  height: 46,
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '\$${cart.totalPrice.toStringAsFixed(2)}\nCredits (-\$13.00)\nPROMO2 (-\$2.00)\n\$ ${(cart.totalPrice - 15.00).toStringAsFixed(2)}',
                        textAlign: TextAlign.right,
                        style: GoogleFonts.inter(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1F2937),
                          height: 1.21,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Pay now button
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: isLoading ? null : onPayPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF6982), // Figma primary color
                foregroundColor: Colors.white,
                elevation: 4,
                shadowColor: Colors.black.withValues(alpha: 0.25),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      'Pay now',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 0.5,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
