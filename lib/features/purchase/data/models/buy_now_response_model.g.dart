// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'buy_now_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BuyNowResponseModel _$BuyNowResponseModelFromJson(Map<String, dynamic> json) =>
    BuyNowResponseModel(
      data: BuyNowDataModel.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BuyNowResponseModelToJson(
  BuyNowResponseModel instance,
) => <String, dynamic>{'data': instance.data};

BuyNowDataModel _$BuyNowDataModelFromJson(Map<String, dynamic> json) =>
    BuyNowDataModel(
      orderId: json['order_id'] as String,
      currency: json['currency'] as String,
      clientSecret: json['client_secret'] as String?,
      paymentIntentId: json['payment_intent_id'] as String?,
      amount: _parseDouble(json['amount']),
      success: json['success'] as bool?,
      checkoutUrl: json['checkout_url'] as String?,
      shopifyCartId: json['shopify_cart_id'] as String?,
      totalPrice: _parseDouble(json['total_price']),
      authenticated: json['authenticated'] as bool?,
      localOrderCreated: json['local_order_created'] as bool?,
    );

Map<String, dynamic> _$BuyNowDataModelToJson(BuyNowDataModel instance) =>
    <String, dynamic>{
      'order_id': instance.orderId,
      'currency': instance.currency,
      'client_secret': instance.clientSecret,
      'payment_intent_id': instance.paymentIntentId,
      'amount': instance.amount,
      'success': instance.success,
      'checkout_url': instance.checkoutUrl,
      'shopify_cart_id': instance.shopifyCartId,
      'total_price': instance.totalPrice,
      'authenticated': instance.authenticated,
      'local_order_created': instance.localOrderCreated,
    };
