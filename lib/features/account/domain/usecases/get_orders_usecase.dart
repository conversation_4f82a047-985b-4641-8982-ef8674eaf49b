import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/order.dart' as entities;
import '../repositories/order_repository.dart';

/// Use case for getting user's order history
class GetOrdersUseCase implements UseCase<List<entities.Order>, NoParams> {
  final OrderRepository repository;

  GetOrdersUseCase(this.repository);

  @override
  Future<Either<Failure, List<entities.Order>>> call(NoParams params) async {
    return await repository.getOrders();
  }
}

/// Use case for getting a specific order by ID
class GetOrderByIdUseCase implements UseCase<entities.Order, GetOrderByIdParams> {
  final OrderRepository repository;

  GetOrderByIdUseCase(this.repository);

  @override
  Future<Either<Failure, entities.Order>> call(GetOrderByIdParams params) async {
    return await repository.getOrderById(params.orderId);
  }
}

/// Parameters for getting order by ID
class GetOrderByIdParams {
  final String orderId;

  GetOrderByIdParams({required this.orderId});
}
