import 'package:json_annotation/json_annotation.dart';

part 'client_models.g.dart';

/// Cart item update request model
@JsonSerializable()
class CartItemUpdate {
  @JsonKey(name: 'variant_id')
  final String variantId;
  
  @JsonKey(name: 'product_id')
  final String? productId;
  
  final int quantity;
  final bool? selected;

  const CartItemUpdate({
    required this.variantId,
    this.productId,
    required this.quantity,
    this.selected,
  });

  factory CartItemUpdate.fromJson(Map<String, dynamic> json) =>
      _$CartItemUpdateFromJson(json);

  Map<String, dynamic> toJson() => _$CartItemUpdateToJson(this);
}

/// Cart update request model for PUT /api/cart
@JsonSerializable()
class CartUpdateRequest {
  final List<CartItemUpdate>? items;

  @JsonKey(name: 'preferred_payment_method')
  final String? preferredPaymentMethod;

  const CartUpdateRequest({
    this.items,
    this.preferredPaymentMethod,
  });

  factory CartUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$CartUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CartUpdateRequestToJson(this);
}

/// Enhanced cart item response model
@JsonSerializable()
class CartItemResponse {
  @JsonKey(name: 'variant_id')
  final String variantId;
  
  @JsonKey(name: 'product_id')
  final String productId;
  
  final String title;
  final String description;
  final double price;
  final int quantity;
  
  @JsonKey(name: 'total_price')
  final double totalPrice;
  
  final bool selected;

  const CartItemResponse({
    required this.variantId,
    required this.productId,
    required this.title,
    required this.description,
    required this.price,
    required this.quantity,
    required this.totalPrice,
    required this.selected,
  });

  factory CartItemResponse.fromJson(Map<String, dynamic> json) =>
      _$CartItemResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CartItemResponseToJson(this);
}

/// Enhanced cart response model
@JsonSerializable()
class CartResponse {
  final String id;
  
  @JsonKey(name: 'user_id')
  final String userId;
  
  final List<CartItemResponse> items;
  
  @JsonKey(name: 'items_count')
  final int itemsCount;
  
  final double subtotal;
  final String currency;
  
  @JsonKey(name: 'has_discount')
  final bool hasDiscount;
  
  @JsonKey(name: 'voucher_code')
  final String? voucherCode;
  
  @JsonKey(name: 'discount_amount')
  final double discountAmount;
  
  @JsonKey(name: 'total_price')
  final double totalPrice;
  
  @JsonKey(name: 'preferred_payment_method')
  final String? preferredPaymentMethod;
  
  @JsonKey(name: 'checkout_ready')
  final bool checkoutReady;
  
  @JsonKey(name: 'created_at')
  final String createdAt;
  
  @JsonKey(name: 'updated_at')
  final String updatedAt;

  const CartResponse({
    required this.id,
    required this.userId,
    required this.items,
    required this.itemsCount,
    required this.subtotal,
    required this.currency,
    required this.hasDiscount,
    this.voucherCode,
    required this.discountAmount,
    required this.totalPrice,
    this.preferredPaymentMethod,
    required this.checkoutReady,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CartResponse.fromJson(Map<String, dynamic> json) =>
      _$CartResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CartResponseToJson(this);
}

/// API response wrapper
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;
  
  @JsonKey(name: 'error_code')
  final String? errorCode;
  
  final Map<String, dynamic>? details;

  const ApiResponse({
    required this.success,
    this.data,
    this.error,
    this.errorCode,
    this.details,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);
}

/// Client error types
enum ClientErrorType {
  network,
  authentication,
  validation,
  server,
  unknown,
}

/// Client exception
class ClientException implements Exception {
  final String message;
  final ClientErrorType type;
  final String? errorCode;
  final int? statusCode;
  final Map<String, dynamic>? details;
  final dynamic originalError;

  const ClientException({
    required this.message,
    required this.type,
    this.errorCode,
    this.statusCode,
    this.details,
    this.originalError,
  });

  @override
  String toString() => 'ClientException: $message';
}

/// Client configuration
class ClientConfig {
  final String baseUrl;
  final Duration connectTimeout;
  final Duration receiveTimeout;
  final Duration sendTimeout;
  final Duration debounceDelay;
  final int maxRetries;
  final bool enableLogging;

  const ClientConfig({
    required this.baseUrl,
    this.connectTimeout = const Duration(seconds: 5),
    this.receiveTimeout = const Duration(seconds: 10),
    this.sendTimeout = const Duration(seconds: 10),
    this.debounceDelay = const Duration(milliseconds: 300),
    this.maxRetries = 3,
    this.enableLogging = true,
  });
}

/// Authentication token provider interface
abstract class TokenProvider {
  Future<String?> getToken();
  Future<void> refreshToken();
  Future<bool> isTokenValid();
}
