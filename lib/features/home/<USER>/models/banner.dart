class Banner {
  final String id;
  final String handle;
  final String imageUrl;
  final String? width;  // Changed to String to support percentage values
  final String? height; // Changed to String to support percentage values
  final String? linkUrl;

  Banner({
    required this.id,
    required this.handle,
    required this.imageUrl,
    this.width,
    this.height,
    this.linkUrl,
  });

  factory Banner.fromGraphQL(Map<String, dynamic> node) {
    return Banner(
      id: node['id'] ?? '',
      handle: node['handle'] ?? '',
      imageUrl: node['imageUrl']?['value'] ?? '',
      width: node['width']?['value'], // Keep as string to support "100%" format
      height: node['height']?['value'], // Keep as string to support "200px" or "50%" format
      linkUrl: node['linkUrl']?['value'],
    );
  }

  /// Parse width value - supports both percentage and pixel values
  double? getWidthValue(double screenWidth) {
    if (width == null || width!.isEmpty) return null;

    if (width!.endsWith('%')) {
      final percentage = double.tryParse(width!.replaceAll('%', ''));
      return percentage != null ? (screenWidth * percentage / 100) : null;
    } else if (width!.endsWith('px')) {
      return double.tryParse(width!.replaceAll('px', ''));
    } else {
      // Assume it's a pixel value if no unit specified
      return double.tryParse(width!);
    }
  }

  /// Parse height value - supports both percentage and pixel values
  double? getHeightValue(double screenWidth) {
    if (height == null || height!.isEmpty) return null;

    if (height!.endsWith('%')) {
      final percentage = double.tryParse(height!.replaceAll('%', ''));
      // For height percentage, use a base height (e.g., 200px) or screen width ratio
      return percentage != null ? (200 * percentage / 100) : null;
    } else if (height!.endsWith('px')) {
      return double.tryParse(height!.replaceAll('px', ''));
    } else {
      // Assume it's a pixel value if no unit specified
      return double.tryParse(height!);
    }
  }

  @override
  String toString() {
    return 'Banner(id: $id, handle: $handle, imageUrl: $imageUrl, width: $width, height: $height, linkUrl: $linkUrl)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Banner &&
        other.id == id &&
        other.handle == handle &&
        other.imageUrl == imageUrl &&
        other.width == width &&
        other.height == height &&
        other.linkUrl == linkUrl;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        handle.hashCode ^
        imageUrl.hashCode ^
        width.hashCode ^
        height.hashCode ^
        linkUrl.hashCode;
  }
}

class BannersConnection {
  final List<BannerEdge> edges;

  BannersConnection({required this.edges});

  factory BannersConnection.fromJson(Map<String, dynamic> json) {
    final edgesData = json['edges'] as List? ?? [];
    final edges = edgesData
        .map((edge) => BannerEdge.fromJson(edge as Map<String, dynamic>))
        .toList();
    
    return BannersConnection(edges: edges);
  }
}

class BannerEdge {
  final Banner node;

  BannerEdge({required this.node});

  factory BannerEdge.fromJson(Map<String, dynamic> json) {
    return BannerEdge(
      node: Banner.fromGraphQL(json['node'] as Map<String, dynamic>),
    );
  }
}
