/// Domain entity for payment status
class PaymentStatus {
  final String status;
  final String orderId;
  final int amountReceived;
  final List<Charge> charges;

  const PaymentStatus({
    required this.status,
    required this.orderId,
    required this.amountReceived,
    required this.charges,
  });

  /// Check if payment is successful
  bool get isSuccessful => status == 'succeeded';

  /// Check if payment is still processing
  bool get isProcessing => status == 'processing';

  /// Check if payment requires action (e.g., 3D Secure)
  bool get requiresAction => status == 'requires_action';

  /// Check if payment was canceled
  bool get isCanceled => status == 'canceled';

  /// Check if payment failed
  bool get isFailed => status == 'failed';

  /// Check if payment is in a final state (completed or failed)
  bool get isFinal => isSuccessful || isCanceled || isFailed;

  @override
  String toString() {
    return 'PaymentStatus(status: $status, orderId: $orderId, amountReceived: $amountReceived, charges: $charges)';
  }
}

/// Domain entity for charge information
class Charge {
  final String id;
  final int amount;
  final String status;
  final int created;
  final String? receiptUrl;

  const Charge({
    required this.id,
    required this.amount,
    required this.status,
    required this.created,
    this.receiptUrl,
  });

  /// Get created date as DateTime
  DateTime get createdDate => DateTime.fromMillisecondsSinceEpoch(created * 1000);

  @override
  String toString() {
    return 'Charge(id: $id, amount: $amount, status: $status, created: $created, receiptUrl: $receiptUrl)';
  }
}
