import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

/// Wallet credits widget matching Figma design
class WalletCreditsWidget extends StatefulWidget {
  final double availableCredits;
  final Function(double)? onCreditsApplied;
  final double? currentCreditsUsed;

  const WalletCreditsWidget({
    super.key,
    required this.availableCredits,
    this.onCreditsApplied,
    this.currentCreditsUsed,
  });

  @override
  State<WalletCreditsWidget> createState() => _WalletCreditsWidgetState();
}

class _WalletCreditsWidgetState extends State<WalletCreditsWidget> {
  final TextEditingController _creditsController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.currentCreditsUsed != null) {
      _creditsController.text = '\$${widget.currentCreditsUsed!.toStringAsFixed(2)}';
    }
  }

  @override
  void dispose() {
    _creditsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity, // Full screen width - responsive
      height: 88, // Exact Figma height
      decoration: const BoxDecoration(
        color: Color(0xFFFFFFFF), // Exact Figma background
        border: Border(
          top: BorderSide(
            color: Color(0xFFE5E7EB), // Exact Figma border
            width: 1,
          ),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 32), // Exact Figma padding
        child: Row(
          children: [
            // Available credits section - flexible left side
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    /// not implement yet it static
                    child: Text(
                      'Available credits\n\$${0.00.toStringAsFixed(2)}',
                      style: GoogleFonts.inter(
                        fontSize: 12, // Exact Figma font size
                        fontWeight: FontWeight.w600, // Exact Figma font weight
                        color: const Color(0xFF1F2937), // Exact Figma color
                      ),
                      textAlign: TextAlign.center, // Exact Figma: CENTER alignment
                    ),
                  ),
                ],
              ),
            ),

            // Credits input section - flexible right side to FILL available space (per Figma)
              Expanded(
  child: Container(
    height: 48,
    padding: const EdgeInsets.symmetric(horizontal: 8),
    decoration: BoxDecoration(
      color: Colors.white, // Outer background
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: Color(0xFFE5E7EB), width: 1), // Optional outer border
    ),
    child: Center(
      child: TextField(
        controller: _creditsController,
        textAlign: TextAlign.center,
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
        ],
        style: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: Colors.black, // Ensure input text is always black
        ),
        decoration: InputDecoration(
          hintText: '\$0.00',
          hintStyle: GoogleFonts.inter(
            fontSize: 15,
            fontWeight: FontWeight.w400,
          ),
          contentPadding: EdgeInsets.zero,
          isDense: true,
          filled: true,
          fillColor: Colors.white, // Ensures white background in all modes
          border: InputBorder.none, // Remove default TextField border
          enabledBorder: InputBorder.none, // Remove border when enabled
          focusedBorder: InputBorder.none, // Remove border when focused
        ),
        onChanged: (value) {
          final amount = double.tryParse(value.replaceAll('\$', '')) ?? 0.0;
          widget.onCreditsApplied?.call(value.isNotEmpty ? amount : 0.0);
        },
      ),
    ),
  ),
)
          ],
        ),
      ),
    );
  }
}
