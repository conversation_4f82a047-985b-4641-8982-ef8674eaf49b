/// Domain entity for payment intent
class PaymentIntent {
  final String clientSecret;
  final String paymentIntentId;
  final String orderId;
  final double amount;
  final String currency;

  const PaymentIntent({
    required this.clientSecret,
    required this.paymentIntentId,
    required this.orderId,
    required this.amount,
    required this.currency,
  });

  @override
  String toString() {
    return 'PaymentIntent(clientSecret: $clientSecret, paymentIntentId: $paymentIntentId, orderId: $orderId, amount: $amount, currency: $currency)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentIntent &&
        other.clientSecret == clientSecret &&
        other.paymentIntentId == paymentIntentId &&
        other.orderId == orderId &&
        other.amount == amount &&
        other.currency == currency;
  }

  @override
  int get hashCode {
    return clientSecret.hashCode ^
        paymentIntentId.hashCode ^
        orderId.hashCode ^
        amount.hashCode ^
        currency.hashCode;
  }
}
