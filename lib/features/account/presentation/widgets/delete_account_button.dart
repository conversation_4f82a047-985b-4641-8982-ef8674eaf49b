import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import '../../../../l10n/app_localizations.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../../../auth/presentation/services/auth_guard_service.dart';
import '../../../auth/domain/entities/user.dart';
import '../../../auth/data/models/enhanced_user_model.dart';
import '../../data/services/delete_account_service.dart';
import '../../../../routes/app_router.dart';
import '../../../../core/providers/tab_state_provider.dart';

class DeleteAccountButton extends ConsumerStatefulWidget {
  const DeleteAccountButton({super.key});

  @override
  ConsumerState<DeleteAccountButton> createState() => _DeleteAccountButtonState();
}

class _DeleteAccountButtonState extends ConsumerState<DeleteAccountButton> {
  final DeleteAccountService _deleteAccountService = DeleteAccountService();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: _isLoading ? null : () => _showDeleteConfirmation(context),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              Container(
                width: 24,
                height: 24,
                alignment: Alignment.center,
                child: _isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Color(0xFFEF4444), // Red color
                          ),
                        ),
                      )
                    : const Icon(
                        Icons.delete_outline,
                        color: Color(0xFFEF4444), // Red color
                        size: 20,
                      ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  l10n.deleteMyAccount,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFFEF4444), // Red color
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.deleteAccountConfirmTitle),
        content: Text(l10n.deleteAccountConfirmMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _handleDeleteAccount();
            },
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFFEF4444), // Red color
            ),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );
  }

  Future<void> _handleDeleteAccount() async {
    final l10n = AppLocalizations.of(context);
    
    // Get current user
    final authState = ref.read(authNotifierProvider);

    // Check if user is authenticated and get user data
    User? user;
    authState.maybeWhen(
      authenticated: (authenticatedUser) {
        user = authenticatedUser;
      },
      orElse: () {
        user = null;
      },
    );

    if (user == null) {
      _showErrorSnackBar(l10n.authenticationRequired);
      return;
    }

    if (user is! EnhancedUserModel) {
      _showErrorSnackBar(l10n.authenticationRequired);
      return;
    }

    final enhancedUser = user as EnhancedUserModel;
    if (enhancedUser.backendToken == null) {
      _showErrorSnackBar(l10n.authenticationRequired);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // First ensure we have valid backend authentication
      _showLoadingSnackBar(l10n.checkingAccountDeletion);

      final hasBackendAuth = await AuthGuardService.requireBackendAuthForAccountFeatures(
        context,
        ref,
        onError: (error) {
          debugPrint('[DeleteAccount] Backend auth failed: $error');
        },
      );

      if (!hasBackendAuth) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar(l10n.authenticationRequired);
        return;
      }

      // Get the current user with valid backend token
      final authState = ref.read(authNotifierProvider);
      User? currentUser;
      authState.maybeWhen(
        authenticated: (authenticatedUser) {
          currentUser = authenticatedUser;
        },
        orElse: () {
          currentUser = null;
        },
      );

      if (currentUser == null || currentUser is! EnhancedUserModel) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar(l10n.authenticationRequired);
        return;
      }

      final enhancedUser = currentUser as EnhancedUserModel;
      if (enhancedUser.backendToken == null) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar(l10n.authenticationRequired);
        return;
      }

      // Now check if account can be deleted
      final deletionCheck = await _deleteAccountService.checkDeletionEligibility(
        enhancedUser.backendToken!,
      );

      if (!deletionCheck.canDelete) {
        setState(() {
          _isLoading = false;
        });
        
        // Show error with specific reasons
        String errorMessage = l10n.cannotDeleteAccount;
        if (deletionCheck.errors != null && deletionCheck.errors!.isNotEmpty) {
          errorMessage += ':\n${deletionCheck.errors!.join('\n')}';
        }
        _showErrorSnackBar(errorMessage);
        return;
      }

      // Proceed with deletion
      _showLoadingSnackBar(l10n.deletingAccount);

      await _deleteAccountService.deleteAccount(enhancedUser.backendToken!);

      // Account deleted successfully - sign out and redirect
      await ref.read(authNotifierProvider.notifier).signOut();
      
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        _showSuccessSnackBar(l10n.accountDeletedSuccessfully);
        
        // Set tab to home and redirect
        ref.read(tabStateProvider.notifier).setTab(0);
        context.go(AppRoutes.home);
      }

    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      String errorMessage = l10n.accountDeletionFailed;
      if (e is AccountDeletionException) {
        errorMessage = e.message;
        if (e.reasons != null && e.reasons!.isNotEmpty) {
          errorMessage += ':\n${e.reasons!.join('\n')}';
        }
      }
      
      _showErrorSnackBar(errorMessage);
    }
  }

  void _showLoadingSnackBar(String message) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        duration: const Duration(seconds: 30), // Long duration for loading
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: AppLocalizations.of(context).cancel,
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
}
