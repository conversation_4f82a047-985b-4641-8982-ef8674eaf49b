import '../../domain/entities/shipping_address.dart';

/// Data model for shipping address
class ShippingAddressModel {
  final String name;
  final String line1;
  final String? line2;
  final String city;
  final String state;
  final String postalCode;
  final String country;
  final String? phone;

  const ShippingAddressModel({
    required this.name,
    required this.line1,
    this.line2,
    required this.city,
    required this.state,
    required this.postalCode,
    required this.country,
    this.phone,
  });

  /// Create from JSON
  factory ShippingAddressModel.fromJson(Map<String, dynamic> json) {
    return ShippingAddressModel(
      name: json['name'] as String,
      line1: json['line1'] as String,
      line2: json['line2'] as String?,
      city: json['city'] as String,
      state: json['state'] as String,
      postalCode: json['postal_code'] as String,
      country: json['country'] as String,
      phone: json['phone'] as String?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'line1': line1,
      if (line2 != null) 'line2': line2,
      'city': city,
      'state': state,
      'postal_code': postalCode,
      'country': country,
      if (phone != null) 'phone': phone,
    };
  }

  /// Convert to domain entity
  ShippingAddress toDomain() {
    return ShippingAddress(
      name: name,
      line1: line1,
      line2: line2,
      city: city,
      state: state,
      postalCode: postalCode,
      country: country,
      phone: phone,
    );
  }

  /// Create from domain entity
  factory ShippingAddressModel.fromDomain(ShippingAddress address) {
    return ShippingAddressModel(
      name: address.name,
      line1: address.line1,
      line2: address.line2,
      city: address.city,
      state: address.state,
      postalCode: address.postalCode,
      country: address.country,
      phone: address.phone,
    );
  }

  @override
  String toString() {
    return 'ShippingAddressModel(name: $name, line1: $line1, line2: $line2, city: $city, state: $state, postalCode: $postalCode, country: $country, phone: $phone)';
  }
}
