import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_travelgator/features/auth/presentation/providers/auth_providers.dart';
import 'package:flutter_travelgator/features/auth/domain/entities/user.dart' as auth;
import '../../../auth/presentation/services/auth_guard_service.dart';

class AccountInformationScreen extends ConsumerStatefulWidget {
  const AccountInformationScreen({super.key});

  @override
  ConsumerState<AccountInformationScreen> createState() =>
      _AccountInformationScreenState();
}

class _AccountInformationScreenState
    extends ConsumerState<AccountInformationScreen> {
  bool _isLoading = true;
  auth.User? _user;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Check authentication before loading user data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthenticationAndLoadData();
    });
  }

  Future<void> _checkAuthenticationAndLoadData() async {
    final hasAuth = await AuthGuardService.requireAuthForAccount(
      context,
      ref,
      onSuccess: () {
        debugPrint('[AccountInformationScreen] Authentication successful - loading user data');
        _loadUserData();
      },
    );

    // If already authenticated, load data immediately
    if (hasAuth) {
      _loadUserData();
    }
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authRepo = ref.read(authRepositoryProvider);
      if (authRepo == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Authentication service unavailable';
        });
        return;
      }

      final userResult = await authRepo.getCurrentUser();
      userResult.fold(
        (failure) {
          setState(() {
            _isLoading = false;
            _errorMessage = failure.message;
          });
        },
        (user) {
          setState(() {
            _isLoading = false;
            _user = user;
          });
        },
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading user data: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF3F4F6),
      appBar: AppBar(
        title: const Text('Account Information'),
        backgroundColor: const Color(0xFFFF6982),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadUserData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_user == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.person_off, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            const Text(
              'No user information available',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadUserData,
              child: const Text('Refresh'),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile photo if available
          if (_user!.profilePhoto != null && _user!.profilePhoto!.isNotEmpty)
            Center(
              child: Container(
                margin: const EdgeInsets.only(bottom: 24),
                child: CircleAvatar(
                  radius: 50,
                  backgroundImage: NetworkImage(_user!.profilePhoto!),
                ),
              ),
            ),

          const Text(
            'Personal Information',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildInfoItem('Name', _user!.name),
          _buildInfoItem('Email', _user!.email),
          _buildInfoItem('User ID', _user!.id),
          _buildInfoItem(
            'Email Verified',
            _user!.isEmailVerified ? 'Yes' : 'No',
          ),

          const SizedBox(height: 16),
          Center(
            child: ElevatedButton(
              onPressed: _loadUserData,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF6982),
              ),
              child: const Text('Refresh Data'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontSize: 16, color: Color(0xFF6B7280)),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 16, color: Color(0xFF1F2937)),
            ),
          ),
        ],
      ),
    );
  }
}
