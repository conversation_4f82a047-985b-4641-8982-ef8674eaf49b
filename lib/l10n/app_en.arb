{"appTitle": "TravelGator", "@appTitle": {"description": "The title of the application"}, "welcomeMessage": "Welcome to TravelGator", "@welcomeMessage": {"description": "Welcome message on the home screen"}, "signIn": "Sign In", "@signIn": {"description": "Text for sign in button"}, "signUp": "SIGN UP", "@signUp": {"description": "Text for sign up button"}, "email": "Email", "@email": {"description": "Label for email field"}, "password": "Password", "@password": {"description": "Label for password field"}, "name": "Name", "@name": {"description": "Label for name field"}, "forgotPassword": "Forgot Password", "@forgotPassword": {"description": "Text for forgot password link"}, "orContinueWith": "or continue with", "@orContinueWith": {"description": "Text for social login separator"}, "dontHaveAccount": "Don't have an account?", "@dontHaveAccount": {"description": "Text for sign up prompt"}, "alreadyHaveAccount": "Already have an account?", "@alreadyHaveAccount": {"description": "Text for sign in prompt"}, "createAccount": "Create Account", "@createAccount": {"description": "Title for account creation screen"}, "getStarted": "Get Started", "@getStarted": {"description": "Text for get started button"}, "legalDocuments": "Legal Documents", "@legalDocuments": {"description": "Title for legal documents modal"}, "userAgreement": "User Agreement", "@userAgreement": {"description": "Title for user agreement document"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Title for privacy policy document"}, "termsAndConditions": "Terms & Conditions", "@termsAndConditions": {"description": "Title for terms and conditions document"}, "lastUpdated": "Last updated: {date}", "@lastUpdated": {"description": "Text showing when document was last updated", "placeholders": {"date": {"type": "String", "description": "The date when the document was last updated"}}}, "close": "Close", "@close": {"description": "Text for close button"}, "languages": "Languages", "@languages": {"description": "Title for languages screen"}, "selectLanguage": "Select Language", "@selectLanguage": {"description": "Header text for language selection"}, "languageDescription": "Choose your preferred language for the app interface.", "@languageDescription": {"description": "Description text for language selection"}, "languageChangeNote": "The app interface will update immediately when you select a new language.", "@languageChangeNote": {"description": "Note about language change behavior"}, "languageChanged": "Language changed to {language}", "@languageChanged": {"description": "Confirmation message when language is changed", "placeholders": {"language": {"type": "String", "description": "The name of the selected language"}}}, "accountInformation": "Account Information", "@accountInformation": {"description": "Title for account information section"}, "transactionHistory": "Transaction History", "@transactionHistory": {"description": "Title for transaction history section"}, "paymentSettings": "Payment Settings", "@paymentSettings": {"description": "Title for payment settings section"}, "currency": "<PERSON><PERSON><PERSON><PERSON>", "@currency": {"description": "Title for currency section"}, "helpCenter": "Help Center", "@helpCenter": {"description": "Title for help center section"}, "logout": "Logout", "@logout": {"description": "Text for logout button"}, "logoutConfirmTitle": "Logout", "@logoutConfirmTitle": {"description": "Title for logout confirmation dialog"}, "logoutConfirmMessage": "Are you sure you want to logout?", "@logoutConfirmMessage": {"description": "Message for logout confirmation dialog"}, "cancel": "Cancel", "@cancel": {"description": "Text for cancel button"}, "appVersion": "TravelGator version {version}", "@appVersion": {"description": "App version text", "placeholders": {"version": {"type": "String", "description": "The version number of the app"}}}, "cart": "<PERSON><PERSON>", "@cart": {"description": "Cart screen title"}, "cartIsEmpty": "Cart is empty", "@cartIsEmpty": {"description": "Message when cart has no items"}, "addSomeProducts": "Add some products to get started", "@addSomeProducts": {"description": "Subtitle for empty cart"}, "startShopping": "Start Shopping", "@startShopping": {"description": "But<PERSON> text to start shopping"}, "authenticationRequired": "Authentication Required", "@authenticationRequired": {"description": "Title for authentication required error"}, "pleaseSignInToCart": "Please sign in to access your cart", "@pleaseSignInToCart": {"description": "Message for authentication required"}, "unableToLoadCart": "Unable to load cart", "@unableToLoadCart": {"description": "Error message when cart fails to load"}, "failedToLoadCart": "Failed to load cart", "@failedToLoadCart": {"description": "Generic cart loading error message"}, "tryAgain": "Try Again", "@tryAgain": {"description": "Button text to retry an action"}, "total": "Total:", "@total": {"description": "Label for total price"}, "addToCart": "Add to Cart", "@addToCart": {"description": "Button text to add item to cart"}, "buyNow": "Buy Now", "@buyNow": {"description": "Button text to buy item immediately"}, "hideDetails": "Hide details", "@hideDetails": {"description": "Button text to hide product details"}, "remove": "Remove", "@remove": {"description": "Button text to remove item"}, "itemRemovedFromCart": "Item removed from cart", "@itemRemovedFromCart": {"description": "Success message when item is removed from cart"}, "variant": "<PERSON><PERSON><PERSON>", "@variant": {"description": "Label for variant selection"}, "noVariantsAvailable": "No variants available for this product", "@noVariantsAvailable": {"description": "Error message when product has no variants"}, "failedToAddToCart": "Failed to add to cart: {error}", "@failedToAddToCart": {"description": "Error message when adding to cart fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "gb": "GB", "@gb": {"description": "Gigabyte unit abbreviation"}, "day": "Day", "@day": {"description": "Day unit for duration"}, "days": "Days", "@days": {"description": "Days unit for duration (plural)"}, "signedInSuccessfully": "Successfully signed in!", "@signedInSuccessfully": {"description": "Success message when user signs in"}, "signInFailed": "Sign-in failed: {error}", "@signInFailed": {"description": "Error message when sign-in fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "retry": "Retry", "@retry": {"description": "Button text to retry an action"}, "orderCompletedSuccessfully": "🎉 Order completed successfully!", "@orderCompletedSuccessfully": {"description": "Success message when order is completed"}, "checkoutFailed": "❌ Checkout failed: {error}", "@checkoutFailed": {"description": "Error message when checkout fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "buyNowFailed": "❌ Buy Now failed: {error}", "@buyNowFailed": {"description": "Error message when buy now fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "processingCheckout": "Processing checkout...", "@processingCheckout": {"description": "Loading message during checkout"}, "checkOut": "Check out", "@checkOut": {"description": "Checkout button text"}, "proceedToCheckout": "Proceed to Checkout", "@proceedToCheckout": {"description": "Proceed to checkout button text"}, "home": "Home", "@home": {"description": "Home tab label in navigation bar"}, "account": "Account", "@account": {"description": "Account tab label in navigation bar"}, "sims": "SIMs", "@sims": {"description": "SIMs tab label in navigation bar"}, "travelgator": "TravelGator", "@travelgator": {"description": "TravelGator tab label and screen title"}, "rewards": "Rewards", "@rewards": {"description": "Rewards tab label in navigation bar"}, "travelgatorScreen": "TravelGator Screen", "@travelgatorScreen": {"description": "TravelGator screen content title"}, "welcomeToTravelgator": "Welcome to\nTravelGator", "@welcomeToTravelgator": {"description": "Welcome title in onboarding"}, "stayConnected": "Stay Connected", "@stayConnected": {"description": "Stay connected title in onboarding"}, "stayConnectedGlobally": "Stay Connected\nGlobally", "@stayConnectedGlobally": {"description": "Stay connected globally title"}, "discoverExplore": "Discover & Explore\nWith Ease", "@discoverExplore": {"description": "Discover and explore title"}, "discoverAmazingDestinations": "Discover Amazing\nDestinations", "@discoverAmazingDestinations": {"description": "Discover amazing destinations title"}, "details": "Details", "@details": {"description": "Details screen title"}, "yourPurchase": "Your Purchase", "@yourPurchase": {"description": "Your purchase section title"}, "yourData": "Your Data", "@yourData": {"description": "Your data section title"}, "newESim": "New eSIM", "@newESim": {"description": "New eSIM option label"}, "topUp": "Top Up", "@topUp": {"description": "Top up option label"}, "totalPayment": "Total Payment", "@totalPayment": {"description": "Total payment label"}, "seeDetails": "See details", "@seeDetails": {"description": "See details button text"}, "selectYourTravelgatorESim": "Select your TravelGator eSIM", "@selectYourTravelgatorESim": {"description": "Default option for eSIM selection"}, "dataAmount": "Data Amount", "@dataAmount": {"description": "Data amount selection label"}, "duration": "Duration", "@duration": {"description": "Duration selection label"}, "quantity": "Quantity", "@quantity": {"description": "Quantity selection label"}, "eSimType": "eSIM Type", "@eSimType": {"description": "eSIM type selection label"}, "reviewYourPurchase": "Review your purchase", "@reviewYourPurchase": {"description": "Title for purchase review popup"}, "yourDataLabel": "Your data", "@yourDataLabel": {"description": "Label for data amount dropdown"}, "dayLabel": "Day", "@dayLabel": {"description": "Label for day duration dropdown"}, "oneDay": "1 day", "@oneDay": {"description": "One day option"}, "threeDays": "3 days", "@threeDays": {"description": "Three days option"}, "sevenDays": "7 days", "@sevenDays": {"description": "Seven days option"}, "fifteenDays": "15 days", "@fifteenDays": {"description": "Fifteen days option"}, "thirtyDays": "30 days", "@thirtyDays": {"description": "Thirty days option"}, "removeItemConfirmation": "Are you sure you want to remove \"{itemTitle}\" from your cart?", "@removeItemConfirmation": {"description": "Confirmation message for removing item from cart", "placeholders": {"itemTitle": {"type": "String", "description": "The title of the item being removed"}}}, "checkoutFailedTitle": "Checkout Failed", "@checkoutFailedTitle": {"description": "Title for checkout failed error"}, "checkoutFailedMessage": "There was an issue processing your checkout. This is likely a backend configuration issue with Shopify.", "@checkoutFailedMessage": {"description": "Detailed message for checkout failure"}, "checkoutErrorTitle": "Checkout Error", "@checkoutErrorTitle": {"description": "Title for checkout error"}, "deleteMyAccount": "Delete My Account", "@deleteMyAccount": {"description": "Text for delete account button"}, "deleteAccountConfirmTitle": "Delete Account", "@deleteAccountConfirmTitle": {"description": "Title for delete account confirmation dialog"}, "deleteAccountConfirmMessage": "Are you sure you want to delete your account? This will permanently remove all your data from our system.", "@deleteAccountConfirmMessage": {"description": "Message for delete account confirmation dialog"}, "delete": "Delete", "@delete": {"description": "Text for delete button"}, "accountDeletionFailed": "Account deletion failed", "@accountDeletionFailed": {"description": "Error message when account deletion fails"}, "accountDeletedSuccessfully": "Account deleted successfully", "@accountDeletedSuccessfully": {"description": "Success message when account is deleted"}, "cannotDeleteAccount": "Cannot delete account", "@cannotDeleteAccount": {"description": "Error message when account cannot be deleted"}, "checkingAccountDeletion": "Checking if account can be deleted...", "@checkingAccountDeletion": {"description": "Loading message when checking account deletion eligibility"}, "deletingAccount": "Deleting account...", "@deletingAccount": {"description": "Loading message when deleting account"}, "serverErrorContactSupport": "Server error occurred. Please try again later or contact support for assistance.", "@serverErrorContactSupport": {"description": "Error message for server errors during account deletion"}, "errorLoadingProducts": "Error loading products", "@errorLoadingProducts": {"description": "Error message when products fail to load"}, "noProductsFound": "No products found", "@noProductsFound": {"description": "Message when no products match the filter"}, "tryAdjustingFilter": "Try adjusting your filter", "@tryAdjustingFilter": {"description": "Suggestion when no products are found"}, "productAddedToCart": "{productTitle} added to cart!", "@productAddedToCart": {"description": "Success message when product is added to cart", "placeholders": {"productTitle": {"type": "String", "description": "The title of the product added"}}}, "cartOperationMayHaveFailed": "Cart operation may have failed. Please check your cart.", "@cartOperationMayHaveFailed": {"description": "Warning message when cart operation status is unclear"}, "processingAddToCart": "Adding to cart...", "@processingAddToCart": {"description": "Loading message when adding item to cart"}, "from": "From", "@from": {"description": "Prefix for price display, e.g. 'From $10.99'"}, "minPrice": "<PERSON>", "@minPrice": {"description": "Label for minimum price filter"}, "maxPrice": "Max Price", "@maxPrice": {"description": "Label for maximum price filter"}, "applyFilter": "Apply Filter", "@applyFilter": {"description": "Button text to apply price filter"}, "clearFilter": "Clear Filter", "@clearFilter": {"description": "Button text to clear price filter"}, "priceFilter": "Price Filter", "@priceFilter": {"description": "Title for price filter section"}, "under": "Under", "@under": {"description": "Prefix for price range, e.g. 'Under $5'"}, "over": "Over", "@over": {"description": "Prefix for price range, e.g. 'Over $50'"}, "orSetCustomRange": "Or set custom range:", "@orSetCustomRange": {"description": "Label for custom price range section"}, "to": "to", "@to": {"description": "Connector word between min and max price, e.g. '$5 to $10'"}, "countryVietnam": "Vietnam", "@countryVietnam": {"description": "Country name: Vietnam"}, "countryChina": "China", "@countryChina": {"description": "Country name: China"}, "countryUnitedStates": "United States", "@countryUnitedStates": {"description": "Country name: United States"}, "countryNewZealand": "New Zealand", "@countryNewZealand": {"description": "Country name: New Zealand"}, "regionAsean": "ASEAN", "@regionAsean": {"description": "Region name: ASEAN"}, "regionEastAsia": "East Asia", "@regionEastAsia": {"description": "Region name: East Asia"}, "regionNorthAmerica": "North America", "@regionNorthAmerica": {"description": "Region name: North America"}, "regionOceania": "Oceania", "@regionOceania": {"description": "Region name: Oceania"}, "eSIMRoamingData": "eSIM Roaming Data", "@eSIMRoamingData": {"description": "Description text for eSIM products"}, "countries": "Countries", "@countries": {"description": "Tab label for countries filter"}, "regions": "Regions", "@regions": {"description": "Tab label for regions filter"}, "authenticationSuccessful": "Authentication successful!", "@authenticationSuccessful": {"description": "Message shown when user successfully authenticates"}, "paymentSuccessful": "🎉 Payment successful!", "@paymentSuccessful": {"description": "Success message when payment is completed"}, "paymentCanceled": "Payment canceled", "@paymentCanceled": {"description": "Message when payment is canceled by user"}, "paymentFailed": "Payment failed", "@paymentFailed": {"description": "Generic payment failure message"}, "paymentErrorUnknown": "Unknown payment error", "@paymentErrorUnknown": {"description": "Message for unknown payment errors"}, "paymentIntentCreationFailed": "Unable to create payment request. Please try again.", "@paymentIntentCreationFailed": {"description": "Error when payment intent creation fails"}, "paymentVerificationFailed": "Payment verification failed. Please contact support.", "@paymentVerificationFailed": {"description": "Error when backend payment verification fails"}, "paymentSucceededOrderFailed": "Payment successful but unable to complete order. Please contact support.", "@paymentSucceededOrderFailed": {"description": "Error when payment succeeds but order completion fails"}, "backendSystemError": "Backend system error. Please contact technical support.", "@backendSystemError": {"description": "Error for backend system issues"}}