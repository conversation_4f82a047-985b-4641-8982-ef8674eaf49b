import 'package:equatable/equatable.dart';

/// Discount statistics entity for user analytics
class DiscountStats extends Equatable {
  final int totalOrdersWithDiscounts;
  final double totalSavings;
  final int totalDiscountCodesUsed;
  final String mostUsedDiscountCode;
  final double averageDiscountAmount;
  final List<DiscountUsage> recentDiscounts;

  const DiscountStats({
    required this.totalOrdersWithDiscounts,
    required this.totalSavings,
    required this.totalDiscountCodesUsed,
    required this.mostUsedDiscountCode,
    required this.averageDiscountAmount,
    required this.recentDiscounts,
  });

  /// Get formatted total savings for display
  String get formattedTotalSavings => '\$${totalSavings.toStringAsFixed(2)}';

  /// Get formatted average discount for display
  String get formattedAverageDiscount => '\$${averageDiscountAmount.toStringAsFixed(2)}';

  @override
  List<Object?> get props => [
        totalOrdersWithDiscounts,
        totalSavings,
        totalDiscountCodesUsed,
        mostUsedDiscountCode,
        averageDiscountAmount,
        recentDiscounts,
      ];
}

/// Individual discount usage information
class DiscountUsage extends Equatable {
  final String code;
  final String? title;
  final double amount;
  final DateTime usedAt;
  final String orderId;
  final String type; // 'shopify' or 'legacy'

  const DiscountUsage({
    required this.code,
    this.title,
    required this.amount,
    required this.usedAt,
    required this.orderId,
    required this.type,
  });

  /// Get formatted discount amount for display
  String get formattedAmount => '\$${amount.toStringAsFixed(2)}';

  /// Get formatted date for display
  String get formattedDate {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    final day = usedAt.day;
    final month = months[usedAt.month - 1];
    final year = usedAt.year;
    
    return '$day $month, $year';
  }

  /// Check if this is a Shopify discount
  bool get isShopifyDiscount => type == 'shopify';

  /// Check if this is a legacy discount
  bool get isLegacyDiscount => type == 'legacy';

  @override
  List<Object?> get props => [
        code,
        title,
        amount,
        usedAt,
        orderId,
        type,
      ];
}
