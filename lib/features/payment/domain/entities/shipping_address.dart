/// Domain entity for shipping address
class ShippingAddress {
  final String name;
  final String line1;
  final String? line2;
  final String city;
  final String state;
  final String postalCode;
  final String country;
  final String? phone;

  const ShippingAddress({
    required this.name,
    required this.line1,
    this.line2,
    required this.city,
    required this.state,
    required this.postalCode,
    required this.country,
    this.phone,
  });

  /// Get full address as a formatted string
  String get fullAddress {
    final parts = <String>[
      line1,
      if (line2 != null && line2!.isNotEmpty) line2!,
      '$city, $state $postalCode',
      country,
    ];
    return parts.join('\n');
  }

  /// Get single line address
  String get singleLineAddress {
    final parts = <String>[
      line1,
      if (line2 != null && line2!.isNotEmpty) line2!,
      city,
      state,
      postalCode,
      country,
    ];
    return parts.join(', ');
  }

  @override
  String toString() {
    return 'ShippingAddress(name: $name, line1: $line1, line2: $line2, city: $city, state: $state, postalCode: $postalCode, country: $country, phone: $phone)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ShippingAddress &&
        other.name == name &&
        other.line1 == line1 &&
        other.line2 == line2 &&
        other.city == city &&
        other.state == state &&
        other.postalCode == postalCode &&
        other.country == country &&
        other.phone == phone;
  }

  @override
  int get hashCode {
    return name.hashCode ^
        line1.hashCode ^
        line2.hashCode ^
        city.hashCode ^
        state.hashCode ^
        postalCode.hashCode ^
        country.hashCode ^
        phone.hashCode;
  }
}
