import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../l10n/app_localizations.dart';
import 'account_action_list.dart';
import '../../account_routes.dart';
import '../../../legal/presentation/services/legal_documents_service.dart';

class AccountSections extends StatelessWidget {
  const AccountSections({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildPersonalSection(context),
        const SizedBox(height: 8),
        _buildPreferencesSection(context),
        const SizedBox(height: 8),
        _buildLegalSection(context),
      ],
    );
  }

  Widget _buildPersonalSection(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return AccountActionList(
      items: [
        ActionItem(
          title: l10n.accountInformation,
          icon: 'assets/icons/profile_icon.svg',
          onTap: () => context.go(AccountRoutes.getAccountInformationPath()),
        ),
        ActionItem(
          title: l10n.transactionHistory,
          icon: 'assets/icons/document_icon.svg',
          onTap: () => context.go(AccountRoutes.getTransactionHistoryPath()),
        ),
        // ActionItem(
        //   title: l10n.paymentSettings,
        //   icon: 'assets/icons/wallet_icon.svg',
        //   onTap: () => context.go(AccountRoutes.getPaymentSettingsPath()),
        // ),
      ],
    );
  }

  Widget _buildPreferencesSection(BuildContext context) {


    return AccountActionList(
      items: [
        // ActionItem(
        //   title: l10n.currency,
        //   icon: 'assets/icons/star_icon.svg',
        //   onTap: () => context.go(AccountRoutes.getCurrencyPath()),
        // ),
        // ActionItem(
        //   title: l10n.languages,
        //   icon: 'assets/icons/world_icon.svg',
        //   onTap: () => context.go(AccountRoutes.getLanguagesPath()),
        // ),
        // ActionItem(
        //   title: l10n.helpCenter,
        //   icon: 'assets/icons/info_icon.svg',
        //   onTap: () => context.go(AccountRoutes.getHelpCenterPath()),
        // ),
      ],
    );
  }

  Widget _buildLegalSection(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return AccountActionList(
      items: [
        ActionItem(
          title: l10n.privacyPolicy,
          icon: 'assets/icons/folder_lock_icon.svg',
          onTap: () => LegalDocumentsService.showPrivacyPolicySafe(context),
        ),
        ActionItem(
          title: l10n.termsAndConditions,
          icon: 'assets/icons/book_icon.svg',
          onTap: () => LegalDocumentsService.showUserAgreementSafe(context),
        ),
      ],
    );
  }
}
