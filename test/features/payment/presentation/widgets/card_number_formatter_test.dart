import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

/// Test implementation of the card number formatter
class CardNumberFormatter extends TextInputFormatter {

  /// Get maximum digits allowed based on card type
  static int _getMaxDigitsForCardType(String digits) {
    if (digits.isEmpty) return 19; // Allow typing initially

    // American Express: starts with 34 or 37, 15 digits
    if (digits.startsWith('34') || digits.startsWith('37')) {
      return 15;
    }

    // Diners Club: starts with 30, 36, 38, 14 digits
    if (digits.startsWith('30') || digits.startsWith('36') || digits.startsWith('38')) {
      return 14;
    }

    // Most other cards (Visa, MasterCard, etc.): 16 digits
    return 16;
  }

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Remove all non-digits
    final digitsOnly = newValue.text.replaceAll(RegExp(r'\D'), '');

    // Get max digits for this card type
    final maxDigits = _getMaxDigitsForCardType(digitsOnly);

    // Limit to maximum allowed digits for this card type
    final limitedDigits = digitsOnly.length > maxDigits
        ? digitsOnly.substring(0, maxDigits)
        : digitsOnly;
    
    // Format with spaces every 4 digits
    final buffer = StringBuffer();
    for (int i = 0; i < limitedDigits.length; i++) {
      if (i > 0 && i % 4 == 0) {
        buffer.write(' ');
      }
      buffer.write(limitedDigits[i]);
    }
    
    final formatted = buffer.toString();
    
    // Calculate cursor position
    int cursorPosition = formatted.length;
    if (newValue.selection.baseOffset < newValue.text.length) {
      // Try to maintain cursor position relative to digits
      final digitsBeforeCursor = newValue.text
          .substring(0, newValue.selection.baseOffset)
          .replaceAll(RegExp(r'\D'), '')
          .length;
      
      cursorPosition = 0;
      int digitCount = 0;
      for (int i = 0; i < formatted.length; i++) {
        if (formatted[i] != ' ') {
          digitCount++;
          if (digitCount > digitsBeforeCursor) break;
        }
        cursorPosition++;
      }
    }
    
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: cursorPosition),
    );
  }
}

void main() {
  group('CardNumberFormatter', () {
    late CardNumberFormatter formatter;

    setUp(() {
      formatter = CardNumberFormatter();
    });

    test('should format valid 16-digit card number correctly', () {
      const input = TextEditingValue(
        text: '****************',
        selection: TextSelection.collapsed(offset: 16),
      );
      
      final result = formatter.formatEditUpdate(
        const TextEditingValue(),
        input,
      );
      
      expect(result.text, '4242 4242 4242 4242');
      expect(result.selection.baseOffset, 19);
    });

    test('should prevent input beyond 16 digits for Visa/MasterCard', () {
      const input = TextEditingValue(
        text: '*****************', // 17 digits (Visa starts with 4)
        selection: TextSelection.collapsed(offset: 17),
      );

      final result = formatter.formatEditUpdate(
        const TextEditingValue(),
        input,
      );

      // Should be limited to 16 digits for Visa
      expect(result.text, '4242 4242 4242 4242');
      expect(result.text.replaceAll(' ', '').length, 16);
    });

    test('should handle Stripe test card number correctly', () {
      const input = TextEditingValue(
        text: '****************',
        selection: TextSelection.collapsed(offset: 16),
      );

      final result = formatter.formatEditUpdate(
        const TextEditingValue(),
        input,
      );
      
      expect(result.text, '4242 4242 4242 4242');
      expect(result.text.replaceAll(' ', '').length, 16);
    });

    test('should prevent extra digit after valid card number', () {
      // First, input valid 16-digit card
      const validInput = TextEditingValue(
        text: '****************',
        selection: TextSelection.collapsed(offset: 16),
      );

      final validResult = formatter.formatEditUpdate(
        const TextEditingValue(),
        validInput,
      );

      expect(validResult.text, '4242 4242 4242 4242');

      // Then try to add extra digit - should be prevented
      const extraDigitInput = TextEditingValue(
        text: '*****************', // 17 digits
        selection: TextSelection.collapsed(offset: 17),
      );

      final extraResult = formatter.formatEditUpdate(
        validResult,
        extraDigitInput,
      );

      // Should remain at 16 digits, extra digit should be rejected
      expect(extraResult.text, '4242 4242 4242 4242');
      expect(extraResult.text.replaceAll(' ', '').length, 16);
    });

    test('should handle empty input', () {
      const input = TextEditingValue(
        text: '',
        selection: TextSelection.collapsed(offset: 0),
      );
      
      final result = formatter.formatEditUpdate(
        const TextEditingValue(),
        input,
      );

      expect(result.text, '');
      expect(result.selection.baseOffset, 0);
    });

    test('should handle partial input correctly', () {
      const input = TextEditingValue(
        text: '42424',
        selection: TextSelection.collapsed(offset: 5),
      );

      final result = formatter.formatEditUpdate(
        const TextEditingValue(),
        input,
      );
      
      expect(result.text, '4242 4');
      expect(result.selection.baseOffset, 6);
    });

    test('should allow 15 digits for American Express cards', () {
      const input = TextEditingValue(
        text: '***************', // 15 digits (AmEx starts with 37)
        selection: TextSelection.collapsed(offset: 15),
      );

      final result = formatter.formatEditUpdate(
        const TextEditingValue(),
        input,
      );

      expect(result.text, '3782 8224 6310 005');
      expect(result.text.replaceAll(' ', '').length, 15);
    });

    test('should prevent 16th digit for American Express cards', () {
      const input = TextEditingValue(
        text: '***************1', // 16 digits (AmEx should be limited to 15)
        selection: TextSelection.collapsed(offset: 16),
      );

      final result = formatter.formatEditUpdate(
        const TextEditingValue(),
        input,
      );

      // Should be limited to 15 digits for AmEx
      expect(result.text, '3782 8224 6310 005');
      expect(result.text.replaceAll(' ', '').length, 15);
    });
  });
}
