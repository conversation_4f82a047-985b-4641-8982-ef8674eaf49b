import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/order_providers.dart';
import '../widgets/order_card.dart';
import '../../../auth/presentation/services/auth_guard_service.dart';

class TransactionHistoryScreen extends ConsumerStatefulWidget {
  const TransactionHistoryScreen({super.key});

  @override
  ConsumerState<TransactionHistoryScreen> createState() => _TransactionHistoryScreenState();
}

class _TransactionHistoryScreenState extends ConsumerState<TransactionHistoryScreen> {
  @override
  void initState() {
    super.initState();
    // Set loading state immediately and check authentication
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Set loading state immediately to show spinner
      ref.read(ordersProvider.notifier).setLoading(true);

      // Check if user has backend authentication
      final hasAuth = await AuthGuardService.requireBackendAuthentication(
        context,
        ref,
        title: 'Authentication Required',
        subtitle: 'Please authenticate to view your transaction history',
        onSuccess: () {
          // Fetch orders after successful authentication
          ref.read(ordersProvider.notifier).fetchOrders();
        },
        onError: (error) {
          // Handle authentication error and clear loading
          ref.read(ordersProvider.notifier).setLoading(false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Authentication failed: $error'),
              backgroundColor: Colors.red,
            ),
          );
        },
      );

      // If already authenticated, fetch orders immediately
      if (hasAuth) {
        ref.read(ordersProvider.notifier).fetchOrders();
      } else {
        // If authentication failed, clear loading state
        ref.read(ordersProvider.notifier).setLoading(false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final ordersState = ref.watch(ordersProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFF3F4F6),
      appBar: _buildAppBar(context),
      body: _buildBody(ordersState),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: const Color(0xFFFF6982),
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: const Text(
        'Transaction history',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.search, color: Colors.white),
          onPressed: () {
            // TODO: Implement search functionality
          },
        ),
        IconButton(
          icon: const Icon(Icons.filter_list, color: Colors.white),
          onPressed: () {
            // TODO: Implement filter functionality
          },
        ),
      ],
    );
  }

  Widget _buildBody(OrdersState ordersState) {
    if (ordersState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Color(0xFFFF6982),
        ),
      );
    }

    if (ordersState.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading transactions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              ordersState.error!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.read(ordersProvider.notifier).refreshOrders(),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF6982),
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (ordersState.orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No transactions yet',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your transaction history will appear here',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(ordersProvider.notifier).refreshOrders(),
      color: const Color(0xFFFF6982),
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: ordersState.orders.length,
        itemBuilder: (context, index) {
          final order = ordersState.orders[index];
          return OrderCard(order: order);
        },
      ),
    );
  }

}
