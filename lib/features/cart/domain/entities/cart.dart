import 'package:json_annotation/json_annotation.dart';
import 'shopify_discount.dart';

part 'cart.g.dart';

/// Cart item entity
@JsonSerializable()
class CartItem {
  @JsonKey(name: 'variant_id')
  final String variantId;
  @Json<PERSON>ey(name: 'product_id')
  final String productId;
  final int quantity;
  final double price;
  final String title;
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  /// Whether this item is selected for checkout (backend-managed selection)
  final bool selected;

  const CartItem({
    required this.variantId,
    required this.productId,
    required this.quantity,
    required this.price,
    required this.title,
    this.imageUrl,
    this.selected = true, // Default to selected for backward compatibility
  });

  factory CartItem.fromJson(Map<String, dynamic> json) => _$CartItemFromJson(json);

  Map<String, dynamic> toJson() => _$CartItemToJson(this);

  CartItem copyWith({
    String? variantId,
    String? productId,
    int? quantity,
    double? price,
    String? title,
    String? imageUrl,
    bool? selected,
  }) {
    return CartItem(
      variantId: variantId ?? this.variantId,
      productId: productId ?? this.productId,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      title: title ?? this.title,
      imageUrl: imageUrl ?? this.imageUrl,
      selected: selected ?? this.selected,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem &&
        other.variantId == variantId &&
        other.productId == productId &&
        other.quantity == quantity &&
        other.price == price &&
        other.title == title &&
        other.imageUrl == imageUrl;
  }

  @override
  int get hashCode {
    return variantId.hashCode ^
        productId.hashCode ^
        quantity.hashCode ^
        price.hashCode ^
        title.hashCode ^
        imageUrl.hashCode;
  }

  @override
  String toString() {
    return 'CartItem(variantId: $variantId, productId: $productId, quantity: $quantity, price: $price, title: $title, imageUrl: $imageUrl)';
  }
}

/// Cart entity
@JsonSerializable()
class Cart {
  final String? id;
  final List<CartItem> items;
  @JsonKey(name: 'total_price')
  final double totalPrice;
  final String? currency;
  @JsonKey(name: 'items_count')
  final int itemsCount;

  // New selection metadata fields (optional for backward compatibility)
  @JsonKey(name: 'selected_items_count')
  final int? selectedItemsCount;
  @JsonKey(name: 'selected_total_price')
  final double? selectedTotalPrice;
  @JsonKey(name: 'all_items_selected')
  final bool? allItemsSelected;
  @JsonKey(name: 'any_items_selected')
  final bool? anyItemsSelected;

  // Enhanced discount fields for Shopify integration
  @JsonKey(name: 'original_total')
  final double? originalTotal;
  @JsonKey(name: 'total_discount')
  final double? totalDiscount;
  @JsonKey(name: 'has_discount')
  final bool? hasDiscount;

  // Legacy voucher fields (for backward compatibility)
  @JsonKey(name: 'voucher_code')
  final String? voucherCode;
  @JsonKey(name: 'discount_amount')
  final double? discountAmount;
  @JsonKey(name: 'discounted_total')
  final double? discountedTotal;
  @JsonKey(name: 'has_voucher')
  final bool? hasVoucher;

  // Shopify discount information
  @JsonKey(name: 'shopify_discount')
  final ShopifyDiscount? shopifyDiscount;
  @JsonKey(name: 'discount_summary')
  final DiscountSummary? discountSummary;

  const Cart({
    this.id,
    required this.items,
    required this.totalPrice,
    this.currency,
    required this.itemsCount,
    this.selectedItemsCount,
    this.selectedTotalPrice,
    this.allItemsSelected,
    this.anyItemsSelected,
    // Enhanced discount fields
    this.originalTotal,
    this.totalDiscount,
    this.hasDiscount,
    // Legacy voucher fields
    this.voucherCode,
    this.discountAmount,
    this.discountedTotal,
    this.hasVoucher,
    // Shopify discount information
    this.shopifyDiscount,
    this.discountSummary,
  });

  factory Cart.fromJson(Map<String, dynamic> json) => _$CartFromJson(json);

  Map<String, dynamic> toJson() => _$CartToJson(this);

  Cart copyWith({
    String? id,
    List<CartItem>? items,
    double? totalPrice,
    String? currency,
    int? itemsCount,
    int? selectedItemsCount,
    double? selectedTotalPrice,
    bool? allItemsSelected,
    bool? anyItemsSelected,
    // Enhanced discount fields
    double? originalTotal,
    double? totalDiscount,
    bool? hasDiscount,
    // Legacy voucher fields
    String? voucherCode,
    double? discountAmount,
    double? discountedTotal,
    bool? hasVoucher,
    // Shopify discount information
    ShopifyDiscount? shopifyDiscount,
    DiscountSummary? discountSummary,
  }) {
    return Cart(
      id: id ?? this.id,
      items: items ?? this.items,
      totalPrice: totalPrice ?? this.totalPrice,
      currency: currency ?? this.currency,
      itemsCount: itemsCount ?? this.itemsCount,
      selectedItemsCount: selectedItemsCount ?? this.selectedItemsCount,
      selectedTotalPrice: selectedTotalPrice ?? this.selectedTotalPrice,
      allItemsSelected: allItemsSelected ?? this.allItemsSelected,
      anyItemsSelected: anyItemsSelected ?? this.anyItemsSelected,
      // Enhanced discount fields
      originalTotal: originalTotal ?? this.originalTotal,
      totalDiscount: totalDiscount ?? this.totalDiscount,
      hasDiscount: hasDiscount ?? this.hasDiscount,
      // Legacy voucher fields
      voucherCode: voucherCode ?? this.voucherCode,
      discountAmount: discountAmount ?? this.discountAmount,
      discountedTotal: discountedTotal ?? this.discountedTotal,
      hasVoucher: hasVoucher ?? this.hasVoucher,
      // Shopify discount information
      shopifyDiscount: shopifyDiscount ?? this.shopifyDiscount,
      discountSummary: discountSummary ?? this.discountSummary,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Cart &&
        other.id == id &&
        other.items == items &&
        other.totalPrice == totalPrice &&
        other.currency == currency &&
        other.itemsCount == itemsCount;
  }

  @override
  int get hashCode {
    return id.hashCode ^ items.hashCode ^ totalPrice.hashCode ^ currency.hashCode ^ itemsCount.hashCode;
  }

  /// Get the effective discount amount (prioritizes new system over legacy)
  double get effectiveDiscountAmount {
    if (totalDiscount != null && totalDiscount! > 0) {
      return totalDiscount!;
    }
    return discountAmount ?? 0.0;
  }

  /// Get the effective discount code (prioritizes new system over legacy)
  String? get effectiveDiscountCode {
    if (discountSummary?.code != null) {
      return discountSummary!.code;
    }
    return voucherCode;
  }

  /// Check if cart has any discount applied (new or legacy)
  bool get hasAnyDiscount {
    return (hasDiscount == true) || (hasVoucher == true);
  }

  /// Get the discount title (only available for Shopify discounts)
  String? get discountTitle {
    return shopifyDiscount?.title ?? discountSummary?.title;
  }

  /// Check if this is a Shopify discount
  bool get hasShopifyDiscount {
    return shopifyDiscount != null || discountSummary?.isShopifyDiscount == true;
  }

  /// Check if this is a legacy discount
  bool get hasLegacyDiscount {
    return discountSummary?.isLegacyDiscount == true ||
           (hasVoucher == true && shopifyDiscount == null);
  }

  @override
  String toString() {
    return 'Cart(id: $id, items: $items, totalPrice: $totalPrice, currency: $currency, itemsCount: $itemsCount, hasDiscount: $hasAnyDiscount)';
  }
}