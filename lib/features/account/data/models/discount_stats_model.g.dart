// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'discount_stats_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DiscountStatsModel _$DiscountStatsModelFromJson(Map<String, dynamic> json) =>
    DiscountStatsModel(
      totalOrdersWithDiscounts:
          (json['total_orders_with_discounts'] as num?)?.toInt() ?? 0,
      totalSavings: (json['total_savings'] as num?)?.toDouble() ?? 0.0,
      totalDiscountCodesUsed:
          (json['total_discount_codes_used'] as num?)?.toInt() ?? 0,
      mostUsedDiscountCode: json['most_used_discount_code'] as String? ?? '',
      averageDiscountAmount:
          (json['average_discount_amount'] as num?)?.toDouble() ?? 0.0,
      recentDiscounts:
          (json['recent_discounts'] as List<dynamic>?)
              ?.map(
                (e) => DiscountUsageModel.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          [],
    );

Map<String, dynamic> _$DiscountStatsModelToJson(DiscountStatsModel instance) =>
    <String, dynamic>{
      'total_orders_with_discounts': instance.totalOrdersWithDiscounts,
      'total_savings': instance.totalSavings,
      'total_discount_codes_used': instance.totalDiscountCodesUsed,
      'most_used_discount_code': instance.mostUsedDiscountCode,
      'average_discount_amount': instance.averageDiscountAmount,
      'recent_discounts': instance.recentDiscounts,
    };

DiscountUsageModel _$DiscountUsageModelFromJson(Map<String, dynamic> json) =>
    DiscountUsageModel(
      code: json['code'] as String,
      title: json['title'] as String?,
      amount: (json['amount'] as num).toDouble(),
      usedAt: json['used_at'] as String,
      orderId: json['order_id'] as String,
      type: json['type'] as String? ?? 'legacy',
    );

Map<String, dynamic> _$DiscountUsageModelToJson(DiscountUsageModel instance) =>
    <String, dynamic>{
      'code': instance.code,
      'title': instance.title,
      'amount': instance.amount,
      'used_at': instance.usedAt,
      'order_id': instance.orderId,
      'type': instance.type,
    };
