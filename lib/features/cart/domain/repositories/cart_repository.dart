import 'package:dartz/dartz.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import '../entities/cart.dart';
import '../entities/checkout_item.dart';

/// Repository interface for cart operations
abstract class CartRepository {
  /// Get the current cart
  Future<Either<Failure, Cart>> getCart();

  /// Add an item to the cart
  Future<Either<Failure, Cart>> addItem({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
  });

  /// Update an item's quantity in the cart
  Future<Either<Failure, Cart>> updateItem({
    required String variantId,
    required int quantity,
  });

  /// Update an item's complete information in the cart
  Future<Either<Failure, Cart>> updateItemComplete({
    required String oldVariantId,
    required String newVariantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
  });

  /// Remove an item from the cart
  Future<Either<Failure, Cart>> removeItem({
    required String variantId,
  });

  /// Proceed to checkout with all items
  Future<Either<Failure, String>> checkout();

  /// Proceed to checkout with selected items only (legacy - full quantities)
  Future<Either<Failure, String>> checkoutSelected({
    required List<String> selectedVariantIds,
  });

  /// Proceed to checkout with specific items and quantities
  Future<Either<Failure, String>> checkoutSelectedWithQuantities({
    required List<CheckoutItem> items,
  });

  /// Clear the entire cart
  Future<Either<Failure, Cart>> clearCart();

  /// Apply voucher to cart
  Future<Either<Failure, Cart>> applyVoucher({
    required String voucherCode,
  });

  /// Remove voucher from cart
  Future<Either<Failure, Cart>> removeVoucher();

  /// Validate voucher code
  Future<Either<Failure, VoucherValidation>> validateVoucher({
    required String code,
    required double orderAmount,
  });
}

/// Voucher validation result
class VoucherValidation {
  final bool isValid;
  final double discountAmount;
  final double finalAmount;
  final String? errorMessage;

  const VoucherValidation({
    required this.isValid,
    required this.discountAmount,
    required this.finalAmount,
    this.errorMessage,
  });
}