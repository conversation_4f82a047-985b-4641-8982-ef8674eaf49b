import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/errors/failures.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../../../auth/data/models/enhanced_user_model.dart';
import '../../domain/entities/payment_intent.dart';
import '../../domain/entities/payment_status.dart';
import '../../domain/entities/shipping_address.dart';
import '../../domain/repositories/payment_repository.dart';
import '../../domain/exceptions/payment_exceptions.dart';
import '../datasources/cart_payment_data_source.dart';
import '../datasources/buy_now_payment_data_source.dart';
import '../models/shipping_address_model.dart';

/// Implementation of payment repository using documented API
class PaymentRepositoryImpl implements PaymentRepository {
  final CartPaymentDataSource _cartDataSource;
  final BuyNowPaymentDataSource _buyNowDataSource;
  final Ref _ref;

  PaymentRepositoryImpl({
    required CartPaymentDataSource cartDataSource,
    required BuyNowPaymentDataSource buyNowDataSource,
    required Ref ref,
  }) : _cartDataSource = cartDataSource,
       _buyNowDataSource = buyNowDataSource,
       _ref = ref;

  @override
  Future<Either<Failure, PaymentIntent>> createCartPaymentIntent({
    required ShippingAddress shippingAddress,
  }) async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      debugPrint('[PaymentRepository] Creating cart payment intent...');

      final shippingAddressModel = ShippingAddressModel.fromDomain(shippingAddress);
      final intentModel = await _cartDataSource.createCartPaymentIntent(
        shippingAddress: shippingAddressModel,
        authToken: authToken,
      );

      return Right(intentModel.toDomain());
    } on PaymentException catch (e) {
      debugPrint('[PaymentRepository] Payment exception: $e');
      return Left(PaymentFailure.fromException(e));
    } catch (e) {
      debugPrint('[PaymentRepository] Unexpected error: $e');
      return Left(PaymentFailure(message: 'Failed to create cart payment intent: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentIntent>> createCartPaymentIntentDigital() async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      debugPrint('[PaymentRepository] Creating digital cart payment intent (no shipping)...');

      final intentModel = await _cartDataSource.createCartPaymentIntentDigital(
        authToken: authToken,
      );

      return Right(intentModel.toDomain());
    } on PaymentException catch (e) {
      debugPrint('[PaymentRepository] Payment exception: $e');
      return Left(PaymentFailure.fromException(e));
    } catch (e) {
      debugPrint('[PaymentRepository] Unexpected error: $e');
      return Left(PaymentFailure(message: 'Failed to create digital cart payment intent: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentIntent>> createCartPaymentIntentWithItems({
    required List<Map<String, dynamic>> cartItems,
  }) async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      debugPrint('[PaymentRepository] Creating cart payment intent with specific items...');
      debugPrint('[PaymentRepository] Items count: ${cartItems.length}');

      final intentModel = await _cartDataSource.createCartPaymentIntentWithItems(
        cartItems: cartItems,
        authToken: authToken,
      );

      return Right(intentModel.toDomain());
    } on PaymentException catch (e) {
      debugPrint('[PaymentRepository] Payment exception: $e');
      return Left(PaymentFailure.fromException(e));
    } catch (e) {
      debugPrint('[PaymentRepository] Unexpected error: $e');
      return Left(PaymentFailure(message: 'Failed to create cart payment intent with items: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentIntent>> createBuyNowPaymentIntent({
    required String variantId,
    required int quantity,
    required double price,
    required String productName,
    String? voucherCode,
  }) async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      debugPrint('[PaymentRepository] Creating buy now payment intent...');
      debugPrint('[PaymentRepository] Product: $productName, Price: $price, Quantity: $quantity');

      final intentModel = await _buyNowDataSource.createBuyNowPaymentIntent(
        variantId: variantId,
        quantity: quantity,
        price: price,
        productName: productName,
        authToken: authToken,
        voucherCode: voucherCode,
      );

      return Right(intentModel.toDomain());
    } on PaymentException catch (e) {
      debugPrint('[PaymentRepository] Payment exception: $e');
      return Left(PaymentFailure.fromException(e));
    } catch (e) {
      debugPrint('[PaymentRepository] Unexpected error: $e');
      return Left(PaymentFailure(message: 'Failed to create buy now payment intent: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentStatus>> getCartPaymentStatus({
    required String paymentIntentId,
  }) async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      debugPrint('[PaymentRepository] Getting cart payment status: $paymentIntentId');

      final statusModel = await _cartDataSource.getPaymentStatus(
        paymentIntentId: paymentIntentId,
        authToken: authToken,
      );

      return Right(statusModel.toDomain());
    } on PaymentException catch (e) {
      debugPrint('[PaymentRepository] Payment exception: $e');
      return Left(PaymentFailure.fromException(e));
    } catch (e) {
      debugPrint('[PaymentRepository] Unexpected error: $e');
      return Left(PaymentFailure(message: 'Failed to get cart payment status: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentStatus>> getBuyNowPaymentStatus({
    required String paymentIntentId,
  }) async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      debugPrint('[PaymentRepository] Getting buy now payment status: $paymentIntentId');

      final statusModel = await _buyNowDataSource.getBuyNowPaymentStatus(
        paymentIntentId: paymentIntentId,
        authToken: authToken,
      );

      return Right(statusModel.toDomain());
    } on PaymentException catch (e) {
      debugPrint('[PaymentRepository] Payment exception: $e');
      return Left(PaymentFailure.fromException(e));
    } catch (e) {
      debugPrint('[PaymentRepository] Unexpected error: $e');
      return Left(PaymentFailure(message: 'Failed to get buy now payment status: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentStatus>> confirmCartPayment({
    required String paymentIntentId,
    required String paymentMethodId,
  }) async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      debugPrint('[PaymentRepository] Confirming cart payment...');
      debugPrint('[PaymentRepository] PaymentIntent ID: $paymentIntentId');
      debugPrint('[PaymentRepository] PaymentMethod ID: $paymentMethodId');

      final result = await _cartDataSource.confirmPayment(
        paymentIntentId: paymentIntentId,
        paymentMethodId: paymentMethodId,
        authToken: authToken,
      );

      debugPrint('[PaymentRepository] Cart payment confirmed: $result');
      return Right(result.toDomain());
    } on PaymentException catch (e) {
      debugPrint('[PaymentRepository] Payment exception: $e');
      return Left(PaymentFailure.fromException(e));
    } catch (e) {
      debugPrint('[PaymentRepository] Unexpected error: $e');
      return Left(PaymentFailure(message: 'Failed to confirm cart payment: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentStatus>> confirmPaymentWithCardDetails({
    required String paymentIntentId,
    required String clientSecret,
    required Map<String, dynamic> cardDetails,
  }) async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      debugPrint('[PaymentRepository] Confirming payment with card details via backend...');
      debugPrint('[PaymentRepository] PaymentIntent ID: $paymentIntentId');
      debugPrint('[PaymentRepository] Card: ****${cardDetails['number']?.toString().substring(cardDetails['number'].toString().length - 4)}');

      final result = await _cartDataSource.confirmPaymentWithCardDetails(
        paymentIntentId: paymentIntentId,
        clientSecret: clientSecret,
        cardDetails: cardDetails,
        authToken: authToken,
      );

      debugPrint('[PaymentRepository] Payment confirmed with card details: $result');
      return Right(result.toDomain());
    } on PaymentException catch (e) {
      debugPrint('[PaymentRepository] Payment exception: $e');
      return Left(PaymentFailure.fromException(e));
    } catch (e) {
      debugPrint('[PaymentRepository] Unexpected error: $e');
      return Left(PaymentFailure(message: 'Failed to confirm payment with card details: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentStatus>> confirmBuyNowPayment({
    required String paymentIntentId,
    required String paymentMethodId,
  }) async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      debugPrint('[PaymentRepository] Confirming buy now payment: $paymentIntentId');

      final statusModel = await _buyNowDataSource.confirmBuyNowPayment(
        paymentIntentId: paymentIntentId,
        paymentMethodId: paymentMethodId,
        authToken: authToken,
      );

      return Right(statusModel.toDomain());
    } on PaymentException catch (e) {
      debugPrint('[PaymentRepository] Payment exception: $e');
      return Left(PaymentFailure.fromException(e));
    } catch (e) {
      debugPrint('[PaymentRepository] Unexpected error: $e');
      return Left(PaymentFailure(message: 'Failed to confirm buy now payment: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> cancelCartPayment({
    required String paymentIntentId,
  }) async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      debugPrint('[PaymentRepository] Canceling cart payment: $paymentIntentId');

      await _cartDataSource.cancelPayment(
        paymentIntentId: paymentIntentId,
        authToken: authToken,
      );

      return const Right(null);
    } on PaymentException catch (e) {
      debugPrint('[PaymentRepository] Payment exception: $e');
      return Left(PaymentFailure.fromException(e));
    } catch (e) {
      debugPrint('[PaymentRepository] Unexpected error: $e');
      return Left(PaymentFailure(message: 'Failed to cancel cart payment: $e'));
    }
  }

  /// Get authentication token from current user
  String? _getAuthToken() {
    final authState = _ref.read(authNotifierProvider);
    return authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          if (enhancedUser.hasBackendAuth && 
              enhancedUser.backendToken != null && 
              enhancedUser.backendToken!.isNotEmpty) {
            return enhancedUser.backendToken;
          }
        }
        return null;
      },
      orElse: () => null,
    );
  }
}
