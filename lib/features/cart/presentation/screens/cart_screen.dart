import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/cart_provider.dart';
import '../providers/cart_state.dart';
import '../widgets/cart_item_card.dart';
import '../widgets/cart_header.dart';
import '../widgets/cart_bottom_info.dart';
import '../widgets/cart_state_widgets.dart';
import '../services/cart_checkout_service.dart';
import '../services/cart_item_service.dart';
import '../../../auth/presentation/services/auth_guard_service.dart';
import '../../../auth/presentation/mixins/auth_redirect_mixin.dart';
import '../../domain/entities/cart.dart';


class CartScreen extends ConsumerStatefulWidget {
  const CartScreen({super.key});

  @override
  ConsumerState<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends ConsumerState<CartScreen> with AuthRedirectMixin, WidgetsBindingObserver {
  // Track selected items for checkout
  final Set<String> _selectedItems = <String>{};

  // Cache for computed values to avoid recalculation
  bool? _cachedIsAllSelected;
  double? _cachedSelectedTotal;
  String _lastCartHash = '';

  // Track user interaction to respect user choices
  bool _isInitialLoad = true;
  String? _lastHandledError;

  // Services
  late CartCheckoutService _checkoutService;
  late CartItemService _itemService;

  @override
  void initState() {
    super.initState();
    // Initialize services
    _checkoutService = CartCheckoutService(context, ref);
    _itemService = CartItemService(context, ref, _invalidateCache);

    // Add observer for app lifecycle changes
    WidgetsBinding.instance.addObserver(this);

    // Load cart data when screen initializes - fast UX approach
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthAndLoadCartFast();
    });

    // Set up cart state listener for error handling
    _setupCartErrorListener();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // When app comes back to foreground, refresh cart if there was an error
    if (state == AppLifecycleState.resumed) {
      final cartState = ref.read(cartProvider);
      if (cartState.status == CartStatus.error) {
        debugPrint('[CartScreen] App resumed with cart error, retrying...');
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            ref.read(cartProvider.notifier).getCart();
          }
        });
      }
    }
  }

  void _setupCartErrorListener() {
    // Listen to cart state changes for error handling
    ref.listenManual(cartProvider, (previous, next) {
      // Only handle transition from loading to error (actual failure)
      if (previous?.status == CartStatus.loading &&
          next.status == CartStatus.error &&
          next.error != null) {

        final isConnectionError = next.error!.contains('Connection refused') ||
                                 next.error!.contains('Connection error') ||
                                 next.error!.contains('Backend server is not available') ||
                                 next.error!.contains('check your internet connection') ||
                                 next.error!.contains('Backend authentication required');

        if (isConnectionError && _lastHandledError != next.error && mounted) {
          _lastHandledError = next.error;
          // Show notification and redirect to home after verification fails
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text(
                    'Unable to connect to server',
                    style: TextStyle(color: Colors.white),
                  ),
                  backgroundColor: Colors.red[600],
                  duration: const Duration(milliseconds: 800), // Show for 800ms
                ),
              );

              // Hide notification first, then redirect
              Future.delayed(const Duration(milliseconds: 900), () {
                if (mounted) {
                  // Clear notification first
                  ScaffoldMessenger.of(context).clearSnackBars();

                  // Then redirect after a brief pause
                  Future.delayed(const Duration(milliseconds: 200), () {
                    if (mounted) {
                      context.go('/home');
                    }
                  });
                }
              });
            }
          });
        }
      } else if (next.cart != null) {
        // Reset error tracking when cart loads successfully
        _lastHandledError = null;
      }
    });
  }

  Future<void> _checkAuthAndLoadCartFast() async {
    // Fast UX approach: User is already authenticated (checked before navigation)
    // Start loading cart immediately to show fresh data
    debugPrint('[CartScreen] Starting fast cart loading for authenticated user...');

    // Immediately load cart with fresh data - this will show loading state
    if (mounted) {
      ref.read(cartProvider.notifier).getCart(forceRefresh: true);
    }
  }

  void _initializeSelectedItems(Cart cart) {
    // Only auto-select on initial load, respect user's explicit choices afterward
    if (_isInitialLoad && _selectedItems.isEmpty && cart.items.isNotEmpty) {
      debugPrint('[CartScreen] Initial auto-selecting all ${cart.items.length} items');
      setState(() {
        _selectedItems.addAll(cart.items.map((item) => item.variantId));
        _invalidateCache();
        _isInitialLoad = false; // Mark as no longer initial load
      });
      debugPrint('[CartScreen] Selected items after auto-select: ${_selectedItems.toList()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Set up auth redirect listener
    setupAuthRedirect();

    final cartState = ref.watch(cartProvider);



    // Initialize selected items on cart load (respects user choices)
    if (cartState.cart != null) {
      _initializeSelectedItems(cartState.cart!);
    }

    // Reset cache when cart changes (including quantity changes)
    if (cartState.cart != null) {
      // Create a simple hash of cart items to detect any changes
      final currentCartHash = cartState.cart!.items.map((item) => '${item.variantId}:${item.quantity}').join(',');
      if (currentCartHash != _lastCartHash) {
        _invalidateCache();
        _lastCartHash = currentCartHash;
      }
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF3F4F6),
      appBar: const CartHeader(),
      body: Column(
        children: [
          // Cart Content
          Expanded(
            child: _buildCartContent(cartState),
          ),

          // Only show bottom info when cart is loaded and has items
          if (_shouldShowBottomInfo(cartState))
            _buildBottomInfo(cartState.cart!),
        ],
      ),
    );
  }

  bool _shouldShowBottomInfo(CartState cartState) {
    return cartState.status == CartStatus.loaded &&
           cartState.cart != null &&
           cartState.cart!.items.isNotEmpty;
  }

  Widget _buildBottomInfo(Cart cart) {
    return CartBottomInfo(
      isAllSelected: _isAllSelected(cart),
      onSelectAllChanged: _handleSelectAllChanged,
      totalPrice: _calculateSelectedTotal(cart),
      onCheckout: _handleCheckout,
      hasSelectedItems: _selectedItems.isNotEmpty,
    );
  }

  void _invalidateCache() {
    _cachedIsAllSelected = null;
    _cachedSelectedTotal = null;
  }

  Widget _buildCartContent(CartState cartState) {
    switch (cartState.status) {
      case CartStatus.initial:
      case CartStatus.loading:
        return const CartLoadingWidget();

      case CartStatus.error:
        // Check if it's a connection error that will be handled by redirect
        final isConnectionError = cartState.error != null && (
          cartState.error!.contains('Connection refused') ||
          cartState.error!.contains('Connection error') ||
          cartState.error!.contains('Backend server is not available') ||
          cartState.error!.contains('check your internet connection') ||
          cartState.error!.contains('Backend authentication required')
        );

        // For connection errors, show loading while redirect is happening
        if (isConnectionError) {
          return const CartLoadingWidget();
        }

        // For other errors, show error widget
        return CartErrorWidget(
          error: cartState.error,
          isAuthenticated: cartState.isAuthenticated,
          onRetry: () => ref.read(cartProvider.notifier).getCart(),
          onAuthRequired: _handleAuthRequired,
        );

      case CartStatus.loaded:
        if (cartState.cart == null || cartState.cart!.items.isEmpty) {
          return const CartEmptyWidget();
        }
        return _buildCartItems(cartState.cart!);
    }
  }

  Future<void> _handleAuthRequired() async {
    await AuthGuardService.requireAuthForCart(
      context,
      ref,
      onSuccess: () {
        // Refresh cart after successful authentication
        debugPrint('[CartScreen] Authentication successful - user can now access cart');
        if (mounted) {
          ref.read(cartProvider.notifier).getCart();
        }
      },
    );
  }

  Widget _buildCartItems(Cart cart) {
    // Initialize selected items when cart loads
    _initializeSelectedItems(cart);

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: cart.items.length,
      itemBuilder: (context, index) => _buildCartItemCard(cart.items[index]),
    );
  }



  Widget _buildCartItemCard(item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: CartItemCard(
        item: item,
        isSelected: _selectedItems.contains(item.variantId),
        onSelectionChanged: (isSelected) => _handleSelectionChanged(item.variantId, isSelected),
        onQuantityChanged: (newQuantity) => _handleQuantityChange(item, newQuantity),
        onRemove: () => _handleRemoveItem(item),
      ),
    );
  }

  void _handleSelectionChanged(String variantId, bool isSelected) {
    debugPrint('[CartScreen] Selection changed: $variantId -> $isSelected');
    setState(() {
      if (isSelected) {
        _selectedItems.add(variantId);
      } else {
        _selectedItems.remove(variantId);
      }
      // Invalidate cache when selection changes
      _invalidateCache();
    });
    debugPrint('[CartScreen] Selected items after change: ${_selectedItems.toList()}');
  }

  bool _isAllSelected(Cart cart) {
    if (cart.items.isEmpty) return false;

    // Use cached value if available
    if (_cachedIsAllSelected != null) {
      return _cachedIsAllSelected!;
    }

    // Calculate and cache the result
    _cachedIsAllSelected = cart.items.every((item) => _selectedItems.contains(item.variantId));
    return _cachedIsAllSelected!;
  }

  void _handleSelectAllChanged(bool selectAll) {
    final cartState = ref.read(cartProvider);
    if (cartState.cart == null) return;

    setState(() {
      if (selectAll) {
        _selectedItems.addAll(cartState.cart!.items.map((item) => item.variantId));
      } else {
        _selectedItems.clear(); // Now users can actually clear all selections!
      }
      // Invalidate cache when selection changes
      _invalidateCache();
    });
    debugPrint('[CartScreen] Select all changed: $selectAll, selected count: ${_selectedItems.length}');
  }

  double _calculateSelectedTotal(Cart cart) {
    // Use cached value if available
    if (_cachedSelectedTotal != null) {
      return _cachedSelectedTotal!;
    }

    // Calculate and cache the result
    double total = 0;
    for (final item in cart.items) {
      if (_selectedItems.contains(item.variantId)) {
        total += item.price * item.quantity;
      }
    }

    _cachedSelectedTotal = total;
    return total;
  }

  void _handleQuantityChange(item, int newQuantity) {
    // Don't invalidate cache immediately for quantity changes to avoid refresh feeling
    // The cache will be invalidated when the cart state updates from the provider
    _itemService.handleQuantityChange(item, newQuantity);
  }

  void _handleRemoveItem(item) {
    // Remove from selected items first
    setState(() {
      _selectedItems.remove(item.variantId);
      _invalidateCache();
    });

    // Show confirmation dialog through service
    _itemService.showRemoveConfirmationDialog(item);
  }

  void _handleCheckout() async {
    // Only checkout selected items
    if (_selectedItems.isEmpty) {
      // Show message if no items are selected
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one item to checkout'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    // Get current cart state
    final cartState = ref.read(cartProvider);
    if (cartState.cart == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cart is empty'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Create a filtered cart with only selected items
    final selectedItems = cartState.cart!.items
        .where((item) => _selectedItems.contains(item.variantId))
        .toList();

    debugPrint('[CartScreen] Checkout: ${_selectedItems.length} selected items out of ${cartState.cart!.items.length} total');
    debugPrint('[CartScreen] Selected variants: ${_selectedItems.toList()}');

    final filteredCart = Cart(
      id: cartState.cart!.id,
      items: selectedItems,
      totalPrice: _calculateSelectedTotal(cartState.cart!),
      currency: cartState.cart!.currency,
      itemsCount: selectedItems.length,
    );

    // Use Stripe checkout with filtered cart (selected items only)
    await _checkoutService.handleStripeCheckoutWithSelectedItems(filteredCart);
  }

}
