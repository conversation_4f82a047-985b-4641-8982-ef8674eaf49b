import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_travelgator/core/utils/validators.dart';

void main() {
  group('Card Validators', () {
    group('validateCardNumber', () {
      test('should return null for valid card numbers', () {
        // Valid test card numbers (using <PERSON><PERSON> algorithm)
        expect(Validators.validateCardNumber('****************'), isNull); // Visa
        expect(Validators.validateCardNumber('****************'), isNull); // Visa
        expect(Validators.validateCardNumber('****************'), isNull); // Mastercard
        expect(Validators.validateCardNumber('2223003122003222'), isNull); // Mastercard
        expect(Validators.validateCardNumber('***************'), isNull);  // Amex
        expect(Validators.validateCardNumber('***************'), isNull);  // Amex
        
        // Test with spaces (should be handled)
        expect(Validators.validateCardNumber('4242 4242 4242 4242'), isNull);
        expect(Validators.validateCardNumber('5555 5555 5555 4444'), isNull);
      });

      test('should return error for invalid card numbers', () {
        expect(Validators.validateCardNumber(''), equals('Card number is required'));
        expect(Validators.validateCardNumber(null), equals('Card number is required'));
        expect(Validators.validateCardNumber('123'), equals('Card number must be 13-19 digits'));
        expect(Validators.validateCardNumber('12345678901234567890'), equals('Card number must be 13-19 digits'));
        expect(Validators.validateCardNumber('****************'), equals('Invalid card number')); // Fails Luhn
        expect(Validators.validateCardNumber('1234567890123456'), equals('Invalid card number')); // Fails Luhn
      });
    });

    group('validateExpiryDate', () {
      test('should return null for valid expiry dates', () {
        // Future dates
        expect(Validators.validateExpiryDate('1225'), isNull); // December 2025
        expect(Validators.validateExpiryDate('0626'), isNull); // June 2026
        expect(Validators.validateExpiryDate('12/25'), isNull); // With slash
        expect(Validators.validateExpiryDate('06/26'), isNull); // With slash
      });

      test('should return error for invalid expiry dates', () {
        expect(Validators.validateExpiryDate(''), equals('Expiry date is required'));
        expect(Validators.validateExpiryDate(null), equals('Expiry date is required'));
        expect(Validators.validateExpiryDate('123'), equals('Enter MM/YY format'));
        expect(Validators.validateExpiryDate('12345'), equals('Enter MM/YY format'));
        expect(Validators.validateExpiryDate('1322'), equals('Invalid month'));
        expect(Validators.validateExpiryDate('0022'), equals('Invalid month'));
        expect(Validators.validateExpiryDate('1220'), equals('Card has expired')); // Past date
        expect(Validators.validateExpiryDate('0121'), equals('Card has expired')); // Past date
      });
    });

    group('validateSecurityCode', () {
      test('should return null for valid security codes', () {
        expect(Validators.validateSecurityCode('123'), isNull);
        expect(Validators.validateSecurityCode('1234'), isNull); // Amex
        expect(Validators.validateSecurityCode('000'), isNull);
        expect(Validators.validateSecurityCode('9999'), isNull);
      });

      test('should return error for invalid security codes', () {
        expect(Validators.validateSecurityCode(''), equals('Security code is required'));
        expect(Validators.validateSecurityCode(null), equals('Security code is required'));
        expect(Validators.validateSecurityCode('12'), equals('Security code must be 3-4 digits'));
        expect(Validators.validateSecurityCode('12345'), equals('Security code must be 3-4 digits'));
        expect(Validators.validateSecurityCode('abc'), equals('Security code must be 3-4 digits'));
      });
    });

    group('validateNameOnCard', () {
      test('should return null for valid names', () {
        expect(Validators.validateNameOnCard('John Doe'), isNull);
        expect(Validators.validateNameOnCard('Mary Jane Smith'), isNull);
        expect(Validators.validateNameOnCard("O'Connor"), isNull);
        expect(Validators.validateNameOnCard('Jean-Pierre'), isNull);
        expect(Validators.validateNameOnCard('A B'), isNull); // Minimum length
      });

      test('should return error for invalid names', () {
        expect(Validators.validateNameOnCard(''), equals('Name on card is required'));
        expect(Validators.validateNameOnCard(null), equals('Name on card is required'));
        expect(Validators.validateNameOnCard('A'), equals('Name must be at least 2 characters'));
        expect(Validators.validateNameOnCard('John123'), equals('Name can only contain letters, spaces, hyphens, and apostrophes'));
        expect(Validators.validateNameOnCard('John@Doe'), equals('Name can only contain letters, spaces, hyphens, and apostrophes'));
      });
    });

    group('isCardFormValid', () {
      test('should return true when all fields are valid', () {
        expect(Validators.isCardFormValid(
          cardNumber: '****************',
          expiryDate: '1225',
          securityCode: '123',
          nameOnCard: 'John Doe',
        ), isTrue);
      });

      test('should return false when any field is invalid', () {
        expect(Validators.isCardFormValid(
          cardNumber: '****************', // Invalid
          expiryDate: '1225',
          securityCode: '123',
          nameOnCard: 'John Doe',
        ), isFalse);

        expect(Validators.isCardFormValid(
          cardNumber: '****************',
          expiryDate: '1320', // Invalid month
          securityCode: '123',
          nameOnCard: 'John Doe',
        ), isFalse);

        expect(Validators.isCardFormValid(
          cardNumber: '****************',
          expiryDate: '1225',
          securityCode: '12', // Too short
          nameOnCard: 'John Doe',
        ), isFalse);

        expect(Validators.isCardFormValid(
          cardNumber: '****************',
          expiryDate: '1225',
          securityCode: '123',
          nameOnCard: '', // Empty
        ), isFalse);
      });
    });
  });
}
