import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../l10n/app_localizations.dart';
import '../../domain/entities/legal_document.dart';
import '../../domain/entities/legal_document_section.dart';
import '../providers/legal_documents_providers.dart';

/// Widget that displays the content of a legal document
class LegalDocumentContent extends ConsumerWidget {
  const LegalDocumentContent({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final document = ref.watch(currentLegalDocumentProvider);

    if (document == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Semantics(
      label: '${document.title} content',
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Document header
            _buildDocumentHeader(context, document),
            const SizedBox(height: 24),

            // Document sections
            ...document.sections.map((section) => _buildSection(section)),

            // Bottom padding for better scrolling experience
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentHeader(BuildContext context, LegalDocument document) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          document.title,
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF111827),
            height: 1.2,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.of(context).lastUpdated(document.lastUpdated),
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF6B7280),
          ),
        ),
      ],
    );
  }

  Widget _buildSection(LegalDocumentSection section) {
    return Semantics(
      label: 'Section: ${section.title}',
      child: Container(
        margin: const EdgeInsets.only(bottom: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section title
            Semantics(
              header: true,
              child: Text(
                section.title,
                style: GoogleFonts.inter(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF111827),
                  height: 1.3,
                ),
              ),
            ),
            const SizedBox(height: 12),

            // Section content
            Text(
              section.content,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF374151),
                height: 1.6,
              ),
            ),

            // Subsections if any
            if (section.hasSubsections) ...[
              const SizedBox(height: 16),
              ...section.subsections!.map((subsection) => _buildSubsection(subsection)),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSubsection(LegalDocumentSection subsection) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16, left: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            subsection.title,
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF111827),
              height: 1.3,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subsection.content,
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: const Color(0xFF374151),
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }
}
