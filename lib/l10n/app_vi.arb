{"appTitle": "TravelGator", "@appTitle": {"description": "The title of the application"}, "welcomeMessage": "Chào mừng đến với TravelGator", "@welcomeMessage": {"description": "Welcome message on the home screen"}, "signIn": "<PERSON><PERSON><PERSON>", "@signIn": {"description": "Text for sign in button"}, "signUp": "<PERSON><PERSON><PERSON>", "@signUp": {"description": "Text for sign up button"}, "email": "Email", "@email": {"description": "Label for email field"}, "password": "<PERSON><PERSON><PERSON>", "@password": {"description": "Label for password field"}, "name": "<PERSON><PERSON><PERSON>", "@name": {"description": "Label for name field"}, "forgotPassword": "<PERSON>u<PERSON><PERSON>?", "@forgotPassword": {"description": "Text for forgot password link"}, "orContinueWith": "hoặc tiếp tục với", "@orContinueWith": {"description": "Text for social login separator"}, "dontHaveAccount": "Bạn chưa có tài k<PERSON>n?", "@dontHaveAccount": {"description": "Text for sign up prompt"}, "alreadyHaveAccount": "Bạn đã có tài k<PERSON>n?", "@alreadyHaveAccount": {"description": "Text for sign in prompt"}, "createAccount": "<PERSON><PERSON><PERSON>", "@createAccount": {"description": "Title for account creation screen"}, "getStarted": "<PERSON><PERSON><PERSON>", "@getStarted": {"description": "Text for get started button"}, "legalDocuments": "<PERSON><PERSON><PERSON>", "@legalDocuments": {"description": "Title for legal documents modal"}, "userAgreement": "Thỏa <PERSON><PERSON><PERSON>n <PERSON>ng", "@userAgreement": {"description": "Title for user agreement document"}, "privacyPolicy": "<PERSON><PERSON><PERSON>", "@privacyPolicy": {"description": "Title for privacy policy document"}, "termsAndConditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON>", "@termsAndConditions": {"description": "Title for terms and conditions document"}, "lastUpdated": "<PERSON><PERSON><PERSON> nh<PERSON>t lần cu<PERSON>: {date}", "@lastUpdated": {"description": "Text showing when document was last updated", "placeholders": {"date": {"type": "String", "description": "The date when the document was last updated"}}}, "close": "Đ<PERSON><PERSON>", "@close": {"description": "Text for close button"}, "languages": "<PERSON><PERSON><PERSON>", "@languages": {"description": "Title for languages screen"}, "selectLanguage": "<PERSON><PERSON><PERSON>", "@selectLanguage": {"description": "Header text for language selection"}, "languageDescription": "<PERSON><PERSON><PERSON> ngôn ngữ ưa thích cho giao diện <PERSON>ng dụng.", "@languageDescription": {"description": "Description text for language selection"}, "languageChangeNote": "<PERSON><PERSON><PERSON>n ứng dụng sẽ cập nhật ngay lập tức khi bạn chọn ngôn ngữ mới.", "@languageChangeNote": {"description": "Note about language change behavior"}, "languageChanged": "<PERSON><PERSON> thay đổi ngôn ngữ thành {language}", "@languageChanged": {"description": "Confirmation message when language is changed", "placeholders": {"language": {"type": "String", "description": "The name of the selected language"}}}, "accountInformation": "Thông Tin T<PERSON>", "@accountInformation": {"description": "Title for account information section"}, "transactionHistory": "<PERSON><PERSON><PERSON>", "@transactionHistory": {"description": "Title for transaction history section"}, "paymentSettings": "Cài Đặt <PERSON>án", "@paymentSettings": {"description": "Title for payment settings section"}, "currency": "<PERSON><PERSON><PERSON><PERSON>", "@currency": {"description": "Title for currency section"}, "helpCenter": "Trung Tâm T<PERSON>ợ <PERSON>", "@helpCenter": {"description": "Title for help center section"}, "logout": "<PERSON><PERSON><PERSON>", "@logout": {"description": "Text for logout button"}, "logoutConfirmTitle": "<PERSON><PERSON><PERSON>", "@logoutConfirmTitle": {"description": "Title for logout confirmation dialog"}, "logoutConfirmMessage": "Bạn có chắc chắn muốn đăng xuất?", "@logoutConfirmMessage": {"description": "Message for logout confirmation dialog"}, "cancel": "<PERSON><PERSON><PERSON>", "@cancel": {"description": "Text for cancel button"}, "appVersion": "TravelGator p<PERSON><PERSON><PERSON> b<PERSON> {version}", "@appVersion": {"description": "App version text", "placeholders": {"version": {"type": "String", "description": "The version number of the app"}}}, "cart": "Giỏ Hàng", "@cart": {"description": "Cart screen title"}, "cartIsEmpty": "Giỏ hàng trống", "@cartIsEmpty": {"description": "Message when cart has no items"}, "addSomeProducts": "<PERSON>hê<PERSON> một số sản phẩm để bắt đầu", "@addSomeProducts": {"description": "Subtitle for empty cart"}, "startShopping": "<PERSON><PERSON><PERSON>", "@startShopping": {"description": "But<PERSON> text to start shopping"}, "authenticationRequired": "<PERSON><PERSON><PERSON>", "@authenticationRequired": {"description": "Title for authentication required error"}, "pleaseSignInToCart": "<PERSON><PERSON> lòng đăng nhập để truy cập giỏ hàng", "@pleaseSignInToCart": {"description": "Message for authentication required"}, "unableToLoadCart": "<PERSON><PERSON><PERSON><PERSON> thể tải giỏ hàng", "@unableToLoadCart": {"description": "Error message when cart fails to load"}, "failedToLoadCart": "<PERSON><PERSON><PERSON><PERSON> thể tải giỏ hàng", "@failedToLoadCart": {"description": "Generic cart loading error message"}, "tryAgain": "<PERSON><PERSON><PERSON>", "@tryAgain": {"description": "Button text to retry an action"}, "total": "Tổng cộng:", "@total": {"description": "Label for total price"}, "addToCart": "<PERSON><PERSON><PERSON><PERSON> Giỏ", "@addToCart": {"description": "Button text to add item to cart"}, "buyNow": "<PERSON><PERSON>", "@buyNow": {"description": "Button text to buy item immediately"}, "hideDetails": "Ẩn chi tiết", "@hideDetails": {"description": "Button text to hide product details"}, "remove": "Xóa", "@remove": {"description": "Button text to remove item"}, "itemRemovedFromCart": "<PERSON><PERSON> xóa sản phẩm khỏi giỏ hàng", "@itemRemovedFromCart": {"description": "Success message when item is removed from cart"}, "noVariantsAvailable": "<PERSON><PERSON><PERSON><PERSON> có biến thể nào cho sản phẩm này", "@noVariantsAvailable": {"description": "Error message when product has no variants"}, "failedToAddToCart": "<PERSON><PERSON><PERSON><PERSON> thể thêm vào giỏ hàng: {error}", "@failedToAddToCart": {"description": "Error message when adding to cart fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "gb": "GB", "@gb": {"description": "Gigabyte unit abbreviation"}, "day": "<PERSON><PERSON><PERSON>", "@day": {"description": "Day unit for duration"}, "days": "<PERSON><PERSON><PERSON>", "@days": {"description": "Days unit for duration (plural)"}, "signedInSuccessfully": "<PERSON><PERSON><PERSON> nhập thành công!", "@signedInSuccessfully": {"description": "Success message when user signs in"}, "signInFailed": "<PERSON><PERSON><PERSON> nhập thất bại: {error}", "@signInFailed": {"description": "Error message when sign-in fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "retry": "<PERSON><PERSON><PERSON> lại", "@retry": {"description": "Button text to retry an action"}, "orderCompletedSuccessfully": "🎉 Đặt hàng thành công!", "@orderCompletedSuccessfully": {"description": "Success message when order is completed"}, "checkoutFailed": "❌ <PERSON>h toán thất bại: {error}", "@checkoutFailed": {"description": "Error message when checkout fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "buyNowFailed": "❌ <PERSON>a ngay thất bại: {error}", "@buyNowFailed": {"description": "Error message when buy now fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "processingCheckout": "<PERSON><PERSON> x<PERSON> lý thanh toán...", "@processingCheckout": {"description": "Message shown while processing checkout"}, "checkOut": "<PERSON><PERSON> toán", "@checkOut": {"description": "Checkout button text"}, "proceedToCheckout": "<PERSON><PERSON><PERSON><PERSON> hành <PERSON>h toán", "@proceedToCheckout": {"description": "Proceed to checkout button text"}, "home": "Trang chủ", "@home": {"description": "Home tab label in navigation bar"}, "account": "<PERSON><PERSON><PERSON>", "@account": {"description": "Account tab label in navigation bar"}, "sims": "SIM", "@sims": {"description": "SIMs tab label in navigation bar"}, "travelgator": "TravelGator", "@travelgator": {"description": "TravelGator tab label and screen title"}, "rewards": "Phần thưởng", "@rewards": {"description": "Rewards tab label in navigation bar"}, "travelgatorScreen": "<PERSON><PERSON><PERSON> h<PERSON> TravelGator", "@travelgatorScreen": {"description": "TravelGator screen content title"}, "welcomeToTravelgator": "Chào mừng đến với\nTravelGator", "@welcomeToTravelgator": {"description": "Welcome title in onboarding"}, "stayConnected": "<PERSON><PERSON><PERSON> kết n<PERSON>i", "@stayConnected": {"description": "Stay connected title in onboarding"}, "stayConnectedGlobally": "<PERSON><PERSON><PERSON> kết n<PERSON>i\ntoàn c<PERSON>u", "@stayConnectedGlobally": {"description": "Stay connected globally title"}, "discoverExplore": "<PERSON><PERSON><PERSON><PERSON> phá & <PERSON><PERSON><PERSON> hiểu\nmột cách dễ dàng", "@discoverExplore": {"description": "Discover and explore title"}, "discoverAmazingDestinations": "<PERSON>h<PERSON>m phá những\nđiểm đến tuyệt vời", "@discoverAmazingDestinations": {"description": "Discover amazing destinations title"}, "details": "<PERSON> ti<PERSON>", "@details": {"description": "Details screen title"}, "yourPurchase": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>a b<PERSON>n", "@yourPurchase": {"description": "Your purchase section title"}, "yourData": "<PERSON><PERSON> liệu c<PERSON>a bạn", "@yourData": {"description": "Your data section title"}, "newESim": "eSIM mớ<PERSON>", "@newESim": {"description": "New eSIM option label"}, "topUp": "<PERSON><PERSON><PERSON> t<PERSON>", "@topUp": {"description": "Top up option label"}, "totalPayment": "<PERSON><PERSON>ng thanh toán", "@totalPayment": {"description": "Total payment label"}, "seeDetails": "<PERSON>em chi tiết", "@seeDetails": {"description": "See details button text"}, "selectYourTravelgatorESim": "Chọn eSIM TravelGator c<PERSON><PERSON> bạn", "@selectYourTravelgatorESim": {"description": "Default option for eSIM selection"}, "dataAmount": "<PERSON><PERSON><PERSON><PERSON> dữ liệu", "@dataAmount": {"description": "Data amount selection label"}, "duration": "<PERSON><PERSON><PERSON><PERSON> hạn", "@duration": {"description": "Duration selection label"}, "quantity": "Số lượng", "@quantity": {"description": "Quantity selection label"}, "eSimType": "Loại eSIM", "@eSimType": {"description": "eSIM type selection label"}, "reviewYourPurchase": "<PERSON>em lại giao d<PERSON>ch", "@reviewYourPurchase": {"description": "Title for purchase review popup"}, "yourDataLabel": "<PERSON><PERSON> liệu c<PERSON>a bạn", "@yourDataLabel": {"description": "Label for data amount dropdown"}, "dayLabel": "<PERSON><PERSON><PERSON>", "@dayLabel": {"description": "Label for day duration dropdown"}, "oneDay": "1 ngày", "@oneDay": {"description": "One day option"}, "threeDays": "3 ngày", "@threeDays": {"description": "Three days option"}, "sevenDays": "7 ngày", "@sevenDays": {"description": "Seven days option"}, "fifteenDays": "15 ngày", "@fifteenDays": {"description": "Fifteen days option"}, "thirtyDays": "30 ngày", "@thirtyDays": {"description": "Thirty days option"}, "removeItemConfirmation": "Bạn có chắc chắn muốn xóa \"{itemTitle}\" khỏi giỏ hàng?", "@removeItemConfirmation": {"description": "Confirmation message for removing item from cart", "placeholders": {"itemTitle": {"type": "String", "description": "The title of the item being removed"}}}, "checkoutFailedTitle": "<PERSON><PERSON> to<PERSON> thất bại", "@checkoutFailedTitle": {"description": "Title for checkout failed error"}, "checkoutFailedMessage": "<PERSON><PERSON> vấn đề khi xử lý thanh toán của bạn. <PERSON><PERSON><PERSON> có thể là vấn đề cấu hình backend với Shopify.", "@checkoutFailedMessage": {"description": "Detailed message for checkout failure"}, "checkoutErrorTitle": "Lỗi thanh toán", "@checkoutErrorTitle": {"description": "Title for checkout error"}, "deleteMyAccount": "<PERSON><PERSON><PERSON>", "@deleteMyAccount": {"description": "Text for delete account button"}, "deleteAccountConfirmTitle": "<PERSON><PERSON><PERSON>", "@deleteAccountConfirmTitle": {"description": "Title for delete account confirmation dialog"}, "deleteAccountConfirmMessage": "Bạn có chắc chắn muốn xóa tài khoản? Điều này sẽ xóa vĩnh viễn tất cả dữ liệu của bạn khỏi hệ thống.", "@deleteAccountConfirmMessage": {"description": "Message for delete account confirmation dialog"}, "delete": "Xóa", "@delete": {"description": "Text for delete button"}, "accountDeletionFailed": "<PERSON><PERSON><PERSON> tài k<PERSON>n thất bại", "@accountDeletionFailed": {"description": "Error message when account deletion fails"}, "accountDeletedSuccessfully": "<PERSON><PERSON><PERSON> tài kho<PERSON>n thành công", "@accountDeletedSuccessfully": {"description": "Success message when account is deleted"}, "cannotDeleteAccount": "<PERSON><PERSON><PERSON><PERSON> thể xóa tài k<PERSON>n", "@cannotDeleteAccount": {"description": "Error message when account cannot be deleted"}, "checkingAccountDeletion": "<PERSON><PERSON> kiểm tra khả năng xóa tài k<PERSON>n...", "@checkingAccountDeletion": {"description": "Loading message when checking account deletion eligibility"}, "deletingAccount": "<PERSON><PERSON> x<PERSON>a tà<PERSON>...", "@deletingAccount": {"description": "Loading message when deleting account"}, "serverErrorContactSupport": "Lỗi máy chủ xảy ra. <PERSON><PERSON> lòng thử lại sau hoặc liên hệ hỗ trợ để được trợ giúp.", "@serverErrorContactSupport": {"description": "Error message for server errors during account deletion"}, "errorLoadingProducts": "Lỗi tải sản phẩm", "@errorLoadingProducts": {"description": "Error message when products fail to load"}, "noProductsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm", "@noProductsFound": {"description": "Message when no products match the filter"}, "tryAdjustingFilter": "<PERSON>h<PERSON> điều chỉnh bộ lọc của bạn", "@tryAdjustingFilter": {"description": "Suggestion when no products are found"}, "productAddedToCart": "{productTitle} đã được thêm vào giỏ hàng!", "@productAddedToCart": {"description": "Success message when product is added to cart", "placeholders": {"productTitle": {"type": "String", "description": "The title of the product added"}}}, "cartOperationMayHaveFailed": "<PERSON><PERSON> tác giỏ hàng có thể đã thất bại. <PERSON><PERSON> lòng kiểm tra giỏ hàng của bạn.", "@cartOperationMayHaveFailed": {"description": "Warning message when cart operation status is unclear"}, "processingAddToCart": "<PERSON><PERSON> thêm vào giỏ hàng...", "@processingAddToCart": {"description": "Loading message when adding item to cart"}, "from": "Từ", "@from": {"description": "Prefix for price display, e.g. 'From $10.99'"}, "minPrice": "<PERSON><PERSON><PERSON> tối thiểu", "@minPrice": {"description": "Label for minimum price filter"}, "maxPrice": "<PERSON><PERSON><PERSON> tối đa", "@maxPrice": {"description": "Label for maximum price filter"}, "applyFilter": "<PERSON><PERSON> d<PERSON> bộ lọc", "@applyFilter": {"description": "Button text to apply price filter"}, "clearFilter": "Xóa bộ lọc", "@clearFilter": {"description": "Button text to clear price filter"}, "priceFilter": "Bộ lọc giá", "@priceFilter": {"description": "Title for price filter section"}, "under": "Dư<PERSON><PERSON>", "@under": {"description": "Prefix for price range, e.g. 'Under $5'"}, "over": "<PERSON><PERSON><PERSON><PERSON>", "@over": {"description": "Prefix for price range, e.g. 'Over $50'"}, "orSetCustomRange": "Hoặc đặt khoảng tùy chỉnh:", "@orSetCustomRange": {"description": "Label for custom price range section"}, "to": "<PERSON><PERSON><PERSON>", "@to": {"description": "Connector word between min and max price, e.g. '$5 to $10'"}, "countryVietnam": "Việt Nam", "@countryVietnam": {"description": "Country name: Vietnam"}, "countryChina": "<PERSON><PERSON>", "@countryChina": {"description": "Country name: China"}, "countryUnitedStates": "<PERSON>a Kỳ", "@countryUnitedStates": {"description": "Country name: United States"}, "countryNewZealand": "New Zealand", "@countryNewZealand": {"description": "Country name: New Zealand"}, "regionAsean": "ASEAN", "@regionAsean": {"description": "Region name: ASEAN"}, "regionEastAsia": "Đông <PERSON>", "@regionEastAsia": {"description": "Region name: East Asia"}, "regionNorthAmerica": "<PERSON><PERSON><PERSON>", "@regionNorthAmerica": {"description": "Region name: North America"}, "regionOceania": "<PERSON><PERSON><PERSON>", "@regionOceania": {"description": "Region name: Oceania"}, "eSIMRoamingData": "Dữ liệu eSIM Roaming", "@eSIMRoamingData": {"description": "Description text for eSIM products"}, "countries": "Quốc gia", "@countries": {"description": "Tab label for countries filter"}, "regions": "<PERSON><PERSON> v<PERSON>", "@regions": {"description": "Tab label for regions filter"}, "authenticationSuccessful": "<PERSON><PERSON><PERSON> thực thành công!", "@authenticationSuccessful": {"description": "Message shown when user successfully authenticates"}, "paymentSuccessful": "🎉 Thanh toán thành công!", "@paymentSuccessful": {"description": "Success message when payment is completed"}, "paymentCanceled": "<PERSON><PERSON> <PERSON><PERSON> đã bị hủy", "@paymentCanceled": {"description": "Message when payment is canceled by user"}, "paymentFailed": "<PERSON><PERSON> to<PERSON> thất bại", "@paymentFailed": {"description": "Generic payment failure message"}, "paymentErrorUnknown": "Lỗi thanh toán không xác định", "@paymentErrorUnknown": {"description": "Message for unknown payment errors"}, "paymentIntentCreationFailed": "<PERSON><PERSON><PERSON><PERSON> thể tạo yêu cầu thanh toán. <PERSON><PERSON> lòng thử lại.", "@paymentIntentCreationFailed": {"description": "Error when payment intent creation fails"}, "paymentVerificationFailed": "<PERSON><PERSON><PERSON> minh thanh toán thất bại. <PERSON><PERSON> lòng liên hệ hỗ trợ.", "@paymentVerificationFailed": {"description": "Error when backend payment verification fails"}, "paymentSucceededOrderFailed": "<PERSON><PERSON> toán thành công nhưng không thể hoàn tất đơn hàng. <PERSON><PERSON> lòng liên hệ hỗ trợ.", "@paymentSucceededOrderFailed": {"description": "Error when payment succeeds but order completion fails"}, "backendSystemError": "Lỗi hệ thống backend. <PERSON><PERSON> lòng liên hệ hỗ trợ kỹ thuật.", "@backendSystemError": {"description": "Error for backend system issues"}}