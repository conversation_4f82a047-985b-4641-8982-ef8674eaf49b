import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/order_providers.dart';
import '../widgets/order_card.dart';
import '../widgets/product_detail_card.dart';
import '../widgets/pricing_breakdown.dart';
import '../../../auth/presentation/services/auth_guard_service.dart';

class TransactionDetailScreen extends ConsumerStatefulWidget {
  final String orderId;

  const TransactionDetailScreen({
    super.key,
    required this.orderId,
  });

  @override
  ConsumerState<TransactionDetailScreen> createState() => _TransactionDetailScreenState();
}

class _TransactionDetailScreenState extends ConsumerState<TransactionDetailScreen> {
  @override
  void initState() {
    super.initState();
    // Set loading state immediately to show spinner
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Set loading state immediately
      ref.read(orderProvider(widget.orderId).notifier).setLoading(true);

      // Check if user has backend authentication
      final hasAuth = await AuthGuardService.requireBackendAuthentication(
        context,
        ref,
        title: 'Authentication Required',
        subtitle: 'Please authenticate to view transaction details',
        onSuccess: () {
          // Fetch order after successful authentication
          ref.read(orderProvider(widget.orderId).notifier).fetchOrder();
        },
        onError: (error) {
          // Handle authentication error and clear loading
          ref.read(orderProvider(widget.orderId).notifier).setError('Authentication failed: $error');

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Authentication failed: $error'),
              backgroundColor: Colors.red,
            ),
          );
        },
      );

      // If already authenticated, fetch order immediately
      if (hasAuth) {
        ref.read(orderProvider(widget.orderId).notifier).fetchOrder();
      } else {
        // If authentication failed, clear loading state
        ref.read(orderProvider(widget.orderId).notifier).setLoading(false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final orderState = ref.watch(orderProvider(widget.orderId));

    return Scaffold(
      backgroundColor: const Color(0xFFF3F4F6),
      appBar: _buildAppBar(context),
      body: _buildBody(orderState),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: const Color(0xFFFF6982),
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: const Text(
        'Transaction details',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildBody(OrderState orderState) {
    if (orderState.isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              color: Color(0xFFFF6982),
            ),
            const SizedBox(height: 16),
            Text(
              'Loading transaction details...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    if (orderState.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading transaction',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              orderState.error!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.read(orderProvider(widget.orderId).notifier).fetchOrder(),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF6982),
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (orderState.order == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Transaction not found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    final order = orderState.order!;

    return Column(
      children: [
        // Scrollable content
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Order information card
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: OrderCard(
                    order: order,
                    onTap: null, // Disable tap on detail screen
                  ),
                ),

                // Product details
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16), // Consistent margin
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: order.items.map((item) {
                      return ProductDetailCard(
                        orderItem: item,
                        isLast: item == order.items.last,
                      );
                    }).toList(),
                  ),
                ),

                // Add bottom padding to ensure content doesn't get hidden behind fixed bottom
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),

        // Fixed bottom pricing breakdown
        PricingBreakdown(order: order),
      ],
    );
  }
}
