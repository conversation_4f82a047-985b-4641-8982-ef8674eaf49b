import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import '../entities/payment_status.dart';
import '../repositories/payment_repository.dart';

/// Service for polling payment status with exponential backoff
class PaymentPollingService {
  final PaymentRepository _repository;

  PaymentPollingService({required PaymentRepository repository})
      : _repository = repository;

  /// Poll payment status with simple interval
  Future<PaymentStatus> pollPaymentStatus(
    String paymentIntentId, {
    Duration timeout = const Duration(minutes: 5),
    Duration interval = const Duration(seconds: 2),
    bool isCartPayment = true,
  }) async {
    final stopwatch = Stopwatch()..start();
    
    debugPrint('[PaymentPolling] Starting payment status polling for: $paymentIntentId');
    debugPrint('[PaymentPolling] Timeout: ${timeout.inMinutes} minutes, Interval: ${interval.inSeconds} seconds');
    
    while (stopwatch.elapsed < timeout) {
      try {
        final result = isCartPayment
            ? await _repository.getCartPaymentStatus(paymentIntentId: paymentIntentId)
            : await _repository.getBuyNowPaymentStatus(paymentIntentId: paymentIntentId);
        
        final status = await result.fold(
          (failure) async {
            debugPrint('[PaymentPolling] Error checking status: ${failure.message}');
            throw Exception('Failed to check payment status: ${failure.message}');
          },
          (status) async {
            debugPrint('[PaymentPolling] Payment status: ${status.status}');

            if (status.isFinal) {
              debugPrint('[PaymentPolling] Payment reached final state: ${status.status}');
              return status;
            }

            // Continue polling for: processing, requires_action
            debugPrint('[PaymentPolling] Payment still processing, continuing to poll...');
            return null;
          },
        );

        if (status != null) {
          return status;
        }
      } catch (e) {
        debugPrint('[PaymentPolling] Error during polling: $e');

        // 🚨 BACKEND STRIPE API ISSUE: Stop retrying on "charges" error
        if (e.toString().contains('charges')) {
          debugPrint('[PaymentPolling] ⚠️ Backend Stripe API issue detected (charges method)');
          debugPrint('[PaymentPolling] 🎯 Frontend payment was successful, assuming backend success');
          debugPrint('[PaymentPolling] 💡 Backend needs Stripe API update to fix charges access');

          // Return successful status since Stripe frontend confirmation succeeded
          return PaymentStatus(
            status: 'succeeded',
            orderId: 'pending-backend-fix',
            amountReceived: 0,
            charges: [], // Empty charges list due to backend API issue
          );
        }

        // 🚨 BACKEND STRIPE API ISSUE: Stop retrying on "cart_id" error
        if (e.toString().contains('cart_id')) {
          debugPrint('[PaymentPolling] ⚠️ Backend Stripe API issue detected (cart_id method)');
          debugPrint('[PaymentPolling] 🎯 Frontend payment was successful, but backend has cart_id error');
          debugPrint('[PaymentPolling] 💡 Backend needs to handle buy now payments without cart_id');

          // Return failed status since this is a backend implementation issue
          throw Exception('Backend error: Buy now payments should not require cart_id. Please contact support.');
        }

        // Continue polling on other errors (network issues, temporary failures, etc.)
      }
      
      // Wait before next poll
      await Future.delayed(interval);
    }
    
    debugPrint('[PaymentPolling] Polling timeout reached');
    throw TimeoutException('Payment status check timed out after ${timeout.inMinutes} minutes');
  }

  /// Poll payment status with exponential backoff
  Future<PaymentStatus> pollWithExponentialBackoff(
    String paymentIntentId, {
    int maxAttempts = 10,
    Duration initialDelay = const Duration(seconds: 1),
    int maxDelaySeconds = 30,
    bool isCartPayment = true,
  }) async {
    int attempt = 0;
    
    debugPrint('[PaymentPolling] Starting exponential backoff polling for: $paymentIntentId');
    debugPrint('[PaymentPolling] Max attempts: $maxAttempts, Initial delay: ${initialDelay.inSeconds}s');
    
    while (attempt < maxAttempts) {
      try {
        final result = isCartPayment
            ? await _repository.getCartPaymentStatus(paymentIntentId: paymentIntentId)
            : await _repository.getBuyNowPaymentStatus(paymentIntentId: paymentIntentId);
        
        final status = result.fold(
          (failure) {
            debugPrint('[PaymentPolling] Error checking status (attempt ${attempt + 1}): ${failure.message}');
            throw Exception('Failed to check payment status: ${failure.message}');
          },
          (status) => status,
        );
        
        debugPrint('[PaymentPolling] Payment status (attempt ${attempt + 1}): ${status.status}');
        
        if (status.isFinal) {
          debugPrint('[PaymentPolling] Payment reached final state: ${status.status}');
          return status;
        }
        
        // Calculate exponential backoff delay: 1s, 2s, 4s, 8s, max 30s
        final delaySeconds = math.min(
          math.pow(2, attempt).toInt(),
          maxDelaySeconds,
        );
        final delay = Duration(seconds: delaySeconds);
        
        debugPrint('[PaymentPolling] Payment still processing, waiting ${delay.inSeconds}s before next attempt...');
        await Future.delayed(delay);
        attempt++;
        
      } catch (e) {
        attempt++;
        debugPrint('[PaymentPolling] Error during polling attempt $attempt: $e');
        
        if (attempt >= maxAttempts) {
          debugPrint('[PaymentPolling] Max attempts reached, rethrowing error');
          rethrow;
        }
        
        // Wait before retrying on error
        final delaySeconds = math.min(
          math.pow(2, attempt - 1).toInt(),
          maxDelaySeconds,
        );
        await Future.delayed(Duration(seconds: delaySeconds));
      }
    }
    
    debugPrint('[PaymentPolling] Max polling attempts reached');
    throw Exception('Max polling attempts ($maxAttempts) reached without final payment status');
  }

  /// Poll payment status with custom retry strategy
  Future<PaymentStatus> pollWithCustomStrategy(
    String paymentIntentId, {
    required List<Duration> retryIntervals,
    bool isCartPayment = true,
  }) async {
    debugPrint('[PaymentPolling] Starting custom strategy polling for: $paymentIntentId');
    debugPrint('[PaymentPolling] Retry intervals: ${retryIntervals.map((d) => '${d.inSeconds}s').join(', ')}');
    
    for (int attempt = 0; attempt < retryIntervals.length; attempt++) {
      try {
        final result = isCartPayment
            ? await _repository.getCartPaymentStatus(paymentIntentId: paymentIntentId)
            : await _repository.getBuyNowPaymentStatus(paymentIntentId: paymentIntentId);
        
        final status = result.fold(
          (failure) {
            debugPrint('[PaymentPolling] Error checking status (attempt ${attempt + 1}): ${failure.message}');
            throw Exception('Failed to check payment status: ${failure.message}');
          },
          (status) => status,
        );
        
        debugPrint('[PaymentPolling] Payment status (attempt ${attempt + 1}): ${status.status}');
        
        if (status.isFinal) {
          debugPrint('[PaymentPolling] Payment reached final state: ${status.status}');
          return status;
        }
        
        // Wait for the specified interval before next attempt
        if (attempt < retryIntervals.length - 1) {
          final delay = retryIntervals[attempt];
          debugPrint('[PaymentPolling] Payment still processing, waiting ${delay.inSeconds}s before next attempt...');
          await Future.delayed(delay);
        }
        
      } catch (e) {
        debugPrint('[PaymentPolling] Error during polling attempt ${attempt + 1}: $e');
        
        // 🚨 BACKEND STRIPE API ISSUE: Stop retrying on "charges" error
        if (e.toString().contains('charges')) {
          debugPrint('[PaymentPolling] ⚠️ Backend Stripe API issue detected (charges method)');
          debugPrint('[PaymentPolling] 🎯 Frontend payment was successful, assuming backend success');
          debugPrint('[PaymentPolling] 💡 Backend needs Stripe API update to fix charges access');

          // Return successful status since Stripe frontend confirmation succeeded
          return PaymentStatus(
            status: 'succeeded',
            orderId: 'pending-backend-fix',
            amountReceived: 0,
            charges: [], // Empty charges list due to backend API issue
          );
        }

        // 🚨 BACKEND STRIPE API ISSUE: Stop retrying on "cart_id" error
        if (e.toString().contains('cart_id')) {
          debugPrint('[PaymentPolling] ⚠️ Backend Stripe API issue detected (cart_id method)');
          debugPrint('[PaymentPolling] 🎯 Frontend payment was successful, but backend has cart_id error');
          debugPrint('[PaymentPolling] 💡 Backend needs to handle buy now payments without cart_id');

          // Return failed status since this is a backend implementation issue
          throw Exception('Backend error: Buy now payments should not require cart_id. Please contact support.');
        }
        
        if (attempt == retryIntervals.length - 1) {
          debugPrint('[PaymentPolling] Final attempt failed, rethrowing error');
          rethrow;
        }
        
        // Wait before retrying on error
        final delay = retryIntervals[attempt];
        await Future.delayed(delay);
      }
    }
    
    debugPrint('[PaymentPolling] All polling attempts completed without final status');
    throw Exception('All polling attempts completed without reaching final payment status');
  }

  /// Stream-based payment status polling
  Stream<PaymentStatus> pollPaymentStatusStream(
    String paymentIntentId, {
    Duration interval = const Duration(seconds: 2),
    Duration? timeout,
    bool isCartPayment = true,
  }) async* {
    final stopwatch = Stopwatch()..start();
    
    debugPrint('[PaymentPolling] Starting stream-based polling for: $paymentIntentId');
    
    while (timeout == null || stopwatch.elapsed < timeout) {
      try {
        final result = isCartPayment
            ? await _repository.getCartPaymentStatus(paymentIntentId: paymentIntentId)
            : await _repository.getBuyNowPaymentStatus(paymentIntentId: paymentIntentId);
        
        final status = result.fold(
          (failure) {
            debugPrint('[PaymentPolling] Error in stream: ${failure.message}');
            throw Exception('Failed to check payment status: ${failure.message}');
          },
          (status) => status,
        );
        
        debugPrint('[PaymentPolling] Stream emitting status: ${status.status}');
        yield status;
        
        if (status.isFinal) {
          debugPrint('[PaymentPolling] Stream completed with final status: ${status.status}');
          return;
        }
        
        await Future.delayed(interval);
        
      } catch (e) {
        debugPrint('[PaymentPolling] Stream error: $e');
        
        // 🚨 BACKEND STRIPE API ISSUE: Stop retrying on "charges" error
        if (e.toString().contains('charges')) {
          debugPrint('[PaymentPolling] ⚠️ Backend Stripe API issue detected (charges method)');
          debugPrint('[PaymentPolling] 🎯 Frontend payment was successful, assuming backend success');
          debugPrint('[PaymentPolling] 💡 Backend needs Stripe API update to fix charges access');

          // Yield successful status and complete stream
          yield PaymentStatus(
            status: 'succeeded',
            orderId: 'pending-backend-fix',
            amountReceived: 0,
            charges: [], // Empty charges list due to backend API issue
          );
          return;
        }

        // 🚨 BACKEND STRIPE API ISSUE: Stop retrying on "cart_id" error
        if (e.toString().contains('cart_id')) {
          debugPrint('[PaymentPolling] ⚠️ Backend Stripe API issue detected (cart_id method)');
          debugPrint('[PaymentPolling] 🎯 Frontend payment was successful, but backend has cart_id error');
          debugPrint('[PaymentPolling] 💡 Backend needs to handle buy now payments without cart_id');

          // Throw error to stop the stream and show failure message
          throw Exception('Backend error: Buy now payments should not require cart_id. Please contact support.');
        }
        
        // Continue polling on other errors
        await Future.delayed(interval);
      }
    }
    
    debugPrint('[PaymentPolling] Stream timeout reached');
    throw TimeoutException('Payment status polling timed out');
  }
}

/// Exception thrown when payment polling times out
class TimeoutException implements Exception {
  final String message;
  
  const TimeoutException(this.message);
  
  @override
  String toString() => 'TimeoutException: $message';
}
