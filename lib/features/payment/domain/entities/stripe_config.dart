import 'package:equatable/equatable.dart';

/// Stripe configuration entity containing publishable key and settings
class StripeConfig extends Equatable {
  final String publishableKey;
  final String merchantId;
  final String merchantDisplayName;
  final String countryCode;
  final String currency;
  final bool testMode;
  final Map<String, dynamic>? additionalSettings;

  const StripeConfig({
    required this.publishableKey,
    this.merchantId = '',
    this.merchantDisplayName = 'TravelGator',
    this.countryCode = 'US',
    this.currency = 'USD',
    this.testMode = true,
    this.additionalSettings,
  });

  @override
  List<Object?> get props => [
        publishableKey,
        merchantId,
        merchantDisplayName,
        countryCode,
        currency,
        testMode,
        additionalSettings,
      ];

  @override
  String toString() {
    return 'StripeConfig(publishableKey: ${publishableKey.substring(0, 10)}..., '
        'merchantId: $merchantId, merchantDisplayName: $merchantDisplayName, '
        'countryCode: $countryCode, currency: $currency, testMode: $testMode)';
  }
}
