import '../../domain/entities/payment_status.dart';

/// Data model for payment status response from backend
class PaymentStatusModel {
  final String status;
  final String orderId;
  final int amountReceived;
  final List<ChargeModel> charges;

  const PaymentStatusModel({
    required this.status,
    required this.orderId,
    required this.amountReceived,
    required this.charges,
  });

  /// Create from JSON response
  factory PaymentStatusModel.fromJson(Map<String, dynamic> json) {
    return PaymentStatusModel(
      status: json['status'] as String,
      orderId: json['order_id'] as String,
      amountReceived: json['amount_received'] as int,
      charges: (json['charges'] as List<dynamic>?)
          ?.map((charge) => ChargeModel.fromJson(charge as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'order_id': orderId,
      'amount_received': amountReceived,
      'charges': charges.map((charge) => charge.toJson()).toList(),
    };
  }

  /// Convert to domain entity
  PaymentStatus toDomain() {
    return PaymentStatus(
      status: status,
      orderId: orderId,
      amountReceived: amountReceived,
      charges: charges.map((charge) => charge.toDomain()).toList(),
    );
  }

  @override
  String toString() {
    return 'PaymentStatusModel(status: $status, orderId: $orderId, amountReceived: $amountReceived, charges: $charges)';
  }
}

/// Data model for charge information
class ChargeModel {
  final String id;
  final int amount;
  final String status;
  final int created;
  final String? receiptUrl;

  const ChargeModel({
    required this.id,
    required this.amount,
    required this.status,
    required this.created,
    this.receiptUrl,
  });

  /// Create from JSON response
  factory ChargeModel.fromJson(Map<String, dynamic> json) {
    return ChargeModel(
      id: json['id'] as String,
      amount: json['amount'] as int,
      status: json['status'] as String,
      created: json['created'] as int,
      receiptUrl: json['receipt_url'] as String?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'status': status,
      'created': created,
      'receipt_url': receiptUrl,
    };
  }

  /// Convert to domain entity
  Charge toDomain() {
    return Charge(
      id: id,
      amount: amount,
      status: status,
      created: created,
      receiptUrl: receiptUrl,
    );
  }

  @override
  String toString() {
    return 'ChargeModel(id: $id, amount: $amount, status: $status, created: $created, receiptUrl: $receiptUrl)';
  }
}
