import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/cart_provider.dart';
import '../../domain/entities/checkout_item.dart';

/// Modern cart service that leverages the unified checkout endpoint
/// Follows the TravelGator Cart API client implementation guide
class UnifiedCartService {
  final WidgetRef ref;
  final BuildContext context;

  UnifiedCartService(this.ref, this.context);

  /// Checkout with custom items and quantities (Priority 1)
  /// This is the highest priority method in the unified endpoint
  Future<String?> checkoutWithCustomQuantities(List<CheckoutItem> items) async {
    debugPrint('[UnifiedCartService] Checkout with custom quantities: ${items.length} items');
    
    try {
      final result = await ref.read(cartProvider.notifier).checkoutSelectedWithQuantities(
        items: items,
      );
      
      if (result != null) {
        debugPrint('[UnifiedCartService] Custom quantities checkout successful');
        return result;
      } else {
        debugPrint('[UnifiedCartService] Custom quantities checkout failed - no result');
        return null;
      }
    } catch (e) {
      debugPrint('[UnifiedCartService] Custom quantities checkout error: $e');
      rethrow;
    }
  }

  /// Checkout selected variant IDs with their current cart quantities (Priority 2)
  Future<String?> checkoutSelectedVariants(List<String> variantIds) async {
    debugPrint('[UnifiedCartService] Checkout selected variants: ${variantIds.join(', ')}');
    
    try {
      final result = await ref.read(cartProvider.notifier).checkoutSelected(
        selectedVariantIds: variantIds,
      );
      
      if (result != null) {
        debugPrint('[UnifiedCartService] Selected variants checkout successful');
        return result;
      } else {
        debugPrint('[UnifiedCartService] Selected variants checkout failed - no result');
        return null;
      }
    } catch (e) {
      debugPrint('[UnifiedCartService] Selected variants checkout error: $e');
      rethrow;
    }
  }

  /// Checkout all items in cart (Priority 5 - lowest priority)
  Future<String?> checkoutAll() async {
    debugPrint('[UnifiedCartService] Checkout all items');
    
    try {
      final result = await ref.read(cartProvider.notifier).checkout();
      
      if (result != null) {
        debugPrint('[UnifiedCartService] Checkout all successful');
        return result;
      } else {
        debugPrint('[UnifiedCartService] Checkout all failed - no result');
        return null;
      }
    } catch (e) {
      debugPrint('[UnifiedCartService] Checkout all error: $e');
      rethrow;
    }
  }

  /// Smart checkout method that automatically chooses the best approach
  /// based on the current cart state and user selection
  Future<String?> smartCheckout({
    List<String>? selectedVariantIds,
    List<CheckoutItem>? customItems,
  }) async {
    debugPrint('[UnifiedCartService] Smart checkout initiated');
    
    // Priority 1: Custom items with quantities
    if (customItems != null && customItems.isNotEmpty) {
      debugPrint('[UnifiedCartService] Using custom quantities approach');
      return await checkoutWithCustomQuantities(customItems);
    }
    
    // Priority 2: Selected variant IDs
    if (selectedVariantIds != null && selectedVariantIds.isNotEmpty) {
      debugPrint('[UnifiedCartService] Using selected variants approach');
      return await checkoutSelectedVariants(selectedVariantIds);
    }
    
    // Priority 5: Checkout all items (fallback)
    debugPrint('[UnifiedCartService] Using checkout all approach (fallback)');
    return await checkoutAll();
  }

  /// Convert frontend selection to CheckoutItem list for custom quantities
  List<CheckoutItem> createCheckoutItemsFromSelection(
    List<String> selectedVariantIds,
    Map<String, int> customQuantities,
  ) {
    return selectedVariantIds.map((variantId) {
      final quantity = customQuantities[variantId] ?? 1;
      return CheckoutItem(
        variantId: variantId,
        quantity: quantity,
      );
    }).toList();
  }

  /// Get selected items from current cart state
  List<String> getSelectedVariantIds() {
    final cartState = ref.read(cartProvider);
    if (cartState.cart == null) return [];
    
    // If backend supports selection metadata, use it
    if (cartState.cart!.anyItemsSelected == true) {
      return cartState.cart!.items
          .where((item) => item.selected)
          .map((item) => item.variantId)
          .toList();
    }
    
    // Fallback: return all items (for backward compatibility)
    return cartState.cart!.items.map((item) => item.variantId).toList();
  }

  /// Check if cart has any selected items
  bool hasSelectedItems() {
    final cartState = ref.read(cartProvider);
    if (cartState.cart == null) return false;
    
    // Use backend selection metadata if available
    if (cartState.cart!.anyItemsSelected != null) {
      return cartState.cart!.anyItemsSelected!;
    }
    
    // Fallback: check if cart has any items
    return cartState.cart!.items.isNotEmpty;
  }

  /// Get total price of selected items
  double getSelectedTotalPrice() {
    final cartState = ref.read(cartProvider);
    if (cartState.cart == null) return 0.0;
    
    // Use backend calculated selected total if available
    if (cartState.cart!.selectedTotalPrice != null) {
      return cartState.cart!.selectedTotalPrice!;
    }
    
    // Fallback: calculate from selected items
    return cartState.cart!.items
        .where((item) => item.selected)
        .fold(0.0, (total, item) => total + (item.price * item.quantity));
  }

  /// Get count of selected items
  int getSelectedItemsCount() {
    final cartState = ref.read(cartProvider);
    if (cartState.cart == null) return 0;
    
    // Use backend calculated count if available
    if (cartState.cart!.selectedItemsCount != null) {
      return cartState.cart!.selectedItemsCount!;
    }
    
    // Fallback: count selected items
    return cartState.cart!.items.where((item) => item.selected).length;
  }

  /// Check if all items are selected
  bool areAllItemsSelected() {
    final cartState = ref.read(cartProvider);
    if (cartState.cart == null) return false;
    
    // Use backend metadata if available
    if (cartState.cart!.allItemsSelected != null) {
      return cartState.cart!.allItemsSelected!;
    }
    
    // Fallback: check manually
    return cartState.cart!.items.isNotEmpty && 
           cartState.cart!.items.every((item) => item.selected);
  }

  /// Validate checkout before proceeding
  bool validateCheckout({
    List<String>? selectedVariantIds,
    List<CheckoutItem>? customItems,
  }) {
    final cartState = ref.read(cartProvider);
    
    // Check if cart exists and has items
    if (cartState.cart == null || cartState.cart!.items.isEmpty) {
      debugPrint('[UnifiedCartService] Validation failed: Cart is empty');
      return false;
    }
    
    // If custom items provided, validate them
    if (customItems != null && customItems.isNotEmpty) {
      final cartVariantIds = cartState.cart!.items.map((item) => item.variantId).toSet();
      final invalidItems = customItems.where((item) => !cartVariantIds.contains(item.variantId));
      
      if (invalidItems.isNotEmpty) {
        debugPrint('[UnifiedCartService] Validation failed: Invalid variant IDs: ${invalidItems.map((item) => item.variantId).join(', ')}');
        return false;
      }
    }
    
    // If selected variants provided, validate them
    if (selectedVariantIds != null && selectedVariantIds.isNotEmpty) {
      final cartVariantIds = cartState.cart!.items.map((item) => item.variantId).toSet();
      final invalidIds = selectedVariantIds.where((id) => !cartVariantIds.contains(id));
      
      if (invalidIds.isNotEmpty) {
        debugPrint('[UnifiedCartService] Validation failed: Invalid variant IDs: ${invalidIds.join(', ')}');
        return false;
      }
    }
    
    // If no specific selection, check if any items are available for checkout
    if (customItems == null && selectedVariantIds == null) {
      if (!hasSelectedItems()) {
        debugPrint('[UnifiedCartService] Validation failed: No items selected for checkout');
        return false;
      }
    }
    
    return true;
  }

  /// Show validation error to user
  void showValidationError(String message) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Refresh cart after successful checkout
  Future<void> refreshCart() async {
    debugPrint('[UnifiedCartService] Refreshing cart after checkout');
    await ref.read(cartProvider.notifier).getCart();
  }
}

/// Provider for the unified cart service
/// Note: This should be used within a Consumer widget to get WidgetRef
final unifiedCartServiceProvider = Provider.family<UnifiedCartService Function(WidgetRef), BuildContext>((ref, context) {
  return (widgetRef) => UnifiedCartService(widgetRef, context);
});
