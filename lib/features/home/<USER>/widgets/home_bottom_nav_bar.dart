import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/providers/tab_state_provider.dart';
import '../../../auth/presentation/services/auth_guard_service.dart';
import '../../../../l10n/app_localizations.dart';

class HomeBottomNavBar extends ConsumerWidget {
  const HomeBottomNavBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(tabStateProvider);

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.08),
            blurRadius: 0,
            offset: Offset(0, -0.5),
          ),
        ],
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 3),
            child: BottomNavigationBar(
              currentIndex: currentIndex,
              type: BottomNavigationBarType.fixed,
              backgroundColor: Colors.white,
              selectedItemColor: const Color(0xFFFF6982),
              unselectedItemColor: const Color(0xFF1F2937),
              selectedLabelStyle: GoogleFonts.inter(
                fontSize: 11,
                fontWeight: FontWeight.w400,
              ),
              unselectedLabelStyle: GoogleFonts.inter(
                fontSize: 11,
                fontWeight: FontWeight.w400,
              ),
              elevation: 0,
              items: [
                BottomNavigationBarItem(
                  icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4, top: 8),
                    child: SvgPicture.asset(
                      'assets/icons/home_icon.svg',
                      colorFilter: const ColorFilter.mode(
                        Color(0xFF1F2937),
                        BlendMode.srcIn,
                      ),
                      height: 20,
                    ),
                  ),
                  activeIcon: Padding(
                    padding: const EdgeInsets.only(bottom: 4, top: 8),
                    child: SvgPicture.asset(
                      'assets/icons/home_icon.svg',
                      colorFilter: const ColorFilter.mode(
                        Color(0xFFFF6982),
                        BlendMode.srcIn,
                      ),
                      height: 20,
                    ),
                  ),
                  label: AppLocalizations.of(context).home,
                ),
                // BottomNavigationBarItem(
                //   icon: Padding(
                //     padding: const EdgeInsets.only(bottom: 4, top: 8),
                //     child: SvgPicture.asset(
                //       'assets/icons/sims_icon.svg',
                //       colorFilter: const ColorFilter.mode(
                //         Color(0xFF1F2933),
                //         BlendMode.srcIn,
                //       ),
                //       height: 20,
                //     ),
                //   ),
                //   activeIcon: Padding(
                //     padding: const EdgeInsets.only(bottom: 4, top: 8),
                //     child: SvgPicture.asset(
                //       'assets/icons/sims_icon.svg',
                //       colorFilter: const ColorFilter.mode(
                //         Color(0xFFFF6982),
                //         BlendMode.srcIn,
                //       ),
                //       height: 20,
                //     ),
                //   ),
                //   label: 'Sims',
                // ),
                // BottomNavigationBarItem(
                //   icon: Padding(
                //     padding: const EdgeInsets.only(bottom: 4, top: 8),
                //     child: Image.asset(
                //       'assets/icons/travelgator_logo.png',
                //       color: const Color(0xFF1F2937),
                //       colorBlendMode: BlendMode.srcIn,
                //       height: 20,
                //       width: 20,
                //       fit: BoxFit.contain,
                //     ),
                //   ),
                //   activeIcon: Padding(
                //     padding: const EdgeInsets.only(bottom: 4, top: 8),
                //     child: Image.asset(
                //       'assets/icons/travelgator_logo.png',
                //       color: const Color(0xFFFF6982),
                //       colorBlendMode: BlendMode.srcIn,
                //       height: 20,
                //       width: 20,
                //       fit: BoxFit.contain,
                //     ),
                //   ),
                //   label: 'TravelGator',
                // ),
                // BottomNavigationBarItem(
                //   icon: Padding(
                //     padding: const EdgeInsets.only(bottom: 4, top: 8),
                //     child: SvgPicture.asset(
                //       'assets/icons/rewards_icon.svg',
                //       colorFilter: const ColorFilter.mode(
                //         Color(0xFF1F2937),
                //         BlendMode.srcIn,
                //       ),
                //       height: 20,
                //     ),
                //   ),
                //   activeIcon: Padding(
                //     padding: const EdgeInsets.only(bottom: 4, top: 8),
                //     child: SvgPicture.asset(
                //       'assets/icons/rewards_icon.svg',
                //       colorFilter: const ColorFilter.mode(
                //         Color(0xFFFF6982),
                //         BlendMode.srcIn,
                //       ),
                //       height: 20,
                //     ),
                //   ),
                //   label: 'Rewards',
                // ),
                BottomNavigationBarItem(
                  icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4, top: 8),
                    child: SvgPicture.asset(
                      'assets/icons/account_icon.svg',
                      colorFilter: const ColorFilter.mode(
                        Color(0xFF1F2937),
                        BlendMode.srcIn,
                      ),
                      height: 20,
                    ),
                  ),
                  activeIcon: Padding(
                    padding: const EdgeInsets.only(bottom: 4, top: 8),
                    child: SvgPicture.asset(
                      'assets/icons/account_icon.svg',
                      colorFilter: const ColorFilter.mode(
                        Color(0xFFFF6982),
                        BlendMode.srcIn,
                      ),
                      height: 20,
                    ),
                  ),
                  label: AppLocalizations.of(context).account,
                ),
              ],
              onTap: (index) async {
                // Handle account tab with authentication check
                if (index == 1) {
                  final isAuthenticated = await AuthGuardService.requireAuthForAccount(
                    context,
                    ref,
                    onSuccess: () {
                      ref.read(tabStateProvider.notifier).setTab(index);
                      context.go('/account');
                    },
                  );

                  if (isAuthenticated && context.mounted) {
                    ref.read(tabStateProvider.notifier).setTab(index);
                    context.go('/account');
                  }
                  return;
                }

                // Handle other tabs normally
                ref.read(tabStateProvider.notifier).setTab(index);

                // Navigate to the appropriate route based on the tab index
                switch (index) {
                  case 0:
                    context.go('/home');
                    break;
                  case 1:
                    context.go('/account');
                    break;
                  // case 1:
                  //   context.go('/sims');
                  //   break;
                  // case 2:
                  //   context.go('/travelgator');
                  //   break;
                  // case 3:
                  //   context.go('/rewards');
                  //   break;
                }
              },
            ),
          ),
          // Active tab indicator
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(
                2,
                (index) => Container(
                  width: MediaQuery.of(context).size.width / 2,
                  height: 4,
                  decoration: BoxDecoration(
                    color:
                        currentIndex == index
                            ? const Color(0xFFFF6982)
                            : Colors.transparent,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(4),
                      bottomRight: Radius.circular(4),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
