import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import '../../../auth/presentation/services/auth_guard_service.dart';

class HomeSearchBar extends ConsumerStatefulWidget {
  const HomeSearchBar({super.key});

  @override
  ConsumerState<HomeSearchBar> createState() => _HomeSearchBarState();
}

class _HomeSearchBarState extends ConsumerState<HomeSearchBar> {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // SEARCH BAR HIDDEN - Only show cart button
        const Spacer(),
        const SizedBox(width: 16),
        IconButton(
          icon: SvgPicture.asset(
            'assets/icons/shopping_bag_icon.svg',
            colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
            height: 24,
            width: 24,
          ),
          onPressed: () async {
            // Check authentication before navigating to cart
            await AuthGuardService.requireAuthForCart(
              context,
              ref,
              onSuccess: () {
                // Only navigate to cart after successful authentication
                if (context.mounted) {
                  context.go('/cart');
                }
              },
            );
          },
        ),
      ],
    );
  }


}
