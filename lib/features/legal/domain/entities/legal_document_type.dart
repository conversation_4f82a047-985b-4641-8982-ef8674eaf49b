import 'package:flutter/material.dart';
import '../../../../l10n/app_localizations.dart';

/// Enum representing different types of legal documents
enum LegalDocumentType {
  userAgreement,
  privacyPolicy;

  /// Get the localized display name for the document type
  String getDisplayName(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    switch (this) {
      case LegalDocumentType.userAgreement:
        return l10n.userAgreement;
      case LegalDocumentType.privacyPolicy:
        return l10n.privacyPolicy;
    }
  }

  /// Get the display name for the document type (deprecated - use getDisplayName instead)
  @Deprecated('Use getDisplayName(context) for localized names')
  String get displayName {
    switch (this) {
      case LegalDocumentType.userAgreement:
        return 'User Agreement';
      case LegalDocumentType.privacyPolicy:
        return 'Privacy Policy';
    }
  }

  /// Get the localization key for the document type
  String get localizationKey {
    switch (this) {
      case LegalDocumentType.userAgreement:
        return 'userAgreement';
      case LegalDocumentType.privacyPolicy:
        return 'privacyPolicy';
    }
  }

  /// Get the icon path for the document type
  String get iconPath {
    switch (this) {
      case LegalDocumentType.userAgreement:
        return 'assets/icons/book_icon.svg';
      case LegalDocumentType.privacyPolicy:
        return 'assets/icons/folder_lock_icon.svg';
    }
  }
}
