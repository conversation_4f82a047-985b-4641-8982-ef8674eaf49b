import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../cart/domain/entities/cart.dart';
import '../../../cart/presentation/providers/cart_provider.dart';
import '../../../cart/presentation/providers/cart_state.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../providers/stripe_providers.dart';
import '../providers/new_checkout_provider.dart';
import '../widgets/widgets.dart';

/// Modular checkout screen with smaller, reusable components
class ModularCheckoutScreen extends ConsumerStatefulWidget {
  final Cart cart;
  final bool isFromBuyNow;
  final Map<String, dynamic>? buyNowData;

  const ModularCheckoutScreen({
    super.key,
    required this.cart,
    this.isFromBuyNow = false,
    this.buyNowData,
  });

  @override
  ConsumerState<ModularCheckoutScreen> createState() => _ModularCheckoutScreenState();
}

class _ModularCheckoutScreenState extends ConsumerState<ModularCheckoutScreen> {
  // Track applied discounts and credits
  double _appliedCredits = 0.0;
  String? _appliedDiscountCode;
  double _discountAmount = 0.0;

  // Track loading state
  bool _isLoading = false;

  // Track card validation state
  bool _isCardFormValid = false;

  @override
  void initState() {
    super.initState();

    // Initialize voucher data from cart if available
    if (widget.cart.voucherCode != null) {
      _appliedDiscountCode = widget.cart.voucherCode;
      _discountAmount = widget.cart.discountAmount ?? 0.0;
    }

    // Initialize Stripe when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeStripe();
    });
  }

  Future<void> _initializeStripe() async {
    await ref.read(stripeInitializationProvider.notifier).initialize();
  }

  @override
  Widget build(BuildContext context) {
    // Listen to cart state changes for real-time updates
    ref.listen<CartState>(cartProvider, (previous, next) {
      if (next.cart != null) {
        final currentCart = next.cart!;
        if (currentCart.voucherCode != _appliedDiscountCode ||
            currentCart.discountAmount != _discountAmount) {
          if (mounted) {
            setState(() {
              _appliedDiscountCode = currentCart.voucherCode;
              _discountAmount = currentCart.discountAmount ?? 0.0;
            });
          }
        }
      }
    });

    // Get current cart state
    final cartState = ref.watch(cartProvider);
    final currentCart = cartState.cart ?? widget.cart;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: const Color(0xFFF3F4F6),
        body: Column(
          children: [
            // Header
            const CheckoutHeader(),

            // Scrollable content
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // Cart summary
                    CheckoutCartSummary(
                      cart: currentCart,
                      isFromBuyNow: widget.isFromBuyNow,
                    ),
                    
                    // Payment section
                    CheckoutPaymentSection(
                      onCardValidationChanged: (isValid) {
                        setState(() {
                          _isCardFormValid = isValid;
                        });
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Credits and discount section
                    CheckoutCreditsDiscountSection(
                      cart: currentCart,
                      onDiscountApplied: (code) {
                        setState(() {
                          _appliedDiscountCode = code;
                          _discountAmount = currentCart.discountAmount ?? _calculateDiscountAmount(code);
                        });
                        debugPrint('Discount code applied: $code, amount: \$${_discountAmount.toStringAsFixed(2)}');
                      },
                      onDiscountRemoved: () {
                        setState(() {
                          _appliedDiscountCode = null;
                          _discountAmount = 0.0;
                        });
                        debugPrint('Discount code removed');
                      },
                      onCreditsApplied: (credits) {
                        setState(() {
                          _appliedCredits = credits;
                        });
                        debugPrint('Credits applied: \$${credits.toStringAsFixed(2)}');
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            // Grand total and pay button
            CheckoutGrandTotalSection(
              cart: currentCart,
              appliedCredits: _appliedCredits,
              appliedDiscountCode: _appliedDiscountCode,
              discountAmount: _discountAmount,
              isLoading: _isLoading,
              isCardFormValid: _isCardFormValid,
              onPayPressed: _handlePayment,
            ),
          ],
        ),
      ),
    );
  }

  /// Calculate discount amount based on code (fallback logic)
  double _calculateDiscountAmount(String code) {
    // Get current cart from provider
    final cartState = ref.read(cartProvider);
    final currentCart = cartState.cart ?? widget.cart;

    // This is a fallback - the real discount should come from the API
    switch (code.toLowerCase()) {
      case 'save10':
        return currentCart.totalPrice * 0.1; // 10% discount
      case 'save5':
        return 5.0; // $5 fixed discount
      case 'save2':
        return 2.0; // $2 fixed discount
      default:
        return 0.0;
    }
  }

  /// Handle payment processing
  Future<void> _handlePayment() async {
    if (!_isCardFormValid) {
      SnackbarUtils.showError(
        context,
        'Please complete your payment information',
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final checkoutNotifier = ref.read(newCheckoutProvider.notifier);

      if (widget.isFromBuyNow && widget.buyNowData != null) {
        // Handle buy now payment
        await _handleBuyNowPayment(checkoutNotifier);
      } else {
        // Handle cart payment
        await _handleCartPayment(checkoutNotifier);
      }

      // Navigate to success screen
      if (mounted) {
        context.go('/payment-success');
      }
    } catch (e) {
      debugPrint('Payment error: $e');
      if (mounted) {
        SnackbarUtils.showError(
          context,
          'Payment failed: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Handle buy now payment
  Future<void> _handleBuyNowPayment(NewCheckoutNotifier checkoutNotifier) async {
    final buyNowData = widget.buyNowData!;
    await checkoutNotifier.createBuyNowPaymentIntent(
      variantId: buyNowData['variantId'],
      quantity: buyNowData['quantity'],
      price: buyNowData['price'],
      productName: buyNowData['productName'],
    );

    final state = ref.read(newCheckoutProvider);
    if (state.paymentIntent?.clientSecret != null) {
      await checkoutNotifier.confirmPaymentWithElement(state.paymentIntent!.clientSecret);
    }
  }

  /// Handle cart payment
  Future<void> _handleCartPayment(NewCheckoutNotifier checkoutNotifier) async {
    await checkoutNotifier.createCartPaymentIntent(widget.cart);

    final state = ref.read(newCheckoutProvider);
    if (state.paymentIntent?.clientSecret != null) {
      await checkoutNotifier.confirmPaymentWithElement(state.paymentIntent!.clientSecret);
    }
  }
}
