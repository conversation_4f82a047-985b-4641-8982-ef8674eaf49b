import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/order.dart' as entities;
import '../entities/discount_stats.dart';

/// Repository interface for order operations
abstract class OrderRepository {
  Future<Either<Failure, List<entities.Order>>> getOrders();
  Future<Either<Failure, entities.Order>> getOrderById(String orderId);
  Future<Either<Failure, DiscountStats>> getDiscountStats();
}
