// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stripe_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StripeConfigModel _$StripeConfigModelFromJson(Map<String, dynamic> json) =>
    StripeConfigModel(
      publishableKey: json['publishable_key'] as String? ?? '',
      merchantId: json['merchant_id'] as String? ?? '',
      merchantDisplayName:
          json['merchant_display_name'] as String? ?? 'TravelGator',
      countryCode: json['country_code'] as String? ?? 'US',
      currency: json['currency'] as String? ?? 'USD',
      testMode: json['test_mode'] as bool? ?? true,
      additionalSettings: json['additional_settings'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$StripeConfigModelToJson(StripeConfigModel instance) =>
    <String, dynamic>{
      'publishable_key': instance.publishableKey,
      'merchant_id': instance.merchantId,
      'merchant_display_name': instance.merchantDisplayName,
      'country_code': instance.countryCode,
      'currency': instance.currency,
      'test_mode': instance.testMode,
      'additional_settings': instance.additionalSettings,
    };
