import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../domain/entities/cart.dart';
import '../../../../l10n/app_localizations.dart';
import '../../../../core/providers/product_provider.dart';
import '../../../../core/models/product_model.dart';

class CartItemCard extends ConsumerStatefulWidget {
  final CartItem item;
  final Function(int) onQuantityChanged;
  final VoidCallback onRemove;
  final bool isSelected;
  final Function(bool) onSelectionChanged;

  const CartItemCard({
    super.key,
    required this.item,
    required this.onQuantityChanged,
    required this.onRemove,
    this.isSelected = true,
    required this.onSelectionChanged,
  });

  @override
  ConsumerState<CartItemCard> createState() => _CartItemCardState();
}

class _CartItemCardState extends ConsumerState<CartItemCard> {
  late int _displayQuantity;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    _displayQuantity = widget.item.quantity;
  }

  @override
  void didUpdateWidget(CartItemCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update display quantity when the item quantity changes from external source
    if (!_isUpdating && widget.item.quantity != _displayQuantity) {
      _displayQuantity = widget.item.quantity;
    }
  }

  void _handleQuantityChange(int newQuantity) {
    setState(() {
      _displayQuantity = newQuantity;
      _isUpdating = true;
    });

    // Call the callback
    widget.onQuantityChanged(newQuantity);

    // Reset updating flag after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    });
  }

  /// Helper method to check if product IDs match (handles GID and numeric formats)
  bool _isMatchingProductId(String productId, String cartProductId) {
    // Direct match
    if (productId == cartProductId) return true;

    // Extract numeric ID from GID format: gid://shopify/Product/123456
    if (productId.contains('gid://shopify/Product/')) {
      final numericId = productId.split('/').last;
      if (numericId == cartProductId) return true;
    }

    // Try the reverse: if cart has GID and product has numeric
    if (cartProductId.contains('gid://shopify/Product/')) {
      final cartNumericId = cartProductId.split('/').last;
      if (cartNumericId == productId) return true;
    }

    return false;
  }

  @override
  Widget build(BuildContext context) {
    // Try to get image URL from cart item first, then from products provider
    String? productImageUrl = widget.item.imageUrl;

    debugPrint('🖼️ Cart item ${widget.item.title}: imageUrl from cart = $productImageUrl');

    // If no image URL from cart, try to find it in the products provider
    if (productImageUrl == null || productImageUrl.isEmpty) {
      final productsState = ref.watch(productsProvider);

      // If products are not loaded, trigger loading
      if (productsState.products.isEmpty && !productsState.isLoading) {
        debugPrint('🖼️ No products loaded, triggering product fetch...');
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ref.read(productsProvider.notifier).fetchProducts(first: 10);
        });
      }

      // Try to find matching product by ID (handle both numeric and GID formats)
      final cartProductId = widget.item.productId;
      final matchingProduct = productsState.products.where((product) {
        return _isMatchingProductId(product.id, cartProductId);
      }).firstOrNull;

      if (matchingProduct?.featuredImage?.url != null) {
        productImageUrl = matchingProduct!.featuredImage!.url;
        debugPrint('🖼️ Found product image for cart item ${widget.item.title}: $productImageUrl');
      } else {
        debugPrint('🖼️ No product image found for cart item ${widget.item.title} (productId: $cartProductId)');
        if (productsState.products.isNotEmpty) {
          debugPrint('🖼️ Available products: ${productsState.products.map((p) => '${p.title} (${p.id})').take(3).join(', ')}...');
        }
      }
    }
    return GestureDetector(
      onTap: () => widget.onSelectionChanged(!widget.isSelected),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: widget.isSelected ? const Color(0xFFF9FAFB) : Colors.white, // Subtle highlight when selected
          borderRadius: BorderRadius.circular(8),
          border: widget.isSelected
            ? Border.all(color: const Color(0xFFFF6982).withValues(alpha: 0.2), width: 1)
            : null, // Subtle border when selected
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
          // Checkbox with improved touch target
          GestureDetector(
            onTap: () => widget.onSelectionChanged(!widget.isSelected),
            child: Container(
              width: 44, // Minimum touch target size for mobile
              height: 44, // Minimum touch target size for mobile
              alignment: Alignment.center,
              color: Colors.transparent, // Ensures the full area is tappable
              child: SizedBox(
                width: 16, // Visual size remains the same
                height: 16, // Visual size remains the same
                child: Checkbox(
                  value: widget.isSelected,
                  onChanged: (value) => widget.onSelectionChanged(value ?? false),
                  activeColor: const Color(0xFFFF6982),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                  side: const BorderSide(
                    color: Color(0xFFFF6982),
                    width: 1,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(width: 8),

          // Product Details Section
          Expanded(
            child: Row(
              children: [
                // Product Image (Carousel)
                Container(
                  width: 108,
                  height: 108,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: const Color(0xFFFF6982),
                  ),
                  child: _buildProductImage(productImageUrl),
                ),

                const SizedBox(width: 8),

                // Product Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product Title
                      Text(
                        _getProductTitle(),
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xFF1F2937),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 8),

                      // Data Detail
                      _buildDetailRow(_getDataAmount(), AppLocalizations.of(context).gb),

                      const SizedBox(height: 8),

                      // Duration Detail
                      _buildDetailRow(_getDuration(context), AppLocalizations.of(context).day),

                      const SizedBox(height: 8),

                      // Quantity Selector
                      _buildQuantitySelector(),

                      const SizedBox(height: 8),

                      // Price
                      _buildPriceSection(),
                    ],
                  ),
                ),
              ],
            ),
          ),

        ],
      ),
    ),
    );  
  }

  Widget _buildProductImage(String? imageUrl) {
    return Container(
      width: 108,
      height: 108,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        color: const Color(0xFFF3F4F6), // Light gray background for fallback
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: imageUrl != null && imageUrl.isNotEmpty
            ? Image.network(
                imageUrl,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                      color: const Color(0xFFFF6982),
                      strokeWidth: 2,
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return _buildFallbackImage();
                },
              )
            : _buildFallbackImage(),
      ),
    );
  }

  Widget _buildFallbackImage() {
    return Container(
      width: 108,
      height: 108,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFF6982),
            Color(0xFFFF8FA3),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.sim_card,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(height: 4),
            Text(
              _getESimType(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getProductTitle() {
    // Extract the main product name from the title
    final title = widget.item.title;
    if (title.contains(' - ')) {
      return title.split(' - ').first;
    }
    return title;
  }

  Variant? _getVariantFromId() {
    // Get all products from the provider
    final productsState = ref.read(productsProvider);

    debugPrint('🔍 CartItem: Looking for variant with ID: ${widget.item.variantId}');
    debugPrint('🔍 CartItem: Available products: ${productsState.products.length}');

    // Find the product that contains this variant
    for (final product in productsState.products) {
      for (final variantEdge in product.variants.edges) {
        final variant = variantEdge.node;

        // Check multiple ID format possibilities:
        // 1. Exact match
        if (variant.id == widget.item.variantId) {
          debugPrint('🔍 CartItem: Found exact match variant: ${variant.id}');
          return variant;
        }

        // 2. GID format vs numeric format
        // Extract numeric part from GID (e.g., "gid://shopify/ProductVariant/123" -> "123")
        final variantNumericId = variant.id.split('/').last;
        final cartItemNumericId = widget.item.variantId.split('/').last;

        if (variantNumericId == cartItemNumericId) {
          debugPrint('🔍 CartItem: Found numeric match variant: ${variant.id} ($variantNumericId)');
          return variant;
        }

        // 3. Check if one contains the other
        if (variant.id.contains(widget.item.variantId) ||
            widget.item.variantId.contains(variant.id)) {
          debugPrint('🔍 CartItem: Found partial match variant: ${variant.id}');
          return variant;
        }
      }
    }

    debugPrint('🔍 CartItem: No variant found for ID: ${widget.item.variantId}');
    return null;
  }

  String _getDataAmount() {
    // Get variant data from product provider using variant_id
    final variant = _getVariantFromId();
    if (variant != null) {
      debugPrint('🔍 CartItem: Found variant for data extraction: ${variant.id}');
      debugPrint('🔍 CartItem: Variant selectedOptions: ${variant.selectedOptions.map((o) => '${o.name}=${o.value}').join(', ')}');

      // Look for GB option in selectedOptions
      final gbOption = variant.getOptionValue('gb') ?? variant.getOptionValue('data');
      if (gbOption != null) {
        debugPrint('🔍 CartItem: Found GB option: $gbOption');
        // Extract numeric value from option (e.g., "1GB" -> "1", "5" -> "5")
        final regex = RegExp(r'(\d+)');
        final match = regex.firstMatch(gbOption);
        final result = match?.group(1) ?? '1';
        debugPrint('🔍 CartItem: Extracted data amount: $result');
        return result;
      } else {
        debugPrint('🔍 CartItem: No GB/data option found in variant');
      }
    } else {
      debugPrint('🔍 CartItem: No variant found, using title fallback');
    }

    // Fallback to title parsing if variant not found
    final title = widget.item.title;
    final regex = RegExp(r'(\d+)\s*GB', caseSensitive: false);
    final match = regex.firstMatch(title);
    final result = match?.group(1) ?? '1';
    debugPrint('🔍 CartItem: Fallback data amount from title: $result');
    return result;
  }

  String _getDuration(BuildContext context) {
    // Get variant data from product provider using variant_id
    final variant = _getVariantFromId();
    if (variant != null) {
      // Look for days option in selectedOptions
      final daysOption = variant.getOptionValue('days') ?? variant.getOptionValue('validity');
      if (daysOption != null) {
        // Extract numeric value from option (e.g., "1 day" -> "1", "7 Days" -> "7")
        final regex = RegExp(r'(\d+)');
        final match = regex.firstMatch(daysOption);
        final duration = match?.group(1) ?? '1';
        final l10n = AppLocalizations.of(context);
        return duration == '1' ? '$duration ${l10n.day}' : '$duration ${l10n.days}';
      }
    }

    // Fallback to title parsing if variant not found
    final title = widget.item.title;
    final regex = RegExp(r'(\d+)\s+days?', caseSensitive: false);
    final match = regex.firstMatch(title);
    final duration = match?.group(1) ?? '1';
    final l10n = AppLocalizations.of(context);
    return duration == '1' ? '$duration ${l10n.day}' : '$duration ${l10n.days}';
  }

  String _getESimType() {
    // Get variant data from product provider using variant_id
    final variant = _getVariantFromId();
    if (variant != null) {
      // Look for eSIM type option in selectedOptions
      final eSimOption = variant.getOptionValue('esim_type') ??
                        variant.getOptionValue('type') ??
                        variant.getOptionValue('esim');
      if (eSimOption != null) {
        return eSimOption;
      }
    }

    // Fallback to title parsing if variant not found
    final title = widget.item.title;
    if (title.contains('(') && title.contains(')')) {
      final startIndex = title.lastIndexOf('(');
      final endIndex = title.lastIndexOf(')');
      if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
        return title.substring(startIndex + 1, endIndex);
      }
    }
    return 'New eSIM'; // Default fallback
  }

Widget _buildDetailRow(String value, String label) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Text(
        label,
        style: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: const Color(0xFF9CA3AF),
        ),
      ),
      Text(
        value,
        style: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: const Color(0xFF1F2937),
        ),
      ),
    ],
  );
}

  Widget _buildQuantitySelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Minus button (filled)
          GestureDetector(
            onTap: () {
              if (_displayQuantity > 1) {
                _handleQuantityChange(_displayQuantity - 1);
              } else {
                // When quantity is 1, show remove confirmation without changing display
                widget.onRemove();
              }
            },
            child: Container(
              width: 24,
              height: 24,
              decoration: const BoxDecoration(
                color: Color(0xFFFF6982),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.remove,
                size: 16,
                color: Color(0xFFD1D5DB),
              ),
            ),
          ),

          // Quantity display
          Container(
            width: 30,
            height: 30,
            margin: const EdgeInsets.symmetric(horizontal: 10),
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFD1D5DB)),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                '$_displayQuantity',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xFF1F2937),
                ),
              ),
            ),
          ),

          // Plus button (outlined)
          GestureDetector(
            onTap: () => _handleQuantityChange(_displayQuantity + 1),
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                border: Border.all(
                  color: const Color(0xFFFF6982),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.add,
                size: 16,
                color: Color(0xFFFF6982),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceSection() {
    // Calculate total price for this item (unit price × quantity)
    final totalPrice = widget.item.price * widget.item.quantity;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          '\$${totalPrice.toStringAsFixed(2)}', // Total price for this item
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: const Color(0xFFFF6982),
          ),
        ),
      ],
    );
  }
}
