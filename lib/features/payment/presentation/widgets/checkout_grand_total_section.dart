import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../cart/domain/entities/cart.dart';

/// Grand total section widget for checkout screen with pricing breakdown and pay button
class CheckoutGrandTotalSection extends StatelessWidget {
  final Cart cart;
  final double appliedCredits;
  final String? appliedDiscountCode;
  final double discountAmount;
  final bool isLoading;
  final bool isCardFormValid;
  final VoidCallback? onPayPressed;

  const CheckoutGrandTotalSection({
    super.key,
    required this.cart,
    this.appliedCredits = 0.0,
    this.appliedDiscountCode,
    this.discountAmount = 0.0,
    this.isLoading = false,
    this.isCardFormValid = false,
    this.onPayPressed,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate pricing breakdown using actual applied values
    final originalPrice = cart.totalPrice;
    final creditsDiscount = appliedCredits;
    
    // Use cart voucher data if available, otherwise use local discount
    final voucherDiscount = cart.discountAmount ?? discountAmount;
    final voucherCode = cart.voucherCode ?? appliedDiscountCode;
    
    final finalPrice = cart.discountedTotal ?? (originalPrice - creditsDiscount - voucherDiscount);

    return Container(
      width: double.infinity, // Full width - responsive
      constraints: const BoxConstraints(minHeight: 162), // Minimum height, can expand
      decoration: const BoxDecoration(
        color: Color(0xFFFFFFFF),
        border: Border(
          top: BorderSide(
            color: Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Main content with padding
          Expanded(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0), // Top and sides padding
              child: Column(
                children: [
                  // Grand Total section with price breakdown
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Grand Total label section - exact Figma layout
                      Container(
                        width: 112, // Exact Figma width
                        height: 39, // Exact Figma height
                        padding: const EdgeInsets.fromLTRB(8, 8, 16, 8),
                        child: Text(
                          'Grand Total:',
                          style: GoogleFonts.inter(
                            fontSize: 18, // Exact Figma font size
                            fontWeight: FontWeight.w600, // Exact Figma font weight
                            color: const Color(0xFF1F2937), // Exact Figma color
                            height: 1.2102272245619032, // Exact Figma line height
                          ),
                          textAlign: TextAlign.right, // Right-aligned as per Figma
                        ),
                      ),

                      // Vertical divider line - exact Figma dimensions
                      Container(
                        width: 1, // Exact Figma width
                        height: 23, // Exact Figma height
                        color: const Color(0xFFE5E7EB), // Exact Figma stroke color
                      ),

                      // Price breakdown section - flexible height
                      Expanded(
                        child: Container(
                          constraints: const BoxConstraints(minHeight: 46), // Minimum height, can expand
                          padding: const EdgeInsets.all(12), // Exact Figma padding
                          decoration: const BoxDecoration(
                            color: Color(0xFFFFFFFF), // Exact Figma background
                            borderRadius: BorderRadius.all(Radius.circular(8)), // Exact Figma border radius
                          ),
                          child: Center(
                            child: _buildPriceBreakdownWidget(
                              originalPrice,
                              creditsDiscount,
                              voucherDiscount,
                              finalPrice,
                              voucherCode
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Pay Now button with exact Figma styling
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 32), // Bottom padding
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: (isLoading || !isCardFormValid) ? null : onPayPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF6982), // Exact Figma color
                  foregroundColor: Colors.white,
                  elevation: 4, // Exact Figma shadow
                  shadowColor: Colors.black.withValues(alpha: 0.25), // Exact Figma shadow
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6), // Exact Figma border radius
                  ),
                  padding: const EdgeInsets.all(12), // Exact Figma padding
                ),
                child: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Pay now',
                        style: GoogleFonts.inter(
                          fontSize: 14, // Exact Figma font size
                          fontWeight: FontWeight.w500, // Exact Figma font weight
                          letterSpacing: 0.005, // Exact Figma letter spacing (0.5%)
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build price breakdown widget with styled text for discounts
  Widget _buildPriceBreakdownWidget(
    double originalPrice,
    double creditsDiscount,
    double voucherDiscount,
    double finalPrice,
    String? voucherCode
  ) {
    // Check if any discounts are applied
    bool hasCredits = creditsDiscount > 0;
    bool hasVoucher = voucherDiscount > 0 && voucherCode != null && voucherCode.isNotEmpty;
    bool hasAnyDiscount = hasCredits || hasVoucher;

    // If no discounts applied, show only the original price
    if (!hasAnyDiscount) {
      debugPrint('[CheckoutGrandTotalSection] No discounts - showing original price: \$${originalPrice.toStringAsFixed(2)}');
      return Text(
        '\$${originalPrice.toStringAsFixed(2)}',
        style: GoogleFonts.inter(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF1F2937),
          height: 1.2102272245619032,
        ),
        textAlign: TextAlign.right,
      );
    }

    // If discounts are applied, show the styled breakdown
    List<Widget> children = [];

    // Original price with strikethrough when voucher applied
    children.add(
      Text(
        '\$${originalPrice.toStringAsFixed(2)}',
        style: GoogleFonts.inter(
          fontSize: hasVoucher ? 14 : 18, // Nhỏ hơn khi có voucher (14px), bình thường khi không có (18px)
          fontWeight: hasVoucher ? FontWeight.w500 : FontWeight.w600, // Lighter weight khi có voucher
          color: hasVoucher ? const Color(0xFF6B7280) : const Color(0xFF1F2937), // Màu nhạt hơn khi có voucher
          height: 1.2102272245619032,
          decoration: hasVoucher ? TextDecoration.lineThrough : null, // Gạch ngang khi có voucher
          decorationColor: const Color(0xFF6B7280), // Màu gạch ngang
        ),
        textAlign: TextAlign.right,
      ),
    );

    // Credits discount (if applied)
    if (hasCredits) {
      children.add(
        Text(
          'Credits (-\$${creditsDiscount.toStringAsFixed(2)})',
          style: GoogleFonts.inter(
            fontSize: 14, // Smaller font for discount lines
            fontWeight: FontWeight.w500,
            color: const Color(0xFF059669), // Green color for credits
            height: 1.2102272245619032,
          ),
          textAlign: TextAlign.right,
        ),
      );
    }

    // Voucher discount (if applied) - smaller text
    if (hasVoucher) {
      children.add(
        Text(
          '${voucherCode.toUpperCase()} (-\$${voucherDiscount.toStringAsFixed(2)})',
          style: GoogleFonts.inter(
            fontSize: 14, // Smaller font for voucher code
            fontWeight: FontWeight.w500,
            color: const Color(0xFFFF6982), // Pink color for voucher
            height: 1.2102272245619032,
          ),
          textAlign: TextAlign.right,
        ),
      );
    }

    // Final price - prominent
    children.add(
      Text(
        '\$  ${finalPrice.toStringAsFixed(2)}',
        style: GoogleFonts.inter(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF1F2937),
          height: 1.2102272245619032,
        ),
        textAlign: TextAlign.right,
      ),
    );

    debugPrint('[CheckoutGrandTotalSection] Price breakdown with discounts displayed');
    debugPrint('[CheckoutGrandTotalSection] Voucher: $voucherCode, Discount: \$${voucherDiscount.toStringAsFixed(2)}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}
