import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../network/api_config.dart';
import 'models/client_models.dart';
import 'utils/error_handler.dart';
import 'auth/auth_provider.dart';

/// Main TravelGator API client helper library
class TravelGatorClient {
  final Dio _dio;
  final AuthInterceptor _authInterceptor;
  final ClientConfig _config;
  
  // Debouncing support
  Timer? _debounceTimer;
  CartUpdateRequest? _pendingUpdates;
  final Map<String, Completer<CartResponse>> _pendingRequests = {};

  TravelGatorClient({
    required AuthInterceptor authInterceptor,
    ClientConfig? config,
    Dio? dio,
  }) : _authInterceptor = authInterceptor,
       _config = config ?? ClientConfig(baseUrl: ApiConfig.baseUrl + ApiConfig.apiVersion),
       _dio = dio ?? _createDioClient(config ?? ClientConfig(baseUrl: ApiConfig.baseUrl + ApiConfig.apiVersion));

  /// Create configured Dio client
  static Dio _createDioClient(ClientConfig config) {
    final dio = Dio();
    
    dio.options = BaseOptions(
      baseUrl: config.baseUrl,
      connectTimeout: config.connectTimeout,
      receiveTimeout: config.receiveTimeout,
      sendTimeout: config.sendTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    if (config.enableLogging && kDebugMode) {
      dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: false,
        responseHeader: false,
      ));
    }

    return dio;
  }

  /// Get current cart
  Future<CartResponse> getCart() async {
    try {
      if (kDebugMode) {
        debugPrint('[TravelGatorClient] Getting cart');
      }

      final headers = await _authInterceptor.getAuthHeaders();
      
      final response = await _executeWithRetry(() => _dio.get(
        '/cart',
        options: Options(headers: headers),
      ));

      return ResponseProcessor.processResponse(
        response,
        (json) => CartResponse.fromJson(json),
      );
    } catch (e) {
      throw ClientErrorHandler.handleGeneralError(e);
    }
  }

  /// Update cart with debouncing
  Future<CartResponse> updateCart(CartUpdateRequest request) async {
    // Use debouncing to batch rapid updates
    if (_config.debounceDelay.inMilliseconds > 0) {
      return _scheduleCartUpdate(request);
    } else {
      return _executeCartUpdate(request);
    }
  }

  /// Schedule cart update with debouncing
  Future<CartResponse> _scheduleCartUpdate(CartUpdateRequest request) async {
    // Merge with pending updates
    _pendingUpdates = _mergePendingUpdates(_pendingUpdates, request);
    
    // Create or get existing completer for this update
    final requestKey = _generateRequestKey(request);
    final completer = _pendingRequests[requestKey] ?? Completer<CartResponse>();
    _pendingRequests[requestKey] = completer;

    // Cancel existing timer
    _debounceTimer?.cancel();
    
    // Schedule update after debounce delay
    _debounceTimer = Timer(_config.debounceDelay, () async {
      try {
        final result = await _executeCartUpdate(_pendingUpdates!);
        
        // Complete all pending requests
        for (final pendingCompleter in _pendingRequests.values) {
          if (!pendingCompleter.isCompleted) {
            pendingCompleter.complete(result);
          }
        }
        
        // Clear pending state
        _pendingUpdates = null;
        _pendingRequests.clear();
      } catch (e) {
        // Complete all pending requests with error
        for (final pendingCompleter in _pendingRequests.values) {
          if (!pendingCompleter.isCompleted) {
            pendingCompleter.completeError(e);
          }
        }
        
        // Clear pending state
        _pendingUpdates = null;
        _pendingRequests.clear();
      }
    });

    return completer.future;
  }

  /// Execute cart update
  Future<CartResponse> _executeCartUpdate(CartUpdateRequest request) async {
    try {
      if (kDebugMode) {
        debugPrint('[TravelGatorClient] Updating cart: ${request.toJson()}');
      }

      final headers = await _authInterceptor.getAuthHeaders();
      
      final response = await _executeWithRetry(() => _dio.put(
        '/cart',
        data: request.toJson(),
        options: Options(headers: headers),
      ));

      return ResponseProcessor.processResponse(
        response,
        (json) => CartResponse.fromJson(json),
      );
    } catch (e) {
      throw ClientErrorHandler.handleGeneralError(e);
    }
  }

  /// Execute request with retry logic
  Future<Response> _executeWithRetry(Future<Response> Function() request) async {
    int attempt = 0;
    
    while (attempt < _config.maxRetries) {
      attempt++;
      
      try {
        return await request();
      } catch (e) {
        final clientError = ClientErrorHandler.handleGeneralError(e);
        
        // Handle authentication errors with token refresh
        if (clientError.type == ClientErrorType.authentication && attempt == 1) {
          try {
            await _authInterceptor.handleAuthError();
            continue; // Retry with new token
          } catch (authError) {
            throw ClientErrorHandler.handleGeneralError(authError);
          }
        }
        
        // Check if error is retryable
        if (attempt >= _config.maxRetries || !ClientErrorHandler.isRetryableError(clientError)) {
          rethrow;
        }
        
        // Wait before retry
        final delay = ClientErrorHandler.getRetryDelay(attempt);
        if (kDebugMode) {
          debugPrint('[TravelGatorClient] Retrying in ${delay.inSeconds}s (attempt $attempt/${_config.maxRetries})');
        }
        await Future.delayed(delay);
      }
    }
    
    throw ClientException(
      message: 'Max retries exceeded',
      type: ClientErrorType.network,
    );
  }

  /// Merge pending cart updates
  CartUpdateRequest _mergePendingUpdates(
    CartUpdateRequest? existing,
    CartUpdateRequest newUpdate,
  ) {
    if (existing == null) return newUpdate;

    // Merge items by variant_id
    final mergedItems = <String, CartItemUpdate>{};
    
    // Add existing items
    if (existing.items != null) {
      for (final item in existing.items!) {
        mergedItems[item.variantId] = item;
      }
    }
    
    // Override with new items
    if (newUpdate.items != null) {
      for (final item in newUpdate.items!) {
        mergedItems[item.variantId] = item;
      }
    }

    return CartUpdateRequest(
      items: mergedItems.values.toList(),
      preferredPaymentMethod: newUpdate.preferredPaymentMethod ?? existing.preferredPaymentMethod,
    );
  }

  /// Generate request key for deduplication
  String _generateRequestKey(CartUpdateRequest request) {
    return request.hashCode.toString();
  }

  /// Dispose resources
  void dispose() {
    _debounceTimer?.cancel();
    _pendingRequests.clear();
    _pendingUpdates = null;
  }
}

/// Provider for TravelGator client
final travelGatorClientProvider = Provider<TravelGatorClient>((ref) {
  final authInterceptor = ref.watch(authInterceptorProvider);
  
  final client = TravelGatorClient(
    authInterceptor: authInterceptor,
    config: const ClientConfig(
      baseUrl: ApiConfig.baseUrl + ApiConfig.apiVersion,
      debounceDelay: Duration(milliseconds: 300),
      maxRetries: 3,
      enableLogging: kDebugMode,
    ),
  );

  ref.onDispose(() {
    client.dispose();
  });

  return client;
});
