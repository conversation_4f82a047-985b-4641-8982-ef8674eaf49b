# Checkout Architecture Documentation

## Overview

TravelGator implements a comprehensive checkout system supporting both traditional cart-based checkout and direct "Buy Now" purchases. The system integrates with Stripe for payment processing and supports both in-app payment intents and web-based checkout sessions.

## Architecture Components

### 1. Core Layers

```
┌─────────────────────────────────────────────────────────┐
│                 Presentation Layer                      │
│  ┌─────────────────┐  ┌─────────────────────────────┐   │
│  │ CheckoutScreen  │  │ PurchaseActions (Buy Now)   │   │
│  │                 │  │                             │   │
│  └─────────────────┘  └─────────────────────────────┘   │
│           │                        │                    │
│  ┌─────────────────┐  ┌─────────────────────────────┐   │
│  │ CartCheckout    │  │ BuyNowProvider              │   │
│  │ Service         │  │                             │   │
│  └─────────────────┘  └─────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
                          │
┌─────────────────────────────────────────────────────────┐
│                   Domain Layer                          │
│  ┌─────────────────┐  ┌─────────────────────────────┐   │
│  │ PaymentRepository│  │ BuyNowRepository            │   │
│  │ Interface        │  │ Interface                   │   │
│  └─────────────────┘  └─────────────────────────────┘   │
│  ┌─────────────────┐  ┌─────────────────────────────┐   │
│  │ PaymentIntent   │  │ BuyNowResponse              │   │
│  │ Entity          │  │ Entity                      │   │
│  └─────────────────┘  └─────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
                          │
┌─────────────────────────────────────────────────────────┐
│                    Data Layer                           │
│  ┌─────────────────┐  ┌─────────────────────────────┐   │
│  │ CartPayment     │  │ BuyNowPayment               │   │
│  │ DataSource      │  │ DataSource                  │   │
│  └─────────────────┘  └─────────────────────────────┘   │
│  ┌─────────────────┐  ┌─────────────────────────────┐   │
│  │ StripeDataSource│  │ PaymentRepository           │   │
│  │                 │  │ Implementation              │   │
│  └─────────────────┘  └─────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

### 2. State Management

The checkout system uses Riverpod for state management with the following key providers:

- **`newCheckoutProvider`**: Manages checkout flow state and payment intent creation
- **`stripeInitializationProvider`**: Handles Stripe SDK initialization
- **`buyNowProvider`**: Manages Buy Now operations and state
- **`cartProvider`**: Manages cart state and operations

### 3. Payment Flow Types

#### A. In-App Payment Intent Flow
- Uses Stripe Payment Element for card input
- Payment confirmation happens within the app
- Supports real-time validation and error handling
- Preferred method for better user experience

#### B. Web-Based Checkout Session Flow
- Redirects to Stripe-hosted checkout page
- Fallback option for complex payment scenarios
- Handles 3D Secure and other authentication flows

## API Endpoints

### Cart Checkout Endpoints

| Endpoint | Method | Purpose | Request Body |
|----------|--------|---------|--------------|
| `/cart/checkout` | POST | Unified checkout endpoint | `{}` (all items) or `{"selected_variant_ids": [...]}` or `{"items": [...]}` |
| `/cart/confirm_payment` | POST | Confirm payment intent | `{"payment_intent_id": "pi_xxx"}` |
| `/cart/payment_status` | GET | Check payment status | Query: `payment_intent_id` |
| `/cart/cancel_payment` | POST | Cancel payment | `{"payment_intent_id": "pi_xxx"}` |

### Buy Now Endpoints

| Endpoint | Method | Purpose | Request Body |
|----------|--------|---------|--------------|
| `/buy_now` | POST | Create buy now order | `{"variant_id": "xxx", "quantity": 1}` |
| `/buy_now/confirm_payment` | POST | Confirm buy now payment | `{"payment_intent_id": "pi_xxx"}` |
| `/buy_now/payment_status` | GET | Check buy now payment status | Query: `payment_intent_id` |

### Stripe Configuration

| Endpoint | Method | Purpose | Response |
|----------|--------|---------|----------|
| `/stripe/config` | GET | Get Stripe configuration | `{"publishable_key": "pk_xxx", "merchant_id": "xxx"}` |

## Data Flow Diagrams

### Cart Checkout Flow

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant Provider
    participant Repository
    participant Backend
    participant Stripe

    User->>UI: Tap "Checkout"
    UI->>Provider: createCartPaymentIntent()
    Provider->>Repository: createCartPaymentIntentWithItems()
    Repository->>Backend: POST /cart/checkout
    Backend-->>Repository: PaymentIntent response
    Repository-->>Provider: PaymentIntent entity
    Provider->>Stripe: confirmPayment()
    Stripe-->>Provider: Payment confirmation
    Provider->>Repository: pollPaymentStatus()
    Repository->>Backend: GET /cart/payment_status
    Backend-->>Repository: Payment status
    Repository-->>Provider: PaymentStatus entity
    Provider-->>UI: Success/Error state
    UI-->>User: Show result
```

### Buy Now Flow

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant Provider
    participant Repository
    participant Backend
    participant Stripe

    User->>UI: Tap "Buy Now"
    UI->>Provider: buyNow()
    Provider->>Repository: buyNow()
    Repository->>Backend: POST /buy_now
    Backend-->>Repository: BuyNowResponse

    alt Payment Intent Response
        Repository-->>Provider: Response with client_secret
        Provider->>UI: Navigate to CheckoutScreen
        UI->>Stripe: Present payment form
        Stripe-->>UI: Payment confirmation
        UI->>Provider: confirmPayment()
    else Checkout URL Response
        Repository-->>Provider: Response with checkout_url
        Provider->>UI: Open WebView
        UI->>Stripe: Redirect to checkout
        Stripe-->>UI: Payment completion
    end
```

## Error Handling

### Error Types and Localization

The system implements comprehensive error handling with full localization support:

#### Payment Error Categories

1. **Stripe Errors**
   - Card declined: `paymentFailed` (localized)
   - Payment canceled: `paymentCanceled`
   - Unknown errors: `paymentErrorUnknown`

2. **Backend Errors**
   - Payment intent creation: `paymentIntentCreationFailed`
   - Payment verification: `paymentVerificationFailed`
   - Order completion: `paymentSucceededOrderFailed`
   - System errors: `backendSystemError`

3. **Network Errors**
   - Connection timeouts
   - API unavailability
   - Authentication failures

#### Error Handling Implementation

```dart
// Stripe error handling with localization
void _handleStripeError(StripeException e) {
  final l10n = AppLocalizations.of(context);
  String errorMessage;

  switch (e.error.code) {
    case FailureCode.Canceled:
      errorMessage = l10n.paymentCanceled;
      break;
    case FailureCode.Failed:
      errorMessage = e.error.localizedMessage ?? l10n.paymentFailed;
      break;
    default:
      errorMessage = e.error.localizedMessage ?? l10n.paymentErrorUnknown;
  }

  SnackbarUtils.showPaymentError(context, errorMessage);
}
```

### Retry Mechanisms

- **Automatic retries**: For network timeouts and temporary failures
- **User-initiated retries**: For payment failures and user errors
- **Smart retry logic**: Prevents retries for permanent failures (e.g., backend system errors)

## Security Considerations

### Authentication
- All API calls require valid backend authentication token
- Token validation on every request
- Automatic token refresh handling

### Payment Security
- Stripe handles all sensitive payment data
- No card information stored locally
- PCI DSS compliance through Stripe integration

### Data Protection
- Payment intents expire automatically
- Sensitive data cleared from memory after use
- Secure communication over HTTPS

## Performance Optimizations

### State Management
- Efficient provider updates to minimize rebuilds
- Debounced user interactions to prevent duplicate requests
- Smart caching of Stripe configuration

### Network Optimization
- Request deduplication for payment status polling
- Optimized polling intervals (2-second intervals, max 30 seconds)
- Connection pooling for API requests

### Memory Management
- Proper disposal of payment forms and controllers
- Cleanup of listeners and subscriptions
- Efficient image loading and caching

## Testing Strategy

### Unit Tests
- Payment entity validation
- Error handling scenarios
- Localization message consistency
- State management logic

### Integration Tests
- End-to-end checkout flows
- Payment confirmation workflows
- Error recovery scenarios
- Cross-platform compatibility

### Manual Testing
- Real payment processing (test mode)
- Network failure scenarios
- Device-specific testing (iOS/Android)
- Accessibility compliance

## Configuration

### Environment Variables
```env
STRIPE_PUBLISHABLE_KEY_TEST=pk_test_xxx
STRIPE_PUBLISHABLE_KEY_LIVE=pk_live_xxx
BACKEND_API_URL=https://api.travelgator.com
```

### Feature Flags
- `ENABLE_IN_APP_PAYMENTS`: Toggle in-app vs web checkout
- `ENABLE_BUY_NOW`: Enable/disable buy now functionality
- `STRIPE_TEST_MODE`: Use test or live Stripe keys

## Monitoring and Analytics

### Key Metrics
- Payment success rates
- Checkout abandonment rates
- Error frequency by type
- Performance metrics (load times, API response times)

### Logging
- Comprehensive debug logging for payment flows
- Error tracking with context information
- User journey tracking for optimization

## Future Enhancements

### Planned Features
1. **Apple Pay/Google Pay Integration**
2. **Subscription Payment Support**
3. **Multi-currency Support**
4. **Enhanced Error Recovery**
5. **Offline Payment Queuing**

### Technical Improvements
1. **GraphQL Migration**
2. **Enhanced Caching Strategy**
3. **Real-time Payment Status Updates**
4. **Advanced Analytics Integration**