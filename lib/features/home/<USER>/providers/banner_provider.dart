import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/product_provider.dart';
import '../../data/repositories/banner_repository.dart';
import '../../domain/models/banner.dart';

// Banner repository provider
final bannerRepositoryProvider = Provider<BannerRepository>((ref) {
  final client = ref.watch(graphQLClientProvider);
  return BannerRepository(client: client);
});

// Banner state
class BannerState {
  final List<Banner> banners;
  final bool isLoading;
  final String? error;
  final Banner? featuredBanner;

  BannerState({
    this.banners = const [],
    this.isLoading = false,
    this.error,
    this.featuredBanner,
  });

  BannerState copyWith({
    List<Banner>? banners,
    bool? isLoading,
    String? error,
    Banner? featuredBanner,
  }) {
    return BannerState(
      banners: banners ?? this.banners,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      featuredBanner: featuredBanner ?? this.featuredBanner,
    );
  }
}

// Banner notifier
class BannerNotifier extends StateNotifier<BannerState> {
  final BannerRepository _repository;

  BannerNotifier(this._repository) : super(BannerState());

  /// Helper method to convert technical errors to user-friendly messages
  String _getUserFriendlyError(dynamic error) {
    final errorString = error.toString();
    if (errorString.contains('Network connection error') ||
        errorString.contains('No internet connection')) {
      return 'No internet connection. Please check your network.';
    } else if (errorString.contains('timeout') || errorString.contains('timed out')) {
      return 'Request timed out. Please try again.';
    } else if (errorString.contains('Failed host lookup')) {
      return 'Unable to connect to server. Please try again.';
    }
    return 'Unable to load banners. Please try again.';
  }

  /// Load all banners
  Future<void> loadBanners({int first = 10}) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final banners = await _repository.getBanners(first: first);
      
      // Set the first banner as featured banner if available
      final featuredBanner = banners.isNotEmpty ? banners.first : null;
      
      state = state.copyWith(
        banners: banners,
        isLoading: false,
        featuredBanner: featuredBanner,
      );
      
      // debugPrint('🎯 BannerNotifier: Loaded ${banners.length} banners');
      // if (featuredBanner != null) {
      //   debugPrint('🎯 BannerNotifier: Featured banner: ${featuredBanner.handle}');
      // }
    } catch (e) {
      // debugPrint('🎯 BannerNotifier: Error loading banners: $e');
      state = state.copyWith(isLoading: false, error: _getUserFriendlyError(e));
    }
  }

  /// Load a specific banner by handle
  Future<void> loadBannerByHandle(String handle) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final banner = await _repository.getBannerByHandle(handle);
      
      if (banner != null) {
        // Update the banners list and set as featured
        final updatedBanners = List<Banner>.from(state.banners);
        final existingIndex = updatedBanners.indexWhere((b) => b.handle == handle);
        
        if (existingIndex >= 0) {
          updatedBanners[existingIndex] = banner;
        } else {
          updatedBanners.insert(0, banner);
        }
        
        state = state.copyWith(
          banners: updatedBanners,
          isLoading: false,
          featuredBanner: banner,
        );
        
        // debugPrint('🎯 BannerNotifier: Loaded banner by handle: ${banner.handle}');
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Banner not found: $handle',
        );
      }
    } catch (e) {
      // debugPrint('🎯 BannerNotifier: Error loading banner by handle: $e');
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Force refresh banners from network
  Future<void> forceRefreshBanners({int first = 10}) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final banners = await _repository.forceRefreshBanners(first: first);
      
      // Set the first banner as featured banner if available
      final featuredBanner = banners.isNotEmpty ? banners.first : null;
      
      state = state.copyWith(
        banners: banners,
        isLoading: false,
        featuredBanner: featuredBanner,
      );
      
      // debugPrint('🎯 BannerNotifier: Force refreshed ${banners.length} banners');
    } catch (e) {
      // debugPrint('🎯 BannerNotifier: Error force refreshing banners: $e');
      state = state.copyWith(isLoading: false, error: _getUserFriendlyError(e));
    }
  }

  /// Set a specific banner as featured
  void setFeaturedBanner(Banner banner) {
    state = state.copyWith(featuredBanner: banner);
    // debugPrint('🎯 BannerNotifier: Set featured banner: ${banner.handle}');
  }
}

// Banner provider
final bannerProvider = StateNotifierProvider<BannerNotifier, BannerState>((ref) {
  final repository = ref.watch(bannerRepositoryProvider);
  return BannerNotifier(repository);
});

// Convenience provider for featured banner
final featuredBannerProvider = Provider<Banner?>((ref) {
  return ref.watch(bannerProvider).featuredBanner;
});

// Convenience provider for all banners
final bannersListProvider = Provider<List<Banner>>((ref) {
  return ref.watch(bannerProvider).banners;
});

// Convenience provider for banner loading state
final bannerLoadingProvider = Provider<bool>((ref) {
  return ref.watch(bannerProvider).isLoading;
});

// Convenience provider for banner error
final bannerErrorProvider = Provider<String?>((ref) {
  return ref.watch(bannerProvider).error;
});
