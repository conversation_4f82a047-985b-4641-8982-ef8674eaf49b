# Buy Now Functionality Documentation

## Overview

The Buy Now feature allows users to purchase products directly without adding them to a cart first. This provides a streamlined checkout experience for immediate purchases, particularly useful for digital products like eSIMs.

## Architecture

### Core Components

```
┌─────────────────────────────────────────────────────────┐
│                    Buy Now Flow                         │
│                                                         │
│  ┌─────────────────┐    ┌─────────────────────────────┐ │
│  │ PurchaseActions │───▶│ BuyNowProvider              │ │
│  │ Widget          │    │                             │ │
│  └─────────────────┘    └─────────────────────────────┘ │
│           │                        │                    │
│           │              ┌─────────────────────────────┐ │
│           │              │ BuyNowUseCase               │ │
│           │              │                             │ │
│           │              └─────────────────────────────┘ │
│           │                        │                    │
│           │              ┌─────────────────────────────┐ │
│           │              │ BuyNowRepository            │ │
│           │              │                             │ │
│           │              └─────────────────────────────┘ │
│           │                        │                    │
│           │              ┌─────────────────────────────┐ │
│           │              │ BuyNowDataSource            │ │
│           │              │                             │ │
│           │              └─────────────────────────────┘ │
│           │                                             │
│  ┌─────────────────┐    ┌─────────────────────────────┐ │
│  │ CheckoutScreen  │    │ BuyNowCheckoutWebView       │ │
│  │ (Payment Intent)│    │ (Checkout URL)              │ │
│  └─────────────────┘    └─────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### State Management

The Buy Now functionality uses Riverpod with the following providers:

- **`buyNowProvider`**: Main state management for Buy Now operations
- **`buyNowDataSourceProvider`**: Data source for API calls
- **`buyNowRepositoryProvider`**: Repository implementation
- **`buyNowUseCaseProvider`**: Business logic use case

## API Integration

### Buy Now Endpoints

#### 1. Simple Buy Now (`/api/buy_now`)
**Purpose**: Create a buy now order with minimal parameters

**Request**:
```json
POST /api/buy_now
Authorization: Bearer <backend_token>
Content-Type: application/json

{
  "variant_id": "gid://shopify/ProductVariant/123456",
  "quantity": 1,
  "product_id": "gid://shopify/Product/789012", // optional
  "price": 29.99, // optional
  "title": "eSIM Product Name" // optional
}
```

**Response Types**:

A. **Payment Intent Response** (In-App Payment):
```json
{
  "data": {
    "client_secret": "pi_1234567890_secret_abcdef",
    "payment_intent_id": "pi_1234567890",
    "order_id": "order_abc123",
    "amount": 29.99,
    "currency": "USD"
  }
}
```

B. **Checkout URL Response** (Web Payment):
```json
{
  "data": {
    "checkout_url": "https://checkout.stripe.com/pay/cs_test_...",
    "order_id": "order_abc123",
    "shopify_cart_id": "cart_def456",
    "total_amount": 29.99,
    "currency": "USD"
  }
}
```

#### 2. Direct Checkout (`/api/direct_checkout`)
**Purpose**: Create a buy now order with full validation

**Request**:
```json
POST /api/direct_checkout
Authorization: Bearer <backend_token>
Content-Type: application/json

{
  "variant_id": "gid://shopify/ProductVariant/123456",
  "product_id": "gid://shopify/Product/789012",
  "quantity": 1,
  "price": 29.99,
  "title": "eSIM Product Name"
}
```

**Response**: Same as Buy Now endpoint

## User Experience Flow

### 1. Product Selection
```dart
// User selects product variant and quantity
PurchaseActions(
  variantId: selectedVariant.id,
  quantity: selectedQuantity,
  price: selectedVariant.price,
  title: product.title,
  productId: product.id,
)
```

### 2. Buy Now Execution
```dart
// Triggered when user taps "Buy Now" button
Future<void> _handleBuyNow(BuildContext context, WidgetRef ref) async {
  // 1. Authentication check
  final authGuard = AuthGuardService();
  final canProceed = await authGuard.checkAuthenticationForAction(
    context: context,
    action: 'buy now',
    ref: ref,
  );

  if (!canProceed) return;

  // 2. Execute buy now
  await _executeBuyNow(context, ref);
}
```

### 3. Payment Flow Decision
```dart
// Based on backend response, choose payment method
if (response.hasPaymentIntent) {
  // In-app payment using Stripe Payment Element
  await _handlePaymentIntentFlow(context, ref, response);
} else if (response.hasCheckoutUrl) {
  // Web-based payment using Stripe Checkout
  await _handleCheckoutUrlFlow(context, ref, response);
}
```

## Payment Flow Types

### A. In-App Payment Intent Flow

**When Used**: When backend returns `client_secret` and `payment_intent_id`

**Flow**:
1. Create temporary cart from buy now response
2. Navigate to `CheckoutScreen` with `isFromBuyNow: true`
3. Use Stripe Payment Element for card input
4. Confirm payment within the app
5. Poll backend for payment status
6. Navigate back to home on success

**Implementation**:
```dart
Future<void> _handlePaymentIntentFlow(
  BuildContext context,
  WidgetRef ref,
  BuyNowResponse response
) async {
  // Create cart from buy now response
  final buyNowCart = Cart(
    id: 'buy-now-${response.orderId}',
    items: [
      CartItem(
        variantId: response.item.variantId,
        productId: response.item.productId,
        quantity: response.item.quantity,
        price: response.item.price,
        title: response.item.title,
        imageUrl: widget.imageUrl ?? '',
      ),
    ],
    totalPrice: response.totalPrice,
    currency: response.currency,
    itemsCount: response.item.quantity,
  );

  // Navigate to checkout screen
  final route = MaterialPageRoute(
    builder: (context) => CheckoutScreen(
      cart: buyNowCart,
      isFromBuyNow: true,
    ),
  );

  await Navigator.of(context).push(route);
}
```

### B. Web-Based Checkout URL Flow

**When Used**: When backend returns `checkout_url` and `shopify_cart_id`

**Flow**:
1. Open WebView with Stripe checkout URL
2. User completes payment on Stripe's hosted page
3. WebView detects success/failure through URL monitoring
4. Close WebView and show result

**Implementation**:
```dart
Future<void> _handleCheckoutUrlFlow(
  BuildContext context,
  WidgetRef ref,
  BuyNowResponse response
) async {
  final route = MaterialPageRoute(
    builder: (context) => BuyNowCheckoutWebView(
      checkoutUrl: response.checkoutUrl,
      onCheckoutSuccess: (orderId) {
        Navigator.of(context).pop();
        _showSuccessMessage(orderId);
      },
      onCheckoutError: (error) {
        Navigator.of(context).pop();
        _showErrorMessage(error);
      },
    ),
  );

  await Navigator.of(context).push(route);
}
```

## Error Handling

### Error Categories

1. **Authentication Errors**
   - User not signed in
   - Backend token missing/invalid
   - Token refresh failures

2. **Validation Errors**
   - Invalid variant ID
   - Invalid quantity (≤ 0)
   - Missing required parameters

3. **Network Errors**
   - API unavailable
   - Connection timeouts
   - Request failures

4. **Payment Errors**
   - Payment intent creation failed
   - Payment confirmation failed
   - Backend verification failed

### Error Handling Implementation

```dart
// Comprehensive error handling with localization
void _handleBuyNowError(dynamic error, BuildContext context) {
  String errorMessage;
  bool allowRetry = true;

  if (error is AuthFailure) {
    errorMessage = 'Authentication required. Please sign in.';
    allowRetry = false;
  } else if (error is ValidationFailure) {
    errorMessage = error.message;
    allowRetry = false;
  } else if (error is NetworkFailure) {
    errorMessage = 'Network error. Please check your connection.';
    allowRetry = true;
  } else {
    errorMessage = 'Buy now failed. Please try again.';
    allowRetry = true;
  }

  SnackbarUtils.showBuyNowError(
    context,
    errorMessage,
    onRetry: allowRetry ? () => _executeBuyNow(context, ref) : null,
  );
}
```

## State Management

### BuyNowState

```dart
class BuyNowState {
  final BuyNowStatus status;
  final BuyNowResponse? response;
  final String? errorMessage;

  const BuyNowState({
    this.status = BuyNowStatus.idle,
    this.response,
    this.errorMessage,
  });
}

enum BuyNowStatus {
  idle,
  loading,
  success,
  error,
}
```

### BuyNowNotifier Methods

```dart
class BuyNowNotifier extends StateNotifier<BuyNowState> {
  // Execute simple buy now
  Future<void> buyNow({
    required String variantId,
    required int quantity,
    String? productId,
    double? price,
    String? title,
  });

  // Execute direct checkout with validation
  Future<void> directCheckout({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
  });

  // Reset state
  void reset();
}
```

## Testing

### Unit Tests

```dart
// Test buy now response detection
test('should detect payment intent response correctly', () {
  final response = BuyNowResponse(
    clientSecret: 'pi_test_secret_123',
    paymentIntentId: 'pi_test_123',
    // ... other fields
  );

  expect(response.hasPaymentIntent, isTrue);
  expect(response.hasCheckoutUrl, isFalse);
});

// Test checkout URL response detection
test('should detect checkout URL response correctly', () {
  final response = BuyNowResponse(
    checkoutUrl: 'https://checkout.stripe.com/pay/cs_test',
    shopifyCartId: 'cart_456',
    clientSecret: null,
    paymentIntentId: null,
    // ... other fields
  );

  expect(response.hasPaymentIntent, isFalse);
  expect(response.hasCheckoutUrl, isTrue);
});
```

### Integration Tests

```dart
// Test complete buy now flow
testWidgets('should complete buy now flow successfully', (tester) async {
  // 1. Setup mock responses
  // 2. Tap buy now button
  // 3. Verify API calls
  // 4. Verify navigation
  // 5. Verify success state
});
```

## Performance Considerations

### Debouncing
- Prevent duplicate buy now requests with 2-second debounce
- Track last click timestamp to avoid rapid successive calls

### Memory Management
- Proper disposal of WebView controllers
- Cleanup of payment form controllers
- Clear sensitive data after use

### Network Optimization
- Request timeout handling (30 seconds)
- Retry logic for network failures
- Connection pooling for API requests

## Security

### Data Protection
- No sensitive payment data stored locally
- Automatic cleanup of payment intents
- Secure token handling

### Authentication
- Backend token validation on every request
- Automatic token refresh
- Graceful handling of authentication failures

## Configuration

### Feature Flags
```dart
// Enable/disable buy now functionality
const bool ENABLE_BUY_NOW = true;

// Choose default payment method
const PaymentMethod DEFAULT_PAYMENT_METHOD = PaymentMethod.paymentIntent;
```

### Environment Configuration
```env
# Buy now specific endpoints
BUY_NOW_ENDPOINT=/api/buy_now
DIRECT_CHECKOUT_ENDPOINT=/api/direct_checkout

# Payment configuration
STRIPE_TEST_MODE=true
PAYMENT_TIMEOUT_SECONDS=30
```

## Monitoring

### Key Metrics
- Buy now conversion rates
- Payment method distribution (intent vs URL)
- Error rates by category
- Average completion time

### Logging
```dart
// Comprehensive logging for debugging
debugPrint('[BuyNow] Starting buy now for variant: $variantId');
debugPrint('[BuyNow] Response type: ${response.hasPaymentIntent ? 'PaymentIntent' : 'CheckoutURL'}');
debugPrint('[BuyNow] Payment completed successfully');
```

## Best Practices

### User Experience
1. **Clear Loading States**: Show progress indicators during API calls
2. **Error Recovery**: Provide retry options for recoverable errors
3. **Success Feedback**: Clear confirmation of successful purchases
4. **Modal Management**: Proper handling of modal lifecycles

### Code Organization
1. **Separation of Concerns**: Clear separation between UI, business logic, and data
2. **Error Boundaries**: Comprehensive error handling at each layer
3. **State Management**: Predictable state updates with Riverpod
4. **Testing**: Comprehensive unit and integration test coverage

### Performance
1. **Lazy Loading**: Load buy now providers only when needed
2. **Efficient Updates**: Minimize unnecessary widget rebuilds
3. **Memory Management**: Proper cleanup of resources
4. **Network Efficiency**: Optimize API calls and responses