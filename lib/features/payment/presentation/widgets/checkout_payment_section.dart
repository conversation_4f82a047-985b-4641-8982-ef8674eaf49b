import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../widgets/figma_payment_form.dart';

/// Payment section widget for checkout screen
class CheckoutPaymentSection extends ConsumerStatefulWidget {
  final Function(bool) onCardValidationChanged;

  const CheckoutPaymentSection({
    super.key,
    required this.onCardValidationChanged,
  });

  @override
  ConsumerState<CheckoutPaymentSection> createState() => _CheckoutPaymentSectionState();
}

class _CheckoutPaymentSectionState extends ConsumerState<CheckoutPaymentSection> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Payment method header
          Text(
            'Payment Method',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Payment form
          FigmaPaymentForm(
            onValidationChanged: widget.onCardValidationChanged,
          ),
        ],
      ),
    );
  }
}
