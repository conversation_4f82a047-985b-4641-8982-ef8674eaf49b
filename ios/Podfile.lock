PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - connectivity_plus (0.0.1):
    - Flutter
  - Firebase/Auth (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.10.0)
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - firebase_auth (5.5.3):
    - Firebase/Auth (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_core (3.13.0):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - FirebaseAppCheckInterop (11.13.0)
  - FirebaseAuth (11.10.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.13.0)
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - Flutter (1.0.0)
  - flutter_native_splash (2.4.3):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - RecaptchaInterop (101.0.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - Stripe (24.7.0):
    - StripeApplePay (= 24.7.0)
    - StripeCore (= 24.7.0)
    - StripePayments (= 24.7.0)
    - StripePaymentsUI (= 24.7.0)
    - StripeUICore (= 24.7.0)
  - stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 24.7.0)
    - stripe_ios/stripe_ios (= 0.0.1)
    - stripe_ios/stripe_objc (= 0.0.1)
    - StripeApplePay (~> 24.7.0)
    - StripeFinancialConnections (~> 24.7.0)
    - StripePayments (~> 24.7.0)
    - StripePaymentSheet (~> 24.7.0)
    - StripePaymentsUI (~> 24.7.0)
  - stripe_ios/stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 24.7.0)
    - stripe_ios/stripe_objc
    - StripeApplePay (~> 24.7.0)
    - StripeFinancialConnections (~> 24.7.0)
    - StripePayments (~> 24.7.0)
    - StripePaymentSheet (~> 24.7.0)
    - StripePaymentsUI (~> 24.7.0)
  - stripe_ios/stripe_objc (0.0.1):
    - Flutter
    - Stripe (~> 24.7.0)
    - StripeApplePay (~> 24.7.0)
    - StripeFinancialConnections (~> 24.7.0)
    - StripePayments (~> 24.7.0)
    - StripePaymentSheet (~> 24.7.0)
    - StripePaymentsUI (~> 24.7.0)
  - StripeApplePay (24.7.0):
    - StripeCore (= 24.7.0)
  - StripeCore (24.7.0)
  - StripeFinancialConnections (24.7.0):
    - StripeCore (= 24.7.0)
    - StripeUICore (= 24.7.0)
  - StripePayments (24.7.0):
    - StripeCore (= 24.7.0)
    - StripePayments/Stripe3DS2 (= 24.7.0)
  - StripePayments/Stripe3DS2 (24.7.0):
    - StripeCore (= 24.7.0)
  - StripePaymentSheet (24.7.0):
    - StripeApplePay (= 24.7.0)
    - StripeCore (= 24.7.0)
    - StripePayments (= 24.7.0)
    - StripePaymentsUI (= 24.7.0)
  - StripePaymentsUI (24.7.0):
    - StripeCore (= 24.7.0)
    - StripePayments (= 24.7.0)
    - StripeUICore (= 24.7.0)
  - StripeUICore (24.7.0):
    - StripeCore (= 24.7.0)
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - Flutter (from `Flutter`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - stripe_ios (from `.symlinks/plugins/stripe_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - PromisesObjC
    - RecaptchaInterop
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  Flutter:
    :path: Flutter
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  stripe_ios:
    :path: ".symlinks/plugins/stripe_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_auth: 83bf106e5ac670dd3a0af27a86be6cba16a85723
  firebase_core: 2d4534e7b489907dcede540c835b48981d890943
  FirebaseAppCheckInterop: 72066489c209823649a997132bcd9269bc33a4bb
  FirebaseAuth: c4146bdfdc87329f9962babd24dae89373f49a32
  FirebaseAuthInterop: 4fa327ec3c551a80a6929561f83af80b1dd44937
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreExtension: 6f357679327f3614e995dc7cf3f2d600bdc774ac
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_native_splash: c32d145d68aeda5502d5f543ee38c192065986cf
  google_sign_in_ios: b48bb9af78576358a168361173155596c845f0b9
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  local_auth_darwin: 553ce4f9b16d3fdfeafce9cf042e7c9f77c1c391
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  Stripe: 8a03a78bfa16b197f9fac51e42670ac563b34388
  stripe_ios: 95bdf6ba58efd184fe1dfed194cd8692299d66ca
  StripeApplePay: 3c1b43d9b5130f6b714863bf8c9482c24168ab27
  StripeCore: 4955c2af14446db04818ad043d19d8f97b73c5fa
  StripeFinancialConnections: 8cf97b04c2f354879a2a5473126efac38f11f406
  StripePayments: 91820845bece6117809bcfdcaef39c84c2b4cae5
  StripePaymentSheet: 1810187cbdbc73410b8fb86cecafaaa41c1481fc
  StripePaymentsUI: 326376e23caa369d1f58041bdb858c89c2b17ed4
  StripeUICore: 17a4f3adb81ae05ab885e1b353022a430176eab1
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: 251cb053df7158f337c0712f2ab29f4e0fa474ce

COCOAPODS: 1.16.2
