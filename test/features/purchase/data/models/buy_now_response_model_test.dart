import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_travelgator/features/purchase/data/models/buy_now_response_model.dart';

void main() {
  group('BuyNowResponseModel', () {
    group('Payment Intent Response Format', () {
      test('should parse payment intent response correctly', () {
        final json = {
          'data': {
            'client_secret': 'pi_3RpnXZFgnEiwHY130NNj9bBk_secret_v0DHWeggVtjduwpgAm9aRtvJ8',
            'payment_intent_id': 'pi_3RpnXZFgnEiwHY130NNj9bBk',
            'order_id': '6887458177c4f29be5838649',
            'amount': 1.3,
            'currency': 'USD'
          }
        };

        final model = BuyNowResponseModel.fromJson(json);

        expect(model.data.orderId, equals('6887458177c4f29be5838649'));
        expect(model.data.currency, equals('USD'));
        expect(model.data.clientSecret, equals('pi_3RpnXZFgnEiwHY130NNj9bBk_secret_v0DHWeggVtjduwpgAm9aRtvJ8'));
        expect(model.data.paymentIntentId, equals('pi_3RpnXZFgnEiwHY130NNj9bBk'));
        expect(model.data.amount, equals(1.3));
        expect(model.data.isPaymentIntentResponse, isTrue);
        expect(model.data.isCheckoutUrlResponse, isFalse);
        expect(model.data.effectiveTotalPrice, equals(1.3));
      });

      test('should convert payment intent response to domain entity', () {
        final json = {
          'data': {
            'client_secret': 'pi_test_secret',
            'payment_intent_id': 'pi_test_id',
            'order_id': 'order_123',
            'amount': 29.99,
            'currency': 'USD'
          }
        };

        final model = BuyNowResponseModel.fromJson(json);
        final domain = model.toDomain();

        expect(domain.message, equals('Buy now successful'));
        expect(domain.orderId, equals('order_123'));
        expect(domain.totalPrice, equals(29.99));
        expect(domain.currency, equals('USD'));
        expect(domain.status, equals('success'));
        expect(domain.orderType, equals('buy_now'));
        expect(domain.checkoutUrl, equals('')); // Empty for payment intent responses
        expect(domain.shopifyCartId, equals('')); // Empty for payment intent responses
      });
    });

    group('Checkout URL Response Format (Legacy)', () {
      test('should parse checkout URL response correctly', () {
        final json = {
          'data': {
            'success': true,
            'checkout_url': 'https://checkout.stripe.com/pay/cs_test',
            'shopify_cart_id': 'cart_123',
            'total_price': 49.99,
            'currency': 'USD',
            'authenticated': true,
            'order_id': 'order_456',
            'local_order_created': true
          }
        };

        final model = BuyNowResponseModel.fromJson(json);

        expect(model.data.orderId, equals('order_456'));
        expect(model.data.currency, equals('USD'));
        expect(model.data.success, isTrue);
        expect(model.data.checkoutUrl, equals('https://checkout.stripe.com/pay/cs_test'));
        expect(model.data.shopifyCartId, equals('cart_123'));
        expect(model.data.totalPrice, equals(49.99));
        expect(model.data.isPaymentIntentResponse, isFalse);
        expect(model.data.isCheckoutUrlResponse, isTrue);
        expect(model.data.effectiveTotalPrice, equals(49.99));
      });

      test('should convert checkout URL response to domain entity', () {
        final json = {
          'data': {
            'success': true,
            'checkout_url': 'https://checkout.stripe.com/pay/cs_test',
            'shopify_cart_id': 'cart_123',
            'total_price': 49.99,
            'currency': 'USD',
            'authenticated': true,
            'order_id': 'order_456',
            'local_order_created': true
          }
        };

        final model = BuyNowResponseModel.fromJson(json);
        final domain = model.toDomain();

        expect(domain.message, equals('Buy now successful'));
        expect(domain.orderId, equals('order_456'));
        expect(domain.totalPrice, equals(49.99));
        expect(domain.currency, equals('USD'));
        expect(domain.status, equals('success'));
        expect(domain.orderType, equals('buy_now'));
        expect(domain.checkoutUrl, equals('https://checkout.stripe.com/pay/cs_test'));
        expect(domain.shopifyCartId, equals('cart_123'));
      });
    });

    group('Error Handling', () {
      test('should handle failed checkout URL response', () {
        final json = {
          'data': {
            'success': false,
            'checkout_url': '',
            'shopify_cart_id': '',
            'total_price': 0.0,
            'currency': 'USD',
            'authenticated': false,
            'order_id': 'failed_order',
            'local_order_created': false
          }
        };

        final model = BuyNowResponseModel.fromJson(json);
        final domain = model.toDomain();

        expect(domain.message, equals('Buy now failed'));
        expect(domain.status, equals('failed'));
      });

      test('should handle missing optional fields', () {
        final json = {
          'data': {
            'order_id': 'minimal_order',
            'currency': 'USD',
            'amount': 10.0
          }
        };

        final model = BuyNowResponseModel.fromJson(json);

        expect(model.data.orderId, equals('minimal_order'));
        expect(model.data.currency, equals('USD'));
        expect(model.data.amount, equals(10.0));
        expect(model.data.effectiveTotalPrice, equals(10.0));
      });
    });
  });
}
