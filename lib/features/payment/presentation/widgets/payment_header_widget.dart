import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Payment header widget following Figma design
class PaymentHeaderWidget extends StatelessWidget {
  const PaymentHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(8, 0, 8, 16),
      child: Container(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Payment title
            Text(
              'Payment',
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                height: 1.21,
                color: const Color(0xFF1F2937),
              ),
            ),
            
            // Security subtitle
            Text(
              'All transaction are secure and encrypted',
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                height: 1.21,
                color: const Color(0xFF9CA3AF),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
