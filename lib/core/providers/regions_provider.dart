import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/home/<USER>/models/region.dart';
import '../repositories/product_repository.dart';
import 'product_provider.dart';

// Regions state class
class RegionsState {
  final List<Region> regions;
  final bool isLoading;
  final String? error;
  final String? activeRegion;

  RegionsState({
    this.regions = const [],
    this.isLoading = false,
    this.error,
    this.activeRegion,
  });

  RegionsState copyWith({
    List<Region>? regions,
    bool? isLoading,
    String? error,
    String? activeRegion,
  }) {
    return RegionsState(
      regions: regions ?? this.regions,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      activeRegion: activeRegion ?? this.activeRegion,
    );
  }
}

// Regions notifier
class RegionsNotifier extends StateNotifier<RegionsState> {
  final ProductRepository _repository;

  RegionsNotifier(this._repository) : super(RegionsState());

  /// Helper method to convert technical errors to user-friendly messages
  String _getUserFriendlyError(dynamic error) {
    final errorString = error.toString();
    if (errorString.contains('Network connection error') ||
        errorString.contains('No internet connection')) {
      return 'No internet connection. Please check your network.';
    } else if (errorString.contains('timeout') || errorString.contains('timed out')) {
      return 'Request timed out. Please try again.';
    } else if (errorString.contains('Failed host lookup')) {
      return 'Unable to connect to server. Please try again.';
    }
    return 'Unable to load regions. Please try again.';
  }

  Future<void> fetchRegions() async {
    try {
      debugPrint('🌏 RegionsNotifier: Starting to fetch regions...');
      state = state.copyWith(isLoading: true, error: null);

      // Fetch regions from repository
      final regions = await _repository.getRegions();
      debugPrint('🌏 RegionsNotifier: Received ${regions.length} regions');
      for (var region in regions) {
        debugPrint('  - ${region.name} (${region.code})');
      }

      state = state.copyWith(regions: regions, isLoading: false);
    } catch (e) {
      debugPrint('🌏 RegionsNotifier: Error fetching regions: $e');
      state = state.copyWith(isLoading: false, error: _getUserFriendlyError(e));
    }
  }

  /// Force refresh regions from network, skipping any cache
  Future<void> forceRefreshRegions() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Clear GraphQL cache for this query (if applicable)
      await _repository.clearProductCache();

      final regions = await _repository.getRegions();

      state = state.copyWith(regions: regions, isLoading: false);
    } catch (e) {
      debugPrint('🌏 RegionsNotifier: Error force refreshing regions: $e');
      state = state.copyWith(isLoading: false, error: _getUserFriendlyError(e));
    }
  }

  /// Set active region
  void setActiveRegion(String? region) {
    state = state.copyWith(activeRegion: region);
  }

  /// Clear active region
  void clearActiveRegion() {
    state = state.copyWith(activeRegion: null);
  }
}

// Regions state provider
final regionsStateProvider =
    StateNotifierProvider<RegionsNotifier, RegionsState>((ref) {
      final repository = ref.watch(productRepositoryProvider);
      return RegionsNotifier(repository);
    });
