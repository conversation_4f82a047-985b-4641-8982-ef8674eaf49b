import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// API configuration for the backend authentication service
class ApiConfig {
  static const String baseUrl = 'https://dev-api.travelgator.makethatold.com'; // Change this to ngrok URL if needed
  // static const String baseUrl = 'http://************:3000'; // Change this to ngrok URL if needed

  static const String apiVersion = '/api';
  static const String authEndpoint = '/auth/authenticate';

  // Buy Now API endpoints
  static const String buyNowEndpoint = '/buy_now';
  static const String directCheckoutEndpoint = '/direct_checkout';

  // Stripe payment endpoints
  static const String stripeConfigEndpoint = '/stripe_payments/config';
  static const String verifyPaymentEndpoint = '/stripe_payments/verify_payment';

  // Current payment endpoints (documented API)
  static const String cartCheckoutEndpoint = '/cart/checkout';
  static const String cartConfirmPaymentEndpoint = '/cart/confirm_payment';
  static const String cartPaymentStatusEndpoint = '/cart/payment_status';
  static const String cartCancelPaymentEndpoint = '/cart/cancel_payment';
  static const String buyNowPaymentEndpoint = '/buy_now';
  static const String buyNowConfirmPaymentEndpoint = '/buy_now/confirm_payment';
  static const String buyNowPaymentStatusEndpoint = '/buy_now/payment_status';

  // User account endpoints
  static const String userDeletionCheckEndpoint = '/users/deletion_check';
  static const String userAccountEndpoint = '/users/account';

  // Order endpoints
  static const String ordersEndpoint = '/orders';
  
  // Timeouts
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds

  /// Create and configure Dio instance for backend API calls
  static Dio createDioClient() {
    final dio = Dio();
    
    // Base options
    dio.options = BaseOptions(
      baseUrl: baseUrl + apiVersion,
      connectTimeout: Duration(milliseconds: connectTimeout),
      receiveTimeout: Duration(milliseconds: receiveTimeout),
      sendTimeout: Duration(milliseconds: sendTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // Add simplified logging interceptor in debug mode
    if (kDebugMode) {
      dio.interceptors.add(InterceptorsWrapper(
        onRequest: (options, handler) {
          debugPrint('[API] ${options.method.toUpperCase()} ${options.uri}');
          handler.next(options);
        },
        onResponse: (response, handler) {
          debugPrint('[API] Status: ${response.statusCode}');
          debugPrint('[API] Response: ${response.data}');
          handler.next(response);
        },
        onError: (error, handler) {
          debugPrint('[API] Error: ${error.response?.statusCode} - ${error.message}');
          handler.next(error);
        },
      ));
    }

    return dio;
  }

  // Convenience getters for complete URLs
  static String get authUrl => baseUrl + apiVersion + authEndpoint;
  static String get stripeConfigUrl => baseUrl + apiVersion + stripeConfigEndpoint;
  static String get verifyPaymentUrl => baseUrl + apiVersion + verifyPaymentEndpoint;
  static String get cartCheckoutUrl => baseUrl + apiVersion + cartCheckoutEndpoint;
  static String get cartConfirmPaymentUrl => baseUrl + apiVersion + cartConfirmPaymentEndpoint;
  static String get cartPaymentStatusUrl => baseUrl + apiVersion + cartPaymentStatusEndpoint;
  static String get cartCancelPaymentUrl => baseUrl + apiVersion + cartCancelPaymentEndpoint;
  static String get buyNowPaymentUrl => baseUrl + apiVersion + buyNowPaymentEndpoint;
  static String get buyNowConfirmPaymentUrl => baseUrl + apiVersion + buyNowConfirmPaymentEndpoint;
  static String get buyNowPaymentStatusUrl => baseUrl + apiVersion + buyNowPaymentStatusEndpoint;
  static String get userDeletionCheckUrl => baseUrl + apiVersion + userDeletionCheckEndpoint;
  static String get userAccountUrl => baseUrl + apiVersion + userAccountEndpoint;
  static String get ordersUrl => baseUrl + apiVersion + ordersEndpoint;
}
