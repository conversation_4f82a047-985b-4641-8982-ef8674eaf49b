import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/utils/modal_manager.dart';
import '../services/legal_documents_service.dart';

/// Test screen to verify legal documents modal works from within another modal
/// This simulates the auth modal scenario
class ModalTestScreen extends StatelessWidget {
  const ModalTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Modal Test'),
        backgroundColor: const Color(0xFFFF6982),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test Legal Documents Modal from within another Modal',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),
            
            ElevatedButton(
              onPressed: () => _showTestModal(context),
              child: const Text('Show Test Modal (simulates Auth Modal)'),
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () {
                LegalDocumentsService.showLegalDocumentsModal(context);
              },
              child: const Text('Show Legal Documents Modal Directly'),
            ),
          ],
        ),
      ),
    );
  }

  void _showTestModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      useSafeArea: false,
      builder: (context) {
        return ManagedModal(
          child: _TestModal(),
        );
      },
    );
  }
}

class _TestModal extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.6,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -10),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: const Color(0xFFE5E7EB),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Test Modal (simulates Auth Modal)',
                      style: GoogleFonts.inter(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF111827),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Color(0xFF6B7280),
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),

            // Content with legal links
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    const Text(
                      'This modal simulates the auth modal scenario.',
                      style: TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 24),
                    
                    // Legal text with clickable links (similar to auth modal)
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          color: const Color(0xFF6B7280),
                          height: 1.4,
                        ),
                        children: [
                          const TextSpan(text: 'By continuing, you agree to our '),
                          TextSpan(
                            text: 'User Agreement',
                            style: GoogleFonts.inter(
                              decoration: TextDecoration.underline,
                              color: const Color(0xFF111827),
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                debugPrint('[TestModal] User Agreement link tapped');
                                debugPrint('[TestModal] Context mounted: ${context.mounted}');
                                LegalDocumentsService.showUserAgreementSafe(
                                  context,
                                  onError: () {
                                    debugPrint('[TestModal] Error showing User Agreement');
                                  },
                                );
                              },
                          ),
                          const TextSpan(text: ' and\nacknowledge that you understand the '),
                          TextSpan(
                            text: 'Privacy Policy',
                            style: GoogleFonts.inter(
                              decoration: TextDecoration.underline,
                              color: const Color(0xFF111827),
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                debugPrint('[TestModal] Privacy Policy link tapped');
                                debugPrint('[TestModal] Context mounted: ${context.mounted}');
                                LegalDocumentsService.showPrivacyPolicySafe(
                                  context,
                                  onError: () {
                                    debugPrint('[TestModal] Error showing Privacy Policy');
                                  },
                                );
                              },
                          ),
                          const TextSpan(text: '.'),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 32),
                    
                    ElevatedButton(
                      onPressed: () {
                        debugPrint('[TestModal] Direct call to legal documents modal');
                        LegalDocumentsService.showLegalDocumentsModal(context);
                      },
                      child: const Text('Direct Call to Legal Documents Modal'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
