<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="32" height="32" fill="#002868" rx="16"/>
  
  <!-- White circle -->
  <circle cx="16" cy="16" r="10" fill="#FFFFFF"/>
  
  <!-- <PERSON> symbol (simplified) -->
  <g transform="translate(16,16)">
    <!-- <PERSON> body -->
    <ellipse cx="0" cy="1" rx="3" ry="4" fill="#002868"/>
    <!-- <PERSON> head -->
    <circle cx="0" cy="-2" r="2.5" fill="#002868"/>
    <!-- Beak -->
    <path d="M0,-4 L2,-3 L0,-2 Z" fill="#FFD700"/>
    <!-- Eye -->
    <circle cx="-0.5" cy="-2.5" r="0.5" fill="#FFFFFF"/>
    <!-- Wings -->
    <path d="M-3,0 Q-6,-2 -5,-4 Q-3,-2 -3,0" fill="#002868"/>
    <path d="M3,0 Q6,-2 5,-4 Q3,-2 3,0" fill="#002868"/>
    <!-- Stars -->
    <g fill="#FFFFFF">
      <polygon points="-6,-6 -5.5,-4.5 -4,-4.5 -5,-3 -4.5,-1.5 -6,-2.5 -7.5,-1.5 -7,-3 -8,-4.5 -6.5,-4.5"/>
      <polygon points="6,-6 6.5,-4.5 8,-4.5 7,-3 7.5,-1.5 6,-2.5 4.5,-1.5 5,-3 4,-4.5 5.5,-4.5"/>
    </g>
  </g>
</svg>
