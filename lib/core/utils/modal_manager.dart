import 'package:flutter/material.dart';

/// Global modal management system to track and control open modals/popups
class ModalManager {
  static final ModalManager _instance = ModalManager._internal();
  factory ModalManager() => _instance;
  ModalManager._internal();

  /// List of currently open modal contexts
  final List<BuildContext> _openModals = [];

  /// Register a modal as open
  void registerModal(BuildContext context) {
    if (!_openModals.contains(context)) {
      _openModals.add(context);
      // debugPrint('[ModalManager] Modal registered. Total open modals: ${_openModals.length}');
    }
  }

  /// Unregister a modal when it closes
  void unregisterModal(BuildContext context) {
    _openModals.remove(context);
    // debugPrint('[ModalManager] Modal unregistered. Total open modals: ${_openModals.length}');
  }

  /// Close all open modals
  void closeAllModals() {
    debugPrint('[ModalManager] Closing all modals. Count: ${_openModals.length}');
    
    // Create a copy of the list to avoid modification during iteration
    final modalsToClose = List<BuildContext>.from(_openModals);
    
    for (final context in modalsToClose) {
      try {
        if (context.mounted && Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
          debugPrint('[ModalManager] Modal closed successfully');
        }
      } catch (e) {
        debugPrint('[ModalManager] Error closing modal: $e');
      }
    }
    
    // Clear the list after attempting to close all modals
    _openModals.clear();
    debugPrint('[ModalManager] All modals closed. Remaining count: ${_openModals.length}');
  }

  /// Get count of open modals
  int get openModalCount => _openModals.length;

  /// Check if any modals are open
  bool get hasOpenModals => _openModals.isNotEmpty;
}

/// Extension to easily register/unregister modals
extension ModalManagerExtension on BuildContext {
  /// Register this context as an open modal
  void registerAsModal() {
    ModalManager().registerModal(this);
  }

  /// Unregister this context as a modal
  void unregisterAsModal() {
    ModalManager().unregisterModal(this);
  }
}

/// Wrapper widget that automatically manages modal registration
class ManagedModal extends StatefulWidget {
  final Widget child;
  
  const ManagedModal({
    super.key,
    required this.child,
  });

  @override
  State<ManagedModal> createState() => _ManagedModalState();
}

class _ManagedModalState extends State<ManagedModal> {
  @override
  void initState() {
    super.initState();
    // Register modal after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.registerAsModal();
      }
    });
  }

  @override
  void dispose() {
    // Unregister modal when disposed
    context.unregisterAsModal();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
