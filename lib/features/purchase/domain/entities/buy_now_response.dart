import 'package:equatable/equatable.dart';

/// Domain entity representing the response from Buy Now API
class BuyNowResponse extends Equatable {
  final String message;
  final String checkoutUrl;
  final String orderId;
  final String shopifyCartId;
  final double totalPrice;
  final String currency;
  final String status;
  final String orderType;
  final BuyNowItem item;

  // Payment intent fields for in-app payment flow
  final String? clientSecret;
  final String? paymentIntentId;

  const BuyNowResponse({
    required this.message,
    required this.checkoutUrl,
    required this.orderId,
    required this.shopifyCartId,
    required this.totalPrice,
    required this.currency,
    required this.status,
    required this.orderType,
    required this.item,
    this.clientSecret,
    this.paymentIntentId,
  });

  @override
  List<Object?> get props => [
        message,
        checkoutUrl,
        orderId,
        shopifyCartId,
        totalPrice,
        currency,
        status,
        orderType,
        item,
        clientSecret,
        paymentIntentId,
      ];

  /// Check if this response contains payment intent data for in-app payment
  bool get hasPaymentIntent => clientSecret != null && paymentIntentId != null;

  /// Check if this response contains checkout URL for web-based payment
  bool get hasCheckoutUrl => checkoutUrl.isNotEmpty;

  @override
  String toString() {
    return 'BuyNowResponse(message: $message, checkoutUrl: $checkoutUrl, orderId: $orderId, shopifyCartId: $shopifyCartId, totalPrice: $totalPrice, currency: $currency, status: $status, orderType: $orderType, item: $item)';
  }
}

/// Domain entity representing an item in the Buy Now response
class BuyNowItem extends Equatable {
  final String variantId;
  final String productId;
  final int quantity;
  final double price;
  final String title;

  const BuyNowItem({
    required this.variantId,
    required this.productId,
    required this.quantity,
    required this.price,
    required this.title,
  });

  @override
  List<Object?> get props => [
        variantId,
        productId,
        quantity,
        price,
        title,
      ];

  @override
  String toString() {
    return 'BuyNowItem(variantId: $variantId, productId: $productId, quantity: $quantity, price: $price, title: $title)';
  }
}
