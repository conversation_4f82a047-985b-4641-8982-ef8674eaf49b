import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import '../models/user_model.dart';
import 'auth_data_source.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/utils/apple_debug_helper.dart';

class FirebaseAuthDataSource implements AuthDataSource {
  final firebase_auth.FirebaseAuth _firebaseAuth;
  final GoogleSignIn? _googleSignIn;

  // Constants for configuration
  static const String _webClientId =
      '448909230583-n412h8gb3nfdepgcotvmo0pcq3fssm62.apps.googleusercontent.com';

  FirebaseAuthDataSource({
    firebase_auth.FirebaseAuth? firebaseAuth,
    GoogleSignIn? googleSignIn,
  }) : _firebaseAuth = firebaseAuth ?? firebase_auth.FirebaseAuth.instance,
       _googleSignIn = googleSignIn ?? _createGoogleSignIn();

  // Helper method to safely create GoogleSignIn instance
  static GoogleSignIn? _createGoogleSignIn() {
    try {
      // Different configuration based on platform
      if (kIsWeb) {
        // Web-specific configuration
        debugPrint('Creating GoogleSignIn for Web with clientId');
        return GoogleSignIn(clientId: _webClientId);
      } else if (defaultTargetPlatform == TargetPlatform.android) {
        // For Android, use default constructor for now to fix crash
        debugPrint(
          'Creating GoogleSignIn for Android with default constructor',
        );
        return GoogleSignIn();
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        // iOS doesn't need serverClientId in the constructor
        debugPrint('Creating GoogleSignIn for iOS');
        return GoogleSignIn();
      } else {
        debugPrint(
          'Google Sign-In not supported on this platform: $defaultTargetPlatform',
        );
        return null;
      }
    } catch (e) {
      debugPrint('Error creating GoogleSignIn: $e');
      return null;
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    final firebaseUser = _firebaseAuth.currentUser;
    if (firebaseUser == null) {
      return null;
    }
    return _mapFirebaseUserToUserModel(firebaseUser);
  }

  @override
  Future<void> signOut() async {
    try {
      // Sign out from Google if signed in with Google
      if (_googleSignIn != null) {
        try {
          await _googleSignIn.signOut();
        } catch (e) {
          debugPrint('Error signing out from Google: $e');
          // Continue to Firebase signOut even if Google signOut fails
        }
      }

      // Sign out from Firebase
      await _firebaseAuth.signOut();
    } catch (e) {
      throw const AuthException('Sign out failed');
    }
  }

  @override
  Future<UserModel> signInWithGoogle() async {
    try {
      // Check if Google Sign-In is available on this platform
      if (_googleSignIn == null) {
        debugPrint('Google Sign-In is not available on this platform');
        throw const AuthException(
          'Google Sign-In is not available on this platform',
        );
      }

      // Additional safeguards for Google Sign-In
      try {
        debugPrint('Platform: ${defaultTargetPlatform.toString()}');
        debugPrint(
          'Attempting Google Sign-In on ${defaultTargetPlatform.toString()}',
        );

        // Trigger the authentication flow with explicit exception handling
        debugPrint('Calling _googleSignIn.signIn()...');
        final GoogleSignInAccount? googleUser = await _googleSignIn
            .signIn()
            .catchError((error) {
              debugPrint('Google sign-in process error: $error');
              throw AuthException(
                'Error during Google sign-in process: ${error.toString()}',
              );
            });

        if (googleUser == null) {
          debugPrint('Google sign-in was cancelled by user');
          throw const AuthException('Google sign-in was cancelled by user');
        }

        debugPrint(
          'Google Sign-In successful. User email: ${googleUser.email}',
        );
        debugPrint('Google Sign-In user ID: ${googleUser.id}');

        // Obtain the auth details from the Google sign-in
        debugPrint('Getting authentication tokens...');
        final GoogleSignInAuthentication
        googleAuth = await googleUser.authentication.catchError((error) {
          debugPrint('Error getting Google auth tokens: $error');
          throw AuthException(
            'Error getting Google authentication tokens: ${error.toString()}',
          );
        });

        // Verify we have the necessary tokens
        debugPrint(
          'Checking for tokens: ID Token: ${googleAuth.idToken != null ? 'Present' : 'Missing'}, Access Token: ${googleAuth.accessToken != null ? 'Present' : 'Missing'}',
        );

        if (googleAuth.idToken == null && googleAuth.accessToken == null) {
          debugPrint('Missing Google authentication tokens');
          throw const AuthException('Missing Google authentication tokens');
        }

        // Create a new credential
        debugPrint('Creating Firebase credential with tokens');
        final credential = firebase_auth.GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );

        // Sign in to Firebase with the Google credential
        debugPrint('Signing in to Firebase with Google credential');
        final userCredential = await _firebaseAuth
            .signInWithCredential(credential)
            .catchError((error) {
              debugPrint(
                'Firebase sign-in with Google credential error: $error',
              );
              throw AuthException(
                'Error signing in with Google credentials: ${error.toString()}',
              );
            });

        final user = userCredential.user;

        if (user == null) {
          debugPrint('Google sign in failed - no user returned');
          throw const AuthException('Google sign in failed');
        }

        debugPrint('Firebase authentication successful. User UID: ${user.uid}');
        return _mapFirebaseUserToUserModel(user);
      } catch (e) {
        debugPrint('Error in Google sign-in flow: $e');
        if (e is AuthException) {
          rethrow;
        }
        throw AuthException('Google sign-in error: ${e.toString()}');
      }
    } on firebase_auth.FirebaseAuthException catch (e) {
      debugPrint(
        'FirebaseAuthException during Google sign-in: ${e.code} - ${e.message}',
      );
      throw AuthException(_getErrorMessage(e));
    } catch (e) {
      debugPrint('Unexpected error during Google sign-in: $e');
      if (e is AuthException) {
        rethrow;
      }
      throw AuthException('Failed to sign in with Google: ${e.toString()}');
    }
  }

  // Helper method to map Firebase User to our UserModel
  UserModel _mapFirebaseUserToUserModel(firebase_auth.User user) {
    return UserModel(
      id: user.uid,
      email: user.email ?? '',
      name: user.displayName ?? '',
      profilePhoto: user.photoURL,
    );
  }

  // Helper method to get user-friendly error messages
  String _getErrorMessage(firebase_auth.FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email';
      case 'wrong-password':
        return 'Wrong password';
      case 'email-already-in-use':
        return 'Email is already in use by another account';
      case 'weak-password':
        return 'The password is too weak';
      case 'invalid-email':
        return 'The email address is invalid';
      case 'operation-not-allowed':
        return 'This operation is not allowed';
      default:
        return e.message ?? 'An error occurred';
    }
  }

  @override
  Future<UserModel> signInWithApple() async {
    try {
      debugPrint('=== STARTING APPLE SIGN-IN PROCESS ===');
      debugPrint('Platform: ${defaultTargetPlatform.toString()}');
      debugPrint('Current time: ${DateTime.now().toIso8601String()}');

      // Check if Apple Sign-In is available
      debugPrint('Checking Apple Sign-In availability...');
      final isAvailable = await SignInWithApple.isAvailable();
      debugPrint('Apple Sign-In availability: $isAvailable');

      if (!isAvailable) {
        debugPrint('❌ Apple Sign-In is not available on this device');
        debugPrint('Requirements: iOS 13+, physical device, iCloud signed in');
        throw const AuthException('Apple Sign-In is not available on this device. This feature requires iOS 13+ and proper Apple Developer configuration.');
      }

      // Trigger the Apple Sign-In flow with comprehensive debugging
      debugPrint('✅ Apple Sign-In is available, proceeding with credential request...');

      final appleCredential = await AppleDebugHelper.debugAppleSignInFlow(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      ).catchError((error, stackTrace) {
        // Use debug helper for error logging
        AppleDebugHelper.debugAppleError(error, stackTrace);

        // Provide specific error messages based on error type
        if (error.toString().contains('1000')) {
          throw const AuthException(
            'Apple Sign-In configuration error. Please ensure:\n'
            '1. App is properly configured in Apple Developer Console\n'
            '2. Sign In with Apple capability is enabled in Xcode\n'
            '3. App is running on a physical device with iOS 13+\n'
            '4. Device is signed in to iCloud'
          );
        } else if (error.toString().contains('1001')) {
          throw const AuthException('Apple Sign-In was canceled by user');
        } else {
          throw AuthException(
            'Apple Sign-In error: ${error.toString()}\n'
            'Please check Apple Developer Console and Xcode configuration.',
          );
        }
      });

      debugPrint('✅ Apple Sign-In credential obtained successfully');

      // Use debug helper for comprehensive credential logging
      AppleDebugHelper.debugAppleCredential(appleCredential);

      // Create a new credential for Firebase
      debugPrint('🔥 Creating Firebase credential with Apple tokens...');

      final oauthCredential = firebase_auth.OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );
      debugPrint('✅ Firebase OAuth credential created successfully');

      // Sign in to Firebase with the Apple credential
      debugPrint('🔥 Signing in to Firebase with Apple credential...');
      final userCredential = await _firebaseAuth
          .signInWithCredential(oauthCredential)
          .catchError((error) {
            debugPrint('❌ Firebase sign-in with Apple credential error: $error');
            debugPrint('Error type: ${error.runtimeType}');
            if (error is firebase_auth.FirebaseAuthException) {
              debugPrint('Firebase error code: ${error.code}');
              debugPrint('Firebase error message: ${error.message}');
            }
            throw AuthException(
              'Error signing in with Apple credentials: ${error.toString()}',
            );
          });

      final user = userCredential.user;

      if (user == null) {
        debugPrint('❌ Apple sign in failed - no user returned from Firebase');
        throw const AuthException('Apple sign in failed');
      }

      debugPrint('✅ Firebase authentication successful!');
      debugPrint('User details:');
      debugPrint('  - UID: ${user.uid}');
      debugPrint('  - Email: ${user.email ?? 'null'}');
      debugPrint('  - Display Name: ${user.displayName ?? 'null'}');
      debugPrint('  - Photo URL: ${user.photoURL ?? 'null'}');
      debugPrint('  - Email Verified: ${user.emailVerified}');
      debugPrint('  - Provider Data: ${user.providerData.map((p) => p.providerId).toList()}');

      // Use Apple credential name if Firebase user doesn't have a display name
      String displayName = user.displayName ?? '';
      if (displayName.isEmpty && (appleCredential.givenName != null || appleCredential.familyName != null)) {
        displayName = '${appleCredential.givenName ?? ''} ${appleCredential.familyName ?? ''}'.trim();
        debugPrint('Using Apple credential name: $displayName');
      }

      final userModel = UserModel(
        id: user.uid,
        email: user.email ?? '',
        name: displayName,
        profilePhoto: user.photoURL,
      );

      debugPrint('✅ UserModel created successfully');
      debugPrint('=== APPLE SIGN-IN COMPLETED SUCCESSFULLY ===');

      return userModel;
    } on firebase_auth.FirebaseAuthException catch (e) {
      debugPrint(
        'FirebaseAuthException during Apple sign-in: ${e.code} - ${e.message}',
      );
      throw AuthException(_getErrorMessage(e));
    } catch (e) {
      debugPrint('Unexpected error during Apple sign-in: $e');
      if (e is AuthException) {
        rethrow;
      }
      throw AuthException('Failed to sign in with Apple: ${e.toString()}');
    }
  }

  @override
  firebase_auth.User? getCurrentFirebaseUser() {
    return _firebaseAuth.currentUser;
  }
}
