import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// Service for handling voucher-related API operations
class VoucherService {
  final Dio _dio;

  VoucherService({required Dio dio}) : _dio = dio;

  /// Validate a voucher code
  Future<VoucherValidationResult> validateVoucher({
    required String code,
    required double orderAmount,
    required String authToken,
  }) async {
    try {
      debugPrint('[VoucherService] Validating voucher: $code for amount: \$${orderAmount.toStringAsFixed(2)}');
      
      final response = await _dio.post(
        '/vouchers/validate_code',
        data: {
          'code': code,
          'order_amount': orderAmount,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[VoucherService] Voucher validation response: ${response.statusCode}');
      debugPrint('[VoucherService] Response data: ${response.data}');

      if (response.statusCode == 200 && response.data['data'] != null) {
        final data = response.data['data'] as Map<String, dynamic>;
        return VoucherValidationResult(
          isValid: data['valid'] as bool,
          discountAmount: (data['discount_amount'] as num?)?.toDouble() ?? 0.0,
          finalAmount: (data['final_amount'] as num?)?.toDouble() ?? orderAmount,
          errorMessage: null,
        );
      } else {
        return VoucherValidationResult(
          isValid: false,
          discountAmount: 0.0,
          finalAmount: orderAmount,
          errorMessage: response.data['message'] ?? 'Invalid voucher code',
        );
      }
    } on DioException catch (e) {
      debugPrint('[VoucherService] Voucher validation error: ${e.message}');
      debugPrint('[VoucherService] Response status: ${e.response?.statusCode}');
      debugPrint('[VoucherService] Response data: ${e.response?.data}');

      String errorMessage = 'Failed to validate voucher';

      if (e.response?.statusCode == 400) {
        errorMessage = e.response?.data['message'] ?? 'Invalid voucher code';
      } else if (e.response?.statusCode == 404) {
        errorMessage = 'Voucher code not found';
      } else if (e.response?.statusCode == 401) {
        errorMessage = 'Authentication required';
      } else if (e.response?.statusCode == 422) {
        // Handle validation errors (like minimum order amount)
        final responseData = e.response?.data;
        if (responseData is Map<String, dynamic>) {
          errorMessage = responseData['error'] ?? responseData['message'] ?? 'Validation error';
        } else if (responseData is String) {
          errorMessage = responseData;
        } else {
          errorMessage = 'Order validation failed';
        }
      }

      return VoucherValidationResult(
        isValid: false,
        discountAmount: 0.0,
        finalAmount: orderAmount,
        errorMessage: errorMessage,
      );
    } catch (e) {
      debugPrint('[VoucherService] Unexpected error: $e');
      return VoucherValidationResult(
        isValid: false,
        discountAmount: 0.0,
        finalAmount: orderAmount,
        errorMessage: 'An unexpected error occurred',
      );
    }
  }

  /// Apply voucher to cart
  Future<VoucherApplicationResult> applyVoucherToCart({
    required String voucherCode,
    required String authToken,
  }) async {
    try {
      debugPrint('[VoucherService] Applying voucher to cart: $voucherCode');
      
      final response = await _dio.post(
        '/cart/apply_voucher',
        data: {
          'voucher_code': voucherCode,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[VoucherService] Apply voucher response: ${response.statusCode}');
      debugPrint('[VoucherService] Response data: ${response.data}');

      if (response.statusCode == 200 && response.data['data'] != null) {
        final responseData = response.data['data'] as Map<String, dynamic>;
        return VoucherApplicationResult(
          success: true,
          discountAmount: (responseData['discount_amount'] as num?)?.toDouble() ?? 0.0,
          finalAmount: (responseData['final_amount'] as num?)?.toDouble() ?? 0.0,
          cartData: responseData['cart'] as Map<String, dynamic>?,
          errorMessage: null,
        );
      } else {
        return VoucherApplicationResult(
          success: false,
          discountAmount: 0.0,
          finalAmount: 0.0,
          cartData: null,
          errorMessage: response.data['message'] ?? 'Failed to apply voucher',
        );
      }
    } on DioException catch (e) {
      debugPrint('[VoucherService] Apply voucher error: ${e.message}');
      debugPrint('[VoucherService] Response status: ${e.response?.statusCode}');
      debugPrint('[VoucherService] Response data: ${e.response?.data}');

      String errorMessage = 'Failed to apply voucher';

      if (e.response?.statusCode == 400) {
        // Handle product-specific discount validation errors
        final responseData = e.response?.data;
        if (responseData is Map<String, dynamic>) {
          // Check for specific error messages from Shopify integration
          if (responseData['error'] != null) {
            errorMessage = responseData['error'];
          } else if (responseData['message'] != null) {
            errorMessage = responseData['message'];
          } else {
            errorMessage = 'Invalid voucher code';
          }
        } else {
          errorMessage = 'Invalid voucher code';
        }
      } else if (e.response?.statusCode == 404) {
        errorMessage = 'Voucher code not found';
      } else if (e.response?.statusCode == 401) {
        errorMessage = 'Authentication required';
      } else if (e.response?.statusCode == 422) {
        // Handle validation errors (like minimum order amount or product eligibility)
        final responseData = e.response?.data;
        if (responseData is Map<String, dynamic>) {
          errorMessage = responseData['error'] ?? responseData['message'] ?? 'Validation error';
        } else if (responseData is String) {
          errorMessage = responseData;
        } else {
          errorMessage = 'Order validation failed';
        }
      }

      return VoucherApplicationResult(
        success: false,
        discountAmount: 0.0,
        finalAmount: 0.0,
        cartData: null,
        errorMessage: errorMessage,
      );
    } catch (e) {
      debugPrint('[VoucherService] Unexpected error: $e');
      return VoucherApplicationResult(
        success: false,
        discountAmount: 0.0,
        finalAmount: 0.0,
        cartData: null,
        errorMessage: 'An unexpected error occurred',
      );
    }
  }

  /// Remove voucher from cart
  Future<VoucherRemovalResult> removeVoucherFromCart({
    required String authToken,
  }) async {
    try {
      debugPrint('[VoucherService] Removing voucher from cart');
      
      final response = await _dio.delete(
        '/cart/remove_voucher',
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[VoucherService] Remove voucher response: ${response.statusCode}');
      debugPrint('[VoucherService] Response data: ${response.data}');

      if (response.statusCode == 200 && response.data['data'] != null) {
        final data = response.data['data'] as Map<String, dynamic>;
        return VoucherRemovalResult(
          success: true,
          totalPrice: (data['total_price'] as num?)?.toDouble() ?? 0.0,
          errorMessage: null,
        );
      } else {
        return VoucherRemovalResult(
          success: false,
          totalPrice: 0.0,
          errorMessage: response.data['message'] ?? 'Failed to remove voucher',
        );
      }
    } on DioException catch (e) {
      debugPrint('[VoucherService] Remove voucher error: ${e.message}');
      return VoucherRemovalResult(
        success: false,
        totalPrice: 0.0,
        errorMessage: e.response?.data['message'] ?? 'Failed to remove voucher',
      );
    } catch (e) {
      debugPrint('[VoucherService] Unexpected error: $e');
      return VoucherRemovalResult(
        success: false,
        totalPrice: 0.0,
        errorMessage: 'An unexpected error occurred',
      );
    }
  }
}

/// Result of voucher validation
class VoucherValidationResult {
  final bool isValid;
  final double discountAmount;
  final double finalAmount;
  final String? errorMessage;

  const VoucherValidationResult({
    required this.isValid,
    required this.discountAmount,
    required this.finalAmount,
    this.errorMessage,
  });
}

/// Result of voucher application
class VoucherApplicationResult {
  final bool success;
  final double discountAmount;
  final double finalAmount;
  final Map<String, dynamic>? cartData;
  final String? errorMessage;

  const VoucherApplicationResult({
    required this.success,
    required this.discountAmount,
    required this.finalAmount,
    this.cartData,
    this.errorMessage,
  });
}

/// Result of voucher removal
class VoucherRemovalResult {
  final bool success;
  final double totalPrice;
  final String? errorMessage;

  const VoucherRemovalResult({
    required this.success,
    required this.totalPrice,
    this.errorMessage,
  });
}
