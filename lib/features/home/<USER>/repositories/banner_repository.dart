import 'package:graphql_flutter/graphql_flutter.dart';
import '../../../../core/graphql/queries.dart';
import '../../domain/models/banner.dart';

class BannerRepository {
  final GraphQLClient client;

  BannerRepository({required this.client});

  /// Fetch all banners
  Future<List<Banner>> getBanners({int first = 10}) async {
    try {
      // debugPrint('🎯 BannerRepository: Fetching banners...');
      
      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getBanners),
        variables: {'first': first},
        fetchPolicy: FetchPolicy.cacheFirst,
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        // debugPrint('🎯 BannerRepository: GraphQL Exception: ${result.exception}');
        // Handle different types of GraphQL exceptions gracefully
        final exception = result.exception!;
        if (exception.linkException != null) {
          final linkException = exception.linkException!;
          if (linkException.toString().contains('Failed host lookup') ||
              linkException.toString().contains('Operation timed out') ||
              linkException.toString().contains('No stream event')) {
            // debugPrint('🎯 BannerRepository: Network connectivity issue detected');
            throw Exception('Network connection error. Please check your internet connection.');
          }
        }
        throw Exception('Failed to load banners. Please try again later.');
      }

      final bannersData = result.data?['metaobjects'];
      if (bannersData == null) {
        // debugPrint('🎯 BannerRepository: No banners data returned');
        return [];
      }

      // debugPrint('🎯 BannerRepository: Raw banners data: $bannersData');

      final BannersConnection bannersConnection = BannersConnection.fromJson(bannersData);
      final banners = bannersConnection.edges.map((edge) => edge.node).toList();

      // debugPrint('🎯 BannerRepository: Successfully fetched ${banners.length} banners');
      // for (final banner in banners) {
      //   debugPrint('🎯   Banner: ${banner.handle} - ${banner.imageUrl}');
      // }

      return banners;
    } on Exception catch (e) {
      // Catch any Exception (including ServerException)
      // debugPrint('🎯 BannerRepository: Caught exception: $e');
      if (e.toString().contains('Network connection error')) {
        rethrow; // Keep our custom network error message
      }
      // Return user-friendly message for any other exception
      throw Exception('Failed to load banners. Please try again later.');
    } catch (e) {
      // Catch any other errors (non-Exception types)
      // debugPrint('🎯 BannerRepository: Caught error: $e');
      throw Exception('Failed to load banners. Please try again later.');
    }
  }

  /// Fetch a specific banner by handle
  Future<Banner?> getBannerByHandle(String handle) async {
    try {
      // debugPrint('🎯 BannerRepository: Fetching banner by handle: $handle');
      
      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getBannerByHandle),
        variables: {'handle': handle},
        fetchPolicy: FetchPolicy.cacheFirst,
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        // debugPrint('🎯 BannerRepository: GraphQL Exception: ${result.exception}');
        throw Exception(result.exception.toString());
      }

      final bannerData = result.data?['metaobject'];
      if (bannerData == null) {
        // debugPrint('🎯 BannerRepository: No banner found with handle: $handle');
        return null;
      }

      // debugPrint('🎯 BannerRepository: Raw banner data: $bannerData');

      final banner = Banner.fromGraphQL(bannerData);
      // debugPrint('🎯 BannerRepository: Successfully fetched banner: ${banner.handle}');

      return banner;
    } on Exception catch (e) {
      // Catch any Exception (including ServerException)
      // debugPrint('🎯 BannerRepository: Caught exception: $e');
      if (e.toString().contains('Network connection error')) {
        rethrow; // Keep our custom network error message
      }
      // Return user-friendly message for any other exception
      throw Exception('Failed to load banner. Please try again later.');
    } catch (e) {
      // Catch any other errors (non-Exception types)
      // debugPrint('🎯 BannerRepository: Caught error: $e');
      throw Exception('Failed to load banner. Please try again later.');
    }
  }

  /// Force refresh banners from network
  Future<List<Banner>> forceRefreshBanners({int first = 10}) async {
    try {
      // debugPrint('🎯 BannerRepository: Force refreshing banners...');
      
      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getBanners),
        variables: {'first': first},
        fetchPolicy: FetchPolicy.networkOnly, // Force network fetch
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        // debugPrint('🎯 BannerRepository: GraphQL Exception: ${result.exception}');
        // Handle different types of GraphQL exceptions gracefully
        final exception = result.exception!;
        if (exception.linkException != null) {
          final linkException = exception.linkException!;
          if (linkException.toString().contains('Failed host lookup') ||
              linkException.toString().contains('Operation timed out') ||
              linkException.toString().contains('No stream event')) {
            // debugPrint('🎯 BannerRepository: Network connectivity issue detected');
            throw Exception('Network connection error. Please check your internet connection.');
          }
        }
        throw Exception('Failed to load banners. Please try again later.');
      }

      final bannersData = result.data?['metaobjects'];
      if (bannersData == null) {
        // debugPrint('🎯 BannerRepository: No banners data returned');
        return [];
      }

      final BannersConnection bannersConnection = BannersConnection.fromJson(bannersData);
      final banners = bannersConnection.edges.map((edge) => edge.node).toList();

      // debugPrint('🎯 BannerRepository: Force refresh completed - ${banners.length} banners');

      return banners;
    } on Exception catch (e) {
      // Catch any Exception (including ServerException)
      // debugPrint('🎯 BannerRepository: Caught exception: $e');
      if (e.toString().contains('Network connection error')) {
        rethrow; // Keep our custom network error message
      }
      // Return user-friendly message for any other exception
      throw Exception('Failed to load banners. Please try again later.');
    } catch (e) {
      // Catch any other errors (non-Exception types)
      // debugPrint('🎯 BannerRepository: Caught error: $e');
      throw Exception('Failed to load banners. Please try again later.');
    }
  }
}
