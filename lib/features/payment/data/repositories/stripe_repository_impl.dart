import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_stripe/flutter_stripe.dart' as stripe;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../../../../core/errors/failures.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../../../auth/data/models/enhanced_user_model.dart';
import '../../domain/entities/stripe_config.dart';
import '../../domain/entities/payment_result.dart';
import '../../domain/repositories/stripe_repository.dart';
import '../../domain/exceptions/payment_exceptions.dart';
import '../datasources/stripe_data_source.dart';

/// Implementation of Stripe repository
class StripeRepositoryImpl implements StripeRepository {
  final StripeDataSource _dataSource;
  final Ref _ref;

  StripeRepositoryImpl({
    required StripeDataSource dataSource,
    required Ref ref,
  }) : _dataSource = dataSource, _ref = ref;

  @override
  Future<Either<Failure, StripeConfig>> initializeStripe() async {
    try {
      // Get Stripe configuration from backend
      final configResult = await getStripeConfig();

      return configResult.fold(
        (failure) async {
          debugPrint('[StripeRepository] Backend config failed: ${failure.message}');
          debugPrint('[StripeRepository] Using fallback configuration...');

          // Use a working test key instead of placeholder
          // Note: This should be replaced with the actual Stripe test key from your Stripe dashboard
          final fallbackConfig = StripeConfig(
            publishableKey: _getStripePublishableKey(),
            merchantId: 'merchant.com.travelgator.development',
            merchantDisplayName: 'TravelGator',
            countryCode: 'US',
            currency: 'USD',
            testMode: true,
          );

          // Validate the API key format before using it
          if (!_isValidStripeKey(fallbackConfig.publishableKey)) {
            debugPrint('[StripeRepository] ❌ Invalid Stripe API key format: ${fallbackConfig.publishableKey}');
            return Left(PaymentFailure(
              message: 'Invalid Stripe API key. Please configure a valid publishable key.',
            ));
          }

          try {
            // Initialize Stripe SDK with fallback key
            stripe.Stripe.publishableKey = fallbackConfig.publishableKey;

            // Set merchant identifier for Apple Pay (if available)
            if (fallbackConfig.merchantId.isNotEmpty) {
              stripe.Stripe.merchantIdentifier = fallbackConfig.merchantId;
            }

            debugPrint('[StripeRepository] ✅ Stripe initialized with fallback configuration');
            debugPrint('[StripeRepository] ✅ Test mode: ${fallbackConfig.testMode}');
            debugPrint('[StripeRepository] ✅ Key prefix: ${fallbackConfig.publishableKey.substring(0, 20)}...');

            return Right(fallbackConfig);
          } catch (e) {
            debugPrint('[StripeRepository] Failed to initialize Stripe with fallback: $e');
            return Left(PaymentFailure(
              message: 'Failed to initialize Stripe: $e',
            ));
          }
        },
        (config) async {
          // Validate the backend-provided API key
          if (!_isValidStripeKey(config.publishableKey)) {
            debugPrint('[StripeRepository] ❌ Invalid Stripe API key from backend: ${config.publishableKey}');
            return Left(PaymentFailure(
              message: 'Invalid Stripe API key received from backend.',
            ));
          }

          try {
            // Initialize Stripe SDK with publishable key
            stripe.Stripe.publishableKey = config.publishableKey;

            // Set merchant identifier for Apple Pay (if available)
            if (config.merchantId.isNotEmpty) {
              stripe.Stripe.merchantIdentifier = config.merchantId;
            }

            debugPrint('[StripeRepository] ✅ Stripe initialized with backend configuration');
            debugPrint('[StripeRepository] ✅ Test mode: ${config.testMode}');
            debugPrint('[StripeRepository] ✅ Key prefix: ${config.publishableKey.substring(0, 20)}...');

            return Right(config);
          } catch (e) {
            debugPrint('[StripeRepository] Failed to initialize Stripe: $e');
            return Left(PaymentFailure(
              message: 'Failed to initialize Stripe: $e',
            ));
          }
        },
      );
    } catch (e) {
      debugPrint('[StripeRepository] Unexpected error during initialization: $e');
      return Left(PaymentFailure(
        message: 'Unexpected error during Stripe initialization: $e',
      ));
    }
  }

  @override
  Future<Either<Failure, StripeConfig>> getStripeConfig() async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      final configModel = await _dataSource.getStripeConfig(authToken);
      return Right(configModel.toDomain());
    } on PaymentException catch (e) {
      debugPrint('[StripeRepository] Payment exception: $e');
      return Left(PaymentFailure.fromException(e));
    } catch (e) {
      debugPrint('[StripeRepository] Unexpected error: $e');
      return Left(PaymentFailure(message: 'Failed to get Stripe config: $e'));
    }
  }

  @override
  Future<Either<Failure, StripeConfig>> refreshStripeConfig() async {
    // For now, just call getStripeConfig again
    // In the future, this could implement caching logic
    return getStripeConfig();
  }

  @override
  Future<Either<Failure, PaymentResult>> processPayment({
    required String clientSecret,
    required String paymentIntentId,
  }) async {
    try {
      debugPrint('[StripeRepository] Processing payment with Payment Element...');
      debugPrint('[StripeRepository] Payment Intent ID: $paymentIntentId');

      // Confirm payment using Payment Element approach
      final paymentIntent = await stripe.Stripe.instance.confirmPayment(
        paymentIntentClientSecret: clientSecret,
        data: const stripe.PaymentMethodParams.card(
          paymentMethodData: stripe.PaymentMethodData(),
        ),
      );

      debugPrint('[StripeRepository] Payment Element confirmation completed successfully');
      debugPrint('[StripeRepository] Payment status: ${paymentIntent.status}');

      // Verify payment with backend
      return verifyPayment(paymentIntentId: paymentIntentId);
    } on stripe.StripeException catch (e) {
      debugPrint('[StripeRepository] Stripe exception: ${e.error}');

      if (e.error.code == stripe.FailureCode.Canceled) {
        return Left(PaymentFailure(message: 'Payment was canceled'));
      } else {
        return Left(PaymentFailure(message: 'Payment failed: ${e.error.message}'));
      }
    } catch (e) {
      debugPrint('[StripeRepository] Unexpected error during payment: $e');
      return Left(PaymentFailure(message: 'Payment processing failed: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentResult>> verifyPayment({
    required String paymentIntentId,
  }) async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      debugPrint('[StripeRepository] Verifying payment: $paymentIntentId');

      final resultModel = await _dataSource.verifyPayment(
        paymentIntentId: paymentIntentId,
        authToken: authToken,
      );

      return Right(resultModel.toDomain());
    } on PaymentException catch (e) {
      debugPrint('[StripeRepository] Payment exception: $e');
      return Left(PaymentFailure.fromException(e));
    } catch (e) {
      debugPrint('[StripeRepository] Unexpected error: $e');
      return Left(PaymentFailure(message: 'Failed to verify payment: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentResult>> handlePaymentSheetResult({
    required String paymentIntentId,
    required bool isCompleted,
    String? error,
  }) async {
    if (!isCompleted) {
      if (error != null) {
        return Left(PaymentFailure(message: 'Payment failed: $error'));
      } else {
        return Left(PaymentFailure(message: 'Payment was canceled'));
      }
    }

    // If completed, verify the payment
    return verifyPayment(paymentIntentId: paymentIntentId);
  }

  /// Get authentication token from current user
  String? _getAuthToken() {
    final authState = _ref.read(authNotifierProvider);
    return authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          if (enhancedUser.hasBackendAuth && 
              enhancedUser.backendToken != null && 
              enhancedUser.backendToken!.isNotEmpty) {
            return enhancedUser.backendToken;
          }
        }
        return null;
      },
      orElse: () => null,
    );
  }

  /// Get Stripe publishable key from environment variables or .env file
  String _getStripePublishableKey() {
    // First try to get from dart-define (runtime environment variable)
    const dartDefineKey = String.fromEnvironment('STRIPE_PUBLISHABLE_KEY');
    if (dartDefineKey.isNotEmpty && dartDefineKey != 'pk_test_51HPjZnL..._test_key_from_stripe_dashboard') {
      debugPrint('[StripeRepository] Using Stripe key from dart-define');
      return dartDefineKey;
    }

    // Then try to get from .env file
    final envKey = dotenv.env['STRIPE_PUBLISHABLE_KEY'];
    if (envKey != null && envKey.isNotEmpty) {
      debugPrint('[StripeRepository] Using Stripe key from .env file');
      return envKey;
    }

    // Fallback to placeholder (will cause validation error)
    debugPrint('[StripeRepository] ⚠️ No valid Stripe key found, using placeholder');
    return 'pk_test_51HPjZnL..._test_key_from_stripe_dashboard';
  }

  /// Validate Stripe publishable key format
  bool _isValidStripeKey(String key) {
    // Check if it's a valid Stripe test or live key format
    return key.startsWith('pk_test_') || key.startsWith('pk_live_');
  }
}
