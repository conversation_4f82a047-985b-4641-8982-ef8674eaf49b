import '../../domain/entities/legal_document.dart';
import '../../domain/entities/legal_document_type.dart';
import '../../domain/entities/legal_document_section.dart';

/// Repository for managing legal documents content
class LegalDocumentsRepository {
  /// Get all available legal documents for a specific locale
  List<LegalDocument> getAllDocuments({String languageCode = 'en'}) {
    return [
      _getUserAgreement(languageCode: languageCode),
      _getPrivacyPolicy(languageCode: languageCode),
    ];
  }

  /// Get a specific document by type for a specific locale
  LegalDocument? getDocumentByType(
    LegalDocumentType type, {
    String languageCode = 'en',
  }) {
    switch (type) {
      case LegalDocumentType.userAgreement:
        return _getUserAgreement(languageCode: languageCode);
      case LegalDocumentType.privacyPolicy:
        return _getPrivacyPolicy(languageCode: languageCode);
    }
  }

  /// Create User Agreement document with placeholder content
  LegalDocument _getUserAgreement({String languageCode = 'en'}) {
    return LegalDocument(
      type: LegalDocumentType.userAgreement,
      title: _getUserAgreementTitle(languageCode),
      lastUpdated: _getLastUpdatedText(languageCode),
      sections: _getUserAgreementSections(languageCode),
    );
  }

  /// Create Privacy Policy document with placeholder content
  LegalDocument _getPrivacyPolicy({String languageCode = 'en'}) {
    return LegalDocument(
      type: LegalDocumentType.privacyPolicy,
      title: _getPrivacyPolicyTitle(languageCode),
      lastUpdated: _getLastUpdatedText(languageCode),
      sections: _getPrivacyPolicySections(languageCode),
    );
  }

  // Helper methods for localized content

  String _getUserAgreementTitle(String languageCode) {
    switch (languageCode) {
      case 'vi':
        return 'Thỏa Thuận Người Dùng';
      case 'en':
      default:
        return 'User Agreement';
    }
  }

  String _getPrivacyPolicyTitle(String languageCode) {
    switch (languageCode) {
      case 'vi':
        return 'Chính Sách Bảo Mật';
      case 'en':
      default:
        return 'Privacy Policy';
    }
  }

  String _getLastUpdatedText(String languageCode) {
    switch (languageCode) {
      case 'vi':
        return '20 tháng 6, 2025';
      case 'en':
      default:
        return 'June 20, 2025';
    }
  }

  List<LegalDocumentSection> _getUserAgreementSections(String languageCode) {
    switch (languageCode) {
      case 'vi':
        return _getUserAgreementSectionsVi();
      case 'en':
      default:
        return _getUserAgreementSectionsEn();
    }
  }

  List<LegalDocumentSection> _getPrivacyPolicySections(String languageCode) {
    switch (languageCode) {
      case 'vi':
        return _getPrivacyPolicySectionsVi();
      case 'en':
      default:
        return _getPrivacyPolicySectionsEn();
    }
  }

  // English content methods
  List<LegalDocumentSection> _getUserAgreementSectionsEn() {
    return [
      const LegalDocumentSection(
        title: 'Terms of Use (User Agreement)',
        content: '''Effective Date: June 20, 2025
Contact Email: <EMAIL>

Welcome to Travel Gator! By using this app, you agree to the following terms:''',
        order: 1,
      ),
      const LegalDocumentSection(
        title: '1. Purpose',
        content: '''Travel Gator is a mobile platform that allows users to explore and purchase travel-related products such as eSIMs, through an integrated Shopify checkout.''',
        order: 2,
      ),
      const LegalDocumentSection(
        title: '2. Eligibility',
        content: '''You must be at least 18 years old to use this app. By using Travel Gator, you confirm that the information you provide is accurate and complete.''',
        order: 3,
      ),
      const LegalDocumentSection(
        title: '3. Account Responsibility',
        content: '''You are responsible for maintaining the security of your account. Please do not share your login credentials. If you suspect unauthorized access, contact us immediately.''',
        order: 4,
      ),
      const LegalDocumentSection(
        title: '4. Purchases and Payment',
        content: '''All purchases are processed through our Shopify storefront. Pricing, currencies, and applicable fees are displayed during checkout. All sales are final unless otherwise stated.''',
        order: 5,
      ),
      const LegalDocumentSection(
        title: '5. Intellectual Property',
        content: '''All content in the app is owned by Travel Gator or its partners. You may not copy, reuse, or redistribute any part of the app without written permission.''',
        order: 6,
      ),
      const LegalDocumentSection(
        title: '6. Termination',
        content: '''We may suspend or terminate your access at any time for violations of these terms or misuse of our services.''',
        order: 7,
      ),
      const LegalDocumentSection(
        title: '7. Changes to Terms',
        content: '''We may update these terms as needed. Continued use of the app means you accept any changes made.''',
        order: 8,
      ),
    ];
  }

  List<LegalDocumentSection> _getUserAgreementSectionsVi() {
    return [
      const LegalDocumentSection(
        title: 'Điều Khoản Sử Dụng (Thỏa Thuận Người Dùng)',
        content: '''Ngày có hiệu lực: 20 tháng 6, 2025
Email liên hệ: <EMAIL>

Chào mừng đến với Travel Gator! Bằng việc sử dụng ứng dụng này, bạn đồng ý với các điều khoản sau:''',
        order: 1,
      ),
      const LegalDocumentSection(
        title: '1. Mục Đích',
        content: '''Travel Gator là một nền tảng di động cho phép người dùng khám phá và mua các sản phẩm liên quan đến du lịch như eSIM, thông qua hệ thống thanh toán Shopify tích hợp.''',
        order: 2,
      ),
      const LegalDocumentSection(
        title: '2. Điều Kiện Tham Gia',
        content: '''Bạn phải ít nhất 18 tuổi để sử dụng ứng dụng này. Bằng việc sử dụng Travel Gator, bạn xác nhận rằng thông tin bạn cung cấp là chính xác và đầy đủ.''',
        order: 3,
      ),
      const LegalDocumentSection(
        title: '3. Trách Nhiệm Tài Khoản',
        content: '''Bạn có trách nhiệm duy trì bảo mật tài khoản của mình. Vui lòng không chia sẻ thông tin đăng nhập của bạn. Nếu bạn nghi ngờ có truy cập trái phép, hãy liên hệ với chúng tôi ngay lập tức.''',
        order: 4,
      ),
      const LegalDocumentSection(
        title: '4. Mua Hàng và Thanh Toán',
        content: '''Tất cả các giao dịch mua hàng được xử lý thông qua cửa hàng Shopify của chúng tôi. Giá cả, tiền tệ và phí áp dụng được hiển thị trong quá trình thanh toán. Tất cả các giao dịch bán hàng là cuối cùng trừ khi có quy định khác.''',
        order: 5,
      ),
      const LegalDocumentSection(
        title: '5. Sở Hữu Trí Tuệ',
        content: '''Tất cả nội dung trong ứng dụng thuộc sở hữu của Travel Gator hoặc các đối tác của chúng tôi. Bạn không được sao chép, tái sử dụng hoặc phân phối lại bất kỳ phần nào của ứng dụng mà không có sự cho phép bằng văn bản.''',
        order: 6,
      ),
      const LegalDocumentSection(
        title: '6. Chấm Dứt',
        content: '''Chúng tôi có thể tạm ngừng hoặc chấm dứt quyền truy cập của bạn bất cứ lúc nào vì vi phạm các điều khoản này hoặc lạm dụng dịch vụ của chúng tôi.''',
        order: 7,
      ),
      const LegalDocumentSection(
        title: '7. Thay Đổi Điều Khoản',
        content: '''Chúng tôi có thể cập nhật các điều khoản này khi cần thiết. Việc tiếp tục sử dụng ứng dụng có nghĩa là bạn chấp nhận bất kỳ thay đổi nào được thực hiện.''',
        order: 8,
      ),
    ];
  }

  List<LegalDocumentSection> _getPrivacyPolicySectionsEn() {
    return [
      const LegalDocumentSection(
        title: 'Privacy Policy',
        content: '''Effective Date: June 20, 2025
Contact Email: <EMAIL>

Travel Gator is committed to protecting your privacy. This policy explains what data we collect and how we use it.''',
        order: 1,
      ),
      const LegalDocumentSection(
        title: '1. What We Collect',
        content: '''Name and email address (via Google login)

Device data (for performance analytics)

Purchase history (via Shopify checkout)

We do not collect sensitive personal data without your consent.''',
        order: 2,
      ),
      const LegalDocumentSection(
        title: '2. How We Use Your Data',
        content: '''To provide access to your account

To process purchases and support orders

To improve app performance and user experience

We do not sell or share your data with external advertisers.''',
        order: 3,
      ),
      const LegalDocumentSection(
        title: '3. Third-Party Services',
        content: '''We use:

Firebase (authentication & analytics)

Shopify (checkout system)

These services may store limited user data under their own privacy terms.''',
        order: 4,
      ),
      const LegalDocumentSection(
        title: '4. Your Rights',
        content: '''You may request access to or deletion of your data by contacting <NAME_EMAIL>''',
        order: 5,
      ),
      const LegalDocumentSection(
        title: '5. Security',
        content: '''We use standard security measures to protect your data. However, no digital platform is 100% secure, so we recommend keeping your login private.''',
        order: 6,
      ),
      const LegalDocumentSection(
        title: '6. Updates',
        content: '''We may update this Privacy Policy in the future. Any changes will be reflected in the app.''',
        order: 7,
      ),
    ];
  }

  List<LegalDocumentSection> _getPrivacyPolicySectionsVi() {
    return [
      const LegalDocumentSection(
        title: 'Chính Sách Bảo Mật',
        content: '''Ngày có hiệu lực: 20 tháng 6, 2025
Email liên hệ: <EMAIL>

Travel Gator cam kết bảo vệ quyền riêng tư của bạn. Chính sách này giải thích dữ liệu chúng tôi thu thập và cách chúng tôi sử dụng nó.''',
        order: 1,
      ),
      const LegalDocumentSection(
        title: '1. Những Gì Chúng Tôi Thu Thập',
        content: '''Tên và địa chỉ email (thông qua đăng nhập Google)

Dữ liệu thiết bị (để phân tích hiệu suất)

Lịch sử mua hàng (thông qua thanh toán Shopify)

Chúng tôi không thu thập dữ liệu cá nhân nhạy cảm mà không có sự đồng ý của bạn.''',
        order: 2,
      ),
      const LegalDocumentSection(
        title: '2. Cách Chúng Tôi Sử Dụng Dữ Liệu Của Bạn',
        content: '''Để cung cấp quyền truy cập vào tài khoản của bạn

Để xử lý mua hàng và hỗ trợ đơn hàng

Để cải thiện hiệu suất ứng dụng và trải nghiệm người dùng

Chúng tôi không bán hoặc chia sẻ dữ liệu của bạn với các nhà quảng cáo bên ngoài.''',
        order: 3,
      ),
      const LegalDocumentSection(
        title: '3. Dịch Vụ Bên Thứ Ba',
        content: '''Chúng tôi sử dụng:

Firebase (xác thực & phân tích)

Shopify (hệ thống thanh toán)

Các dịch vụ này có thể lưu trữ dữ liệu người dùng hạn chế theo các điều khoản bảo mật riêng của họ.''',
        order: 4,
      ),
      const LegalDocumentSection(
        title: '4. Quyền Của Bạn',
        content: '''Bạn có thể yêu cầu truy cập hoặc xóa dữ liệu của mình bằng cách liên hệ với chúng tôi tại <EMAIL>''',
        order: 5,
      ),
      const LegalDocumentSection(
        title: '5. Bảo Mật',
        content: '''Chúng tôi sử dụng các biện pháp bảo mật tiêu chuẩn để bảo vệ dữ liệu của bạn. Tuy nhiên, không có nền tảng kỹ thuật số nào an toàn 100%, vì vậy chúng tôi khuyến nghị bạn giữ thông tin đăng nhập của mình ở chế độ riêng tư.''',
        order: 6,
      ),
      const LegalDocumentSection(
        title: '6. Cập Nhật',
        content: '''Chúng tôi có thể cập nhật Chính sách Bảo mật này trong tương lai. Bất kỳ thay đổi nào sẽ được phản ánh trong ứng dụng.''',
        order: 7,
      ),
    ];
  }
}
