import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/network/api_config.dart';

/// Response model for deletion check API
class DeletionCheckResponse {
  final bool canDelete;
  final String message;
  final List<String>? errors;

  DeletionCheckResponse({
    required this.canDelete,
    required this.message,
    this.errors,
  });

  factory DeletionCheckResponse.fromJson(Map<String, dynamic> json) {
    return DeletionCheckResponse(
      canDelete: json['can_delete'] ?? false,
      message: json['message'] ?? '',
      errors: json['errors'] != null 
          ? List<String>.from(json['errors']) 
          : null,
    );
  }
}

/// Response model for account deletion API
class AccountDeletionResponse {
  final String message;
  final Map<String, dynamic>? details;

  AccountDeletionResponse({
    required this.message,
    this.details,
  });

  factory AccountDeletionResponse.fromJson(Map<String, dynamic> json) {
    return AccountDeletionResponse(
      message: json['message'] ?? '',
      details: json['details'],
    );
  }
}

/// Exception thrown when account deletion operations fail
class AccountDeletionException implements Exception {
  final String message;
  final int? statusCode;
  final List<String>? reasons;
  final Object? originalError;

  AccountDeletionException(
    this.message, {
    this.statusCode,
    this.reasons,
    this.originalError,
  });

  @override
  String toString() => 'AccountDeletionException: $message';
}

/// Service for handling account deletion API calls
class DeleteAccountService {
  final Dio _dio;

  DeleteAccountService({Dio? dio}) : _dio = dio ?? ApiConfig.createDioClient();

  /// Check if account can be deleted
  Future<DeletionCheckResponse> checkDeletionEligibility(String backendToken) async {
    try {
      debugPrint('[DeleteAccount] Checking deletion eligibility...');
      debugPrint('[DeleteAccount] Endpoint: ${ApiConfig.userDeletionCheckUrl}');

      final response = await _dio.get(
        ApiConfig.userDeletionCheckEndpoint,
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
          },
        ),
      );

      debugPrint('[DeleteAccount] Deletion check response status: ${response.statusCode}');
      debugPrint('[DeleteAccount] Deletion check response data: ${response.data}');

      if (response.statusCode == 200) {
        final deletionResponse = DeletionCheckResponse.fromJson(
          response.data as Map<String, dynamic>,
        );

        debugPrint('[DeleteAccount] Can delete: ${deletionResponse.canDelete}');
        debugPrint('[DeleteAccount] Message: ${deletionResponse.message}');
        if (deletionResponse.errors != null) {
          debugPrint('[DeleteAccount] Errors: ${deletionResponse.errors}');
        }

        return deletionResponse;
      } else {
        debugPrint('[DeleteAccount] Unexpected status code: ${response.statusCode}');
        throw AccountDeletionException(
          'Deletion check failed with status ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      debugPrint('[DeleteAccount] Deletion check DioException: ${e.message}');
      debugPrint('[DeleteAccount] Error type: ${e.type}');
      debugPrint('[DeleteAccount] Response: ${e.response?.data}');

      final statusCode = e.response?.statusCode;

      // Handle 422 responses that contain business logic (not errors)
      if (statusCode == 422 && e.response?.data != null) {
        final responseData = e.response!.data;
        if (responseData is Map<String, dynamic>) {
          // Check if this is a business logic response (has can_delete field)
          if (responseData.containsKey('can_delete')) {
            debugPrint('[DeleteAccount] 422 response contains business logic, treating as valid response');
            final deletionResponse = DeletionCheckResponse.fromJson(responseData);

            debugPrint('[DeleteAccount] Can delete: ${deletionResponse.canDelete}');
            debugPrint('[DeleteAccount] Message: ${deletionResponse.message}');
            if (deletionResponse.errors != null) {
              debugPrint('[DeleteAccount] Errors: ${deletionResponse.errors}');
            }

            return deletionResponse;
          }

          // Check if this is a token error
          final error = responseData['error']?.toString() ?? '';
          if (error.toLowerCase().contains('invalid token')) {
            throw AccountDeletionException(
              'Invalid or expired token',
              statusCode: statusCode,
              originalError: e,
            );
          }
        }
      }

      String errorMessage;
      if (statusCode == 401) {
        errorMessage = 'Unauthorized - please sign in again';
      } else if (statusCode == 403) {
        errorMessage = 'Access denied';
      } else {
        errorMessage = _extractErrorMessage(e);
      }

      throw AccountDeletionException(
        errorMessage,
        statusCode: statusCode,
        originalError: e,
      );
    } catch (e) {
      debugPrint('[DeleteAccount] Unexpected error during deletion check: $e');
      throw AccountDeletionException(
        'Unexpected error during deletion check: $e',
        originalError: e,
      );
    }
  }

  /// Delete user account
  Future<AccountDeletionResponse> deleteAccount(String backendToken) async {
    try {
      debugPrint('[DeleteAccount] Starting account deletion...');
      debugPrint('[DeleteAccount] Endpoint: ${ApiConfig.userAccountUrl}');

      final response = await _dio.delete(
        ApiConfig.userAccountEndpoint,
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
          },
        ),
      );

      debugPrint('[DeleteAccount] Account deletion response status: ${response.statusCode}');
      debugPrint('[DeleteAccount] Account deletion response data: ${response.data}');

      if (response.statusCode == 200) {
        final deletionResponse = AccountDeletionResponse.fromJson(
          response.data as Map<String, dynamic>,
        );

        debugPrint('[DeleteAccount] Account deletion successful');
        debugPrint('[DeleteAccount] Message: ${deletionResponse.message}');
        debugPrint('[DeleteAccount] Details: ${deletionResponse.details}');

        return deletionResponse;
      } else {
        debugPrint('[DeleteAccount] Unexpected status code: ${response.statusCode}');
        throw AccountDeletionException(
          'Account deletion failed with status ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      debugPrint('[DeleteAccount] Account deletion DioException: ${e.message}');
      debugPrint('[DeleteAccount] Error type: ${e.type}');
      debugPrint('[DeleteAccount] Response: ${e.response?.data}');

      final statusCode = e.response?.statusCode;
      String errorMessage;
      List<String>? reasons;

      if (statusCode == 401) {
        errorMessage = 'Unauthorized - please sign in again';
      } else if (statusCode == 403) {
        errorMessage = 'Access denied';
      } else if (statusCode == 422) {
        // Handle 422 as potential token expiration
        final responseData = e.response?.data;
        if (responseData != null && responseData is Map<String, dynamic>) {
          final error = responseData['error']?.toString() ?? '';
          if (error.toLowerCase().contains('invalid token')) {
            errorMessage = 'Invalid or expired token';
          } else {
            errorMessage = error.isNotEmpty ? error : 'Invalid request';
          }
        } else {
          errorMessage = 'Invalid request';
        }
      } else if (statusCode == 500) {
        // Handle server errors with user-friendly message
        final responseData = e.response?.data;
        if (responseData != null && responseData is Map<String, dynamic>) {
          final supportMessage = responseData['support_message']?.toString();
          if (supportMessage != null && supportMessage.isNotEmpty) {
            errorMessage = supportMessage;
          } else {
            errorMessage = 'Server error occurred. Please try again later or contact support.';
          }
        } else {
          errorMessage = 'Server error occurred. Please try again later or contact support.';
        }
      } else if (statusCode == 400 && e.response?.data != null) {
        // Handle error response with reasons
        final responseData = e.response!.data;
        errorMessage = responseData['error'] ?? 'Account deletion failed';
        if (responseData['reasons'] != null) {
          reasons = List<String>.from(responseData['reasons']);
        }
      } else {
        errorMessage = _extractErrorMessage(e);
      }

      throw AccountDeletionException(
        errorMessage,
        statusCode: statusCode,
        reasons: reasons,
        originalError: e,
      );
    } catch (e) {
      debugPrint('[DeleteAccount] Unexpected error during account deletion: $e');
      throw AccountDeletionException(
        'Unexpected error during account deletion: $e',
        originalError: e,
      );
    }
  }

  /// Extract error message from DioException
  String _extractErrorMessage(DioException e) {
    if (e.response?.data != null) {
      final data = e.response!.data;
      if (data is Map<String, dynamic>) {
        return data['message'] ?? data['error'] ?? e.message ?? 'Unknown error';
      }
    }
    return e.message ?? 'Network error';
  }
}
