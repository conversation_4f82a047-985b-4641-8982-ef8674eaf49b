import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import '../../../../l10n/app_localizations.dart';

class CartHeader extends StatelessWidget implements PreferredSizeWidget {
  const CartHeader({super.key});

  @override
  Size get preferredSize => const Size.fromHeight(86); // 44px status bar + 42px header body

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFFF6982), // Pink color from Figma
      child: SafeArea(
        child: SizedBox(
          height: 42, // Header body height from Figma
          child: Row(
        children: [
          // Left icon container (52x42 with 5px 10px padding)
          Container(
            width: 52,
            height: 42,
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: GestureDetector(
              onTap: () {
                if (context.canPop()) {
                  context.pop();
                } else {
                  context.go('/');
                }
              },
              child: Center(
                child: Icon(
                  Icons.arrow_back_ios,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),

          // Center content with 40px left padding
          Expanded(
            child: Container(
              padding: const EdgeInsets.only(left: 40),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    height: 42,
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 9),
                    child: Center(
                      child: Text(
                        AppLocalizations.of(context).cart,
                        style: GoogleFonts.inter(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Right spacing to balance the layout (removed cart icon)
          const SizedBox(
            width: 52, // Same width as left container for balance
            height: 42,
          ),
        ],
      ),
        ),
      ),
    );
  }
}
