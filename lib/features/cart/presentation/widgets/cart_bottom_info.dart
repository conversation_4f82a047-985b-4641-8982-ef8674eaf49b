import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:auto_size_text/auto_size_text.dart';
import '../../../../l10n/app_localizations.dart';

class CartBottomInfo extends StatelessWidget {
  final bool isAllSelected;
  final Function(bool) onSelectAllChanged;
  final double totalPrice;
  final VoidCallback onCheckout;
  final bool hasSelectedItems;

  const CartBottomInfo({
    super.key,
    required this.isAllSelected,
    required this.onSelectAllChanged,
    required this.totalPrice,
    required this.onCheckout,
    this.hasSelectedItems = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity, // Full width instead of fixed 375px
      decoration: const BoxDecoration(
        color: Color(0xFFFFFFFF), // #FFFFFF from Figma
        border: Border(
          top: BorderSide(
            color: Color(0xFFE5E7EB), // Border color from Figma
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 32), // Exact padding from Figma
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Checkbox with improved touch target
              GestureDetector(
                onTap: () => onSelectAllChanged(!isAllSelected),
                child: Container(
                  width: 44, // Minimum touch target size for mobile
                  height: 44, // Minimum touch target size for mobile
                  alignment: Alignment.center,
                  color: Colors.transparent, // Ensures the full area is tappable
                  child: SizedBox(
                    width: 16, // Visual size remains the same as Figma
                    height: 16, // Visual size remains the same as Figma
                    child: Checkbox(
                      value: isAllSelected,
                      onChanged: (value) => onSelectAllChanged(value ?? false),
                      activeColor: const Color(0xFFFF6982),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4), // 4px border radius from Figma
                      ),
                      side: const BorderSide(
                        color: Color(0xFF9CA3AF), // Stroke color from Figma
                        width: 1,
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 16), // Gap from Figma

              // Payment Section - Expanded to fill
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8), // Padding from Figma
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8), // 8px border radius from Figma
                  ),
                  child: Row(
                    children: [
                      // Total label
                      Text(
                        AppLocalizations.of(context).total, // Added colon as in Figma
                        style: GoogleFonts.inter(
                          fontSize: 14, // Font size from Figma
                          fontWeight: FontWeight.w400, // Font weight from Figma
                          color: const Color(0xFF1F2937), // Text color from Figma
                          height: 1.2102272851126534, // Line height from Figma
                        ),
                      ),

                      const SizedBox(width: 8), // Gap from Figma

                      // Total price - Expanded to align right
                      Expanded(
                        child: AutoSizeText(
                          '\$${totalPrice.toStringAsFixed(2)}',
                          style: GoogleFonts.inter(
                            fontSize: 18, // Font size from Figma
                            fontWeight: FontWeight.w600, // Font weight from Figma
                            color: const Color(0xFFFF6982), // Color from Figma
                            height: 1.2102272245619032, // Line height from Figma
                          ),
                          textAlign: TextAlign.left, // Right alignment from Figma
                          maxLines: 1,
                          minFontSize: 12,
                          maxFontSize: 18,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 16), // Gap from Figma

              // Checkout Buttons - Expanded to fill remaining space
              Expanded(
                child: Row(
                  children: [
                    // Traditional Checkout Button
                    Expanded(
                      child: GestureDetector(
                        onTap: hasSelectedItems ? onCheckout : null,
                        child: Container(
                          padding: const EdgeInsets.all(12), // Padding from Figma
                          decoration: BoxDecoration(
                            color: hasSelectedItems
                                ? const Color(0xFFFF6982) // Active color from Figma
                                : const Color(0xFFD1D5DB), // Disabled color
                            borderRadius: BorderRadius.circular(6), // 6px border radius from Figma
                            border: Border.all(
                              color: hasSelectedItems
                                  ? const Color(0xFFFF6982) // Active border color from Figma
                                  : const Color(0xFFD1D5DB), // Disabled border color
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 4), // Text padding from Figma
                                child: Text(
                                  AppLocalizations.of(context).checkOut,
                                  style: GoogleFonts.inter(
                                    fontSize: 14, // Font size from Figma
                                    fontWeight: FontWeight.w700, // Font weight from Figma
                                    color: hasSelectedItems
                                        ? const Color(0xFFFFFFFF) // White text from Figma
                                        : const Color(0xFF9CA3AF), // Disabled text color
                                    letterSpacing: 0.005, // 0.5% letter spacing from Figma
                                    height: 1.4285714285714286, // Line height from Figma
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),


                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
