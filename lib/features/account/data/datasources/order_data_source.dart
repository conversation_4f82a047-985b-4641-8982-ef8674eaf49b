import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/order_model.dart';
import '../models/discount_stats_model.dart';
import '../../../../core/network/api_config.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../../../auth/data/models/enhanced_user_model.dart';

/// Abstract data source for order operations
abstract class OrderDataSource {
  Future<OrdersResponse> getOrders();
  Future<OrderResponse> getOrderById(String orderId);
  Future<DiscountStatsModel> getDiscountStats();
}

/// Implementation of order data source using Dio
class OrderDataSourceImpl implements OrderDataSource {
  final Dio dio;
  final Ref ref;

  OrderDataSourceImpl({required this.dio, required this.ref});

  @override
  Future<OrdersResponse> getOrders() async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        throw Exception('User not authenticated');
      }

      final response = await dio.get(
        ApiConfig.ordersEndpoint,
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
          },
        ),
      );

      // Debug logging
      debugPrint('[OrderDataSource] Raw response data: ${response.data}');
      debugPrint('[OrderDataSource] Response type: ${response.data.runtimeType}');

      return OrdersResponse.fromJson(response.data);
    } on DioException catch (e) {
      debugPrint('[OrderDataSource] DioException: ${e.message}');
      debugPrint('[OrderDataSource] Response data: ${e.response?.data}');
      throw _handleDioException(e);
    } catch (e, stackTrace) {
      debugPrint('[OrderDataSource] Exception: $e');
      debugPrint('[OrderDataSource] Stack trace: $stackTrace');
      throw Exception('Failed to fetch orders: $e');
    }
  }

  @override
  Future<OrderResponse> getOrderById(String orderId) async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        throw Exception('User not authenticated');
      }

      final response = await dio.get(
        '${ApiConfig.ordersEndpoint}/$orderId',
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
          },
        ),
      );
      return OrderResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw Exception('Failed to fetch order: $e');
    }
  }

  @override
  Future<DiscountStatsModel> getDiscountStats() async {
    try {
      final authToken = _getAuthToken();
      if (authToken == null) {
        throw Exception('User not authenticated');
      }

      final response = await dio.get(
        '${ApiConfig.ordersEndpoint}/discount_stats',
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
          },
        ),
      );

      debugPrint('[OrderDataSource] Discount stats response: ${response.data}');
      return DiscountStatsModel.fromJson(response.data['data']);
    } on DioException catch (e) {
      debugPrint('[OrderDataSource] DioException fetching discount stats: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[OrderDataSource] Exception fetching discount stats: $e');
      throw Exception('Failed to fetch discount stats: $e');
    }
  }

  /// Get authentication token from current user
  String? _getAuthToken() {
    final authState = ref.read(authNotifierProvider);
    return authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          if (enhancedUser.hasBackendAuth &&
              enhancedUser.backendToken != null &&
              enhancedUser.backendToken!.isNotEmpty) {
            return enhancedUser.backendToken;
          }
        }
        return null;
      },
      orElse: () => null,
    );
  }

  Exception _handleDioException(DioException e) {
    switch (e.response?.statusCode) {
      case 401:
        return Exception('Unauthorized: Please log in again');
      case 403:
        return Exception('Forbidden: Access denied');
      case 404:
        return Exception('Order not found');
      case 500:
        return Exception('Server error: Please try again later');
      default:
        return Exception('Network error: ${e.message}');
    }
  }
}
