import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'api_config.dart';

/// Response model for backend authentication
class BackendAuthResponse {
  final bool success;
  final String? message;
  final Map<String, dynamic>? data;
  final String? backendToken;
  final Map<String, dynamic>? user;

  const BackendAuthResponse({
    required this.success,
    this.message,
    this.data,
    this.backendToken,
    this.user,
  });

  factory BackendAuthResponse.fromJson(Map<String, dynamic> json) {
    // For the updated API response format, if we have a token, consider it successful
    // The API now returns { "token": "...", "user": {...} } for success
    // and error status codes for failures
    final hasToken = json['token'] != null && json['token'].toString().isNotEmpty;

    return BackendAuthResponse(
      success: json['success'] ?? hasToken, // Default to true if token is present
      message: json['message'],
      data: json['data'],
      backendToken: json['token'] ?? json['data']?['token'],
      user: json['user'] ?? json['data']?['user'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
      'token': backendToken,
      'user': user,
    };
  }
}

/// Service for handling backend authentication API calls
class BackendAuthService {
  final Dio _dio;

  BackendAuthService({Dio? dio}) : _dio = dio ?? ApiConfig.createDioClient();

  /// Authenticate with backend using Firebase ID token
  Future<BackendAuthResponse> authenticate(String firebaseIdToken) async {
    try {
      debugPrint('[BackendAuth] Starting backend authentication...');
      debugPrint('[BackendAuth] Endpoint: ${ApiConfig.authUrl}');
      debugPrint('[BackendAuth] Firebase token length: ${firebaseIdToken.length}');

      final response = await _dio.post(
        ApiConfig.authEndpoint,
        options: Options(
          headers: {
            'Authorization': 'Bearer $firebaseIdToken',
            // Note: Backend expects only Authorization header, no Content-Type or request body
          },
        ),
      );

      debugPrint('[BackendAuth] Response status: ${response.statusCode}');
      debugPrint('[BackendAuth] Response data: ${response.data}');

      if (response.statusCode == 200) {
        final authResponse = BackendAuthResponse.fromJson(
          response.data as Map<String, dynamic>,
        );

        debugPrint('[BackendAuth] Authentication successful');
        debugPrint('[BackendAuth] Backend token present: ${authResponse.backendToken != null}');
        debugPrint('[BackendAuth] Backend token length: ${authResponse.backendToken?.length ?? 0}');
        debugPrint('[BackendAuth] User data present: ${authResponse.user != null}');
        debugPrint('[BackendAuth] User ID: ${authResponse.user?['id']}');
        debugPrint('[BackendAuth] User email: ${authResponse.user?['email']}');

        return authResponse;
      } else {
        debugPrint('[BackendAuth] Unexpected status code: ${response.statusCode}');
        throw BackendAuthException(
          'Authentication failed with status ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      debugPrint('[BackendAuth] DioException: ${e.message}');
      debugPrint('[BackendAuth] Error type: ${e.type}');
      debugPrint('[BackendAuth] Response: ${e.response?.data}');
      
      final statusCode = e.response?.statusCode;
      final errorMessage = _extractErrorMessage(e);
      
      throw BackendAuthException(
        errorMessage,
        statusCode: statusCode,
        originalError: e,
      );
    } catch (e) {
      debugPrint('[BackendAuth] Unexpected error: $e');
      throw BackendAuthException(
        'Unexpected error during backend authentication: $e',
        originalError: e,
      );
    }
  }

  /// Verify backend token
  Future<BackendAuthResponse> verifyToken(String backendToken) async {
    try {
      debugPrint('[BackendAuth] Verifying backend token...');
      debugPrint('[BackendAuth] Token length: ${backendToken.length}');

      final response = await _dio.get(
        '/auth/verify',
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
          },
        ),
      );

      debugPrint('[BackendAuth] Token verification response status: ${response.statusCode}');
      debugPrint('[BackendAuth] Token verification response data: ${response.data}');

      if (response.statusCode == 200) {
        final authResponse = BackendAuthResponse.fromJson(
          response.data as Map<String, dynamic>,
        );

        debugPrint('[BackendAuth] Token verification successful');
        debugPrint('[BackendAuth] User data present: ${authResponse.user != null}');
        debugPrint('[BackendAuth] User ID: ${authResponse.user?['id']}');
        debugPrint('[BackendAuth] User email: ${authResponse.user?['email']}');

        return authResponse;
      } else {
        debugPrint('[BackendAuth] Token verification failed with status: ${response.statusCode}');
        throw BackendAuthException(
          'Token verification failed with status ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      debugPrint('[BackendAuth] Token verification DioException: ${e.message}');
      debugPrint('[BackendAuth] Error type: ${e.type}');
      debugPrint('[BackendAuth] Response: ${e.response?.data}');

      final statusCode = e.response?.statusCode;
      String errorMessage;

      if (statusCode == 401) {
        errorMessage = 'Invalid or expired token';
      } else {
        errorMessage = _extractErrorMessage(e);
      }

      throw BackendAuthException(
        errorMessage,
        statusCode: statusCode,
        originalError: e,
      );
    } catch (e) {
      debugPrint('[BackendAuth] Unexpected error during token verification: $e');
      throw BackendAuthException(
        'Unexpected error during token verification: $e',
        originalError: e,
      );
    }
  }

  /// Extract meaningful error message from DioException
  String _extractErrorMessage(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout - please check your internet connection';
      case DioExceptionType.sendTimeout:
        return 'Request timeout - please try again';
      case DioExceptionType.receiveTimeout:
        return 'Server response timeout - please try again';
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final responseData = e.response?.data;
        
        if (statusCode == 401) {
          return 'Authentication failed - invalid token';
        } else if (statusCode == 403) {
          return 'Access forbidden - insufficient permissions';
        } else if (statusCode == 404) {
          return 'Authentication endpoint not found';
        } else if (statusCode == 500) {
          return 'Server error - please try again later';
        }
        
        // Try to extract error message from response
        if (responseData is Map<String, dynamic>) {
          return responseData['message'] ?? 
                 responseData['error'] ?? 
                 'Backend authentication failed';
        }
        
        return 'Backend authentication failed (Status: $statusCode)';
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error - please check your internet connection';
      case DioExceptionType.unknown:
      default:
        return 'Network error - please try again';
    }
  }
}

/// Custom exception for backend authentication errors
class BackendAuthException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic originalError;

  const BackendAuthException(
    this.message, {
    this.statusCode,
    this.originalError,
  });

  @override
  String toString() {
    return 'BackendAuthException: $message${statusCode != null ? ' (Status: $statusCode)' : ''}';
  }
}
