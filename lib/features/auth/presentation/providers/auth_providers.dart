import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../main.dart';
import '../../data/datasources/auth_data_source.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../domain/usecases/apple_sign_in_use_case.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/sign_in_use_case.dart';
import '../../domain/usecases/sign_up_use_case.dart';
import '../../domain/usecases/google_sign_in_use_case.dart';
import '../../domain/usecases/composite_google_sign_in_use_case.dart';
import '../../domain/usecases/composite_apple_sign_in_use_case.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import './auth_state.dart';

import '../../data/models/enhanced_user_model.dart';
import '../../data/models/user_model.dart';
import '../../data/datasources/auth_local_data_source.dart';
import '../../../../core/storage/local_storage.dart';
import 'backend_auth_providers.dart';

part 'auth_providers.g.dart';

// Skip the type parameter completely as per Riverpod 2.0+ convention
// Data sources
@riverpod
AuthDataSource? authDataSource(authDataSourceRef) {
  final isFirebaseInitialized = authDataSourceRef.watch(firebaseInitProvider);

  if (!isFirebaseInitialized) {
    // Firebase isn't initialized, return null or show a message
    debugPrint('Firebase is not initialized, auth features will be disabled');
    return null;
  }

  try {
    return AuthDataSourceImpl();
  } catch (e) {
    debugPrint('Error creating AuthDataSourceImpl: $e');
    return null;
  }
}

@riverpod
LocalStorage localStorage(localStorageRef) {
  return LocalStorageImpl();
}

@riverpod
AuthLocalDataSource authLocalDataSource(authLocalDataSourceRef) {
  final localStorage = authLocalDataSourceRef.watch(localStorageProvider);
  return AuthLocalDataSourceImpl(localStorage: localStorage);
}

// Repositories
@riverpod
AuthRepository? authRepository(authRepositoryRef) {
  final dataSource = authRepositoryRef.watch(authDataSourceProvider);

  if (dataSource == null) {
    return null;
  }

  return AuthRepositoryImpl(dataSource: dataSource);
}

// Use cases
@riverpod
SignInUseCase? signInUseCase(signInUseCaseRef) {
  final repository = signInUseCaseRef.watch(authRepositoryProvider);

  if (repository == null) {
    return null;
  }

  return SignInUseCase(repository);
}

@riverpod
SignUpUseCase? signUpUseCase(signUpUseCaseRef) {
  final repository = signUpUseCaseRef.watch(authRepositoryProvider);

  if (repository == null) {
    return null;
  }

  return SignUpUseCase(repository);
}

@riverpod
GoogleSignInUseCase? googleSignInUseCase(googleSignInUseCaseRef) {
  final repository = googleSignInUseCaseRef.watch(authRepositoryProvider);

  if (repository == null) {
    return null;
  }

  return GoogleSignInUseCase(repository);
}

@riverpod
AppleSignInUseCase? appleSignInUseCase(appleSignInUseCaseRef) {
  final repository = appleSignInUseCaseRef.watch(authRepositoryProvider);

  if (repository == null) {
    return null;
  }

  return AppleSignInUseCase(repository);
}

// Auth notifier with code generation
@riverpod
class AuthNotifier extends _$AuthNotifier {
  bool _isCheckingAuth = false;

  @override
  AuthState build() {
    // Start with loading state instead of initial to avoid race condition
    _checkAuthStatus();
    return const AuthState.loading();
  }

  /// Initialize the auth state based on stored credentials
  /// This method now includes backend authentication validation on app startup
  Future<void> _checkAuthStatus() async {
    // Prevent multiple concurrent auth checks
    if (_isCheckingAuth) {
      debugPrint('[AuthNotifier] Auth check already in progress, skipping...');
      return;
    }

    _isCheckingAuth = true;

    try {
      // Use safe reads to avoid uninitialized provider errors
      final repository = ref.read(authRepositoryProvider);
      final backendAuthRepository = ref.read(backendAuthRepositoryProvider);
      final localDataSource = ref.read(authLocalDataSourceProvider);

      if (repository == null) {
        state = const AuthState.unauthenticated();
        return;
      }

      // Check current state safely - if we can't read it, assume we need to check auth
      bool isCurrentlyAuthenticated = false;
      try {
        isCurrentlyAuthenticated = state.maybeMap(
          authenticated: (_) => true,
          orElse: () => false,
        );
      } catch (e) {
        debugPrint('[AuthNotifier] Could not read current state, proceeding with auth check: $e');
        isCurrentlyAuthenticated = false;
      }

      if (!isCurrentlyAuthenticated) {
        state = const AuthState.loading();
      }

      debugPrint('[AuthNotifier] Checking authentication status on app startup...');

      // First check if we have a cached enhanced user with backend auth
      final cachedEnhancedUser = await localDataSource.getCachedEnhancedUser();
      if (cachedEnhancedUser != null && cachedEnhancedUser.hasBackendAuth) {
        debugPrint('[AuthNotifier] Found cached enhanced user with backend auth');

        // Validate that Firebase user is still signed in
        final isSignedIn = await repository.isSignedIn();
        if (isSignedIn) {
          debugPrint('[AuthNotifier] Firebase user still signed in, using cached enhanced user');
          state = AuthState.authenticated(cachedEnhancedUser);
          debugPrint('[AuthNotifier] Auth check completed successfully with cached user');
          return;
        } else {
          debugPrint('[AuthNotifier] Firebase user no longer signed in, clearing cache');
          await localDataSource.clearUser();
        }
      }

      // Fallback to Firebase authentication check
      final isSignedIn = await repository.isSignedIn();
      if (!isSignedIn) {
        debugPrint('[AuthNotifier] No Firebase user found, setting unauthenticated');
        state = const AuthState.unauthenticated();
        return;
      }

      debugPrint('[AuthNotifier] Firebase user found, getting user details...');
      final userResult = await repository.getCurrentUser();

      await userResult.fold(
        (failure) async {
          debugPrint('[AuthNotifier] Failed to get current user: ${failure.message}');
          state = const AuthState.unauthenticated();
        },
        (user) async {
          if (user == null) {
            debugPrint('[AuthNotifier] User is null, setting unauthenticated');
            state = const AuthState.unauthenticated();
            return;
          }

          debugPrint('[AuthNotifier] Firebase user retrieved: ${user.email}');

          // Attempt backend authentication with Firebase token
          if (user.token != null && user.token!.isNotEmpty) {
            debugPrint('[AuthNotifier] Attempting backend authentication on startup...');

            final backendResult = await backendAuthRepository.authenticateWithBackend(user.token!);

            await backendResult.fold(
              (failure) async {
                debugPrint('[AuthNotifier] Backend authentication failed on startup: ${failure.message}');
                debugPrint('[AuthNotifier] Treating as authentication failure, signing out...');

                // Backend auth failed, sign out the user completely
                await repository.signOut();
                state = const AuthState.unauthenticated();
              },
              (backendAuthResult) async {
                debugPrint('[AuthNotifier] Backend authentication successful on startup');

                // Create enhanced user model with backend data
                final enhancedUser = EnhancedUserModel.fromUserAndBackendAuth(
                  user: user,
                  backendAuthResult: backendAuthResult,
                );

                // Cache the enhanced user for future app starts
                await _cacheEnhancedUser(enhancedUser);

                debugPrint('[AuthNotifier] Setting authenticated state with backend data');
                state = AuthState.authenticated(enhancedUser);
              },
            );
          } else {
            debugPrint('[AuthNotifier] No Firebase token available, treating as authentication failure');
            await repository.signOut();
            state = const AuthState.unauthenticated();
          }
        },
      );
    } catch (e) {
      debugPrint('[AuthNotifier] Unexpected error during auth status check: $e');
      state = const AuthState.unauthenticated();
    } finally {
      _isCheckingAuth = false;
    }
  }

  /// Sign out the current user
  Future<void> signOut() async {
    final repository = ref.read(authRepositoryProvider);
    final localDataSource = ref.read(authLocalDataSourceProvider);

    if (repository == null) {
      state = const AuthState.unauthenticated();
      return;
    }

    state = const AuthState.loading();

    // Clear cached user data
    try {
      await localDataSource.clearUser();
      debugPrint('[AuthNotifier] Cached user data cleared');
    } catch (e) {
      debugPrint('[AuthNotifier] Failed to clear cached user data: $e');
    }

    final result = await repository.signOut();

    state = result.fold(
      (failure) => AuthState.error(failure),
      (_) => const AuthState.unauthenticated(),
    );
  }

  /// Sign in with Google using composite authentication
  Future<void> signInWithGoogle() async {
    // Try to use composite use case first (Firebase + Backend)
    final compositeUseCase = ref.read(compositeGoogleSignInUseCaseProvider);
    await _signInWithCompositeUseCase(compositeUseCase!);

  }

  /// Sign in with Google (Firebase only) - for modal authentication
  Future<void> signInWithGoogleFirebaseOnly() async {
    final useCase = ref.read(googleSignInUseCaseProvider);

    if (useCase == null) {
      debugPrint('Google Sign-In use case is null');
      state = AuthState.error(
        AuthFailure(message: 'Google Sign-In services are not available'),
      );
      return;
    }

    try {
      debugPrint('Starting Firebase-only Google Sign-In');

      final result = await useCase();

      state = result.fold(
        (failure) {
          debugPrint('Google sign-in failed: ${failure.message}');
          return AuthState.error(failure);
        },
        (user) {
          debugPrint('Google sign-in succeeded for user: ${user.email}');
          return AuthState.authenticated(user);
        },
      );
    } catch (e, stackTrace) {
      debugPrint('Exception in Firebase-only Google sign-in: $e');
      debugPrint('Stack trace: $stackTrace');
      state = AuthState.error(
        AuthFailure(message: 'Failed to sign in with Google: ${e.toString()}'),
      );
    }
  }

  /// Sign in using composite use case (Firebase + Backend)
  Future<void> _signInWithCompositeUseCase(CompositeGoogleSignInUseCase useCase) async {
    try {
      debugPrint('Starting composite Google Sign-In (Firebase + Backend)');
      // state = const AuthState.loading();

      final result = await useCase();

      await result.fold(
        (failure) async {
          debugPrint('Composite sign-in failed: ${failure.message}');

          // Clear any cached user data when composite authentication fails
          try {
            final localDataSource = ref.read(authLocalDataSourceProvider);
            await localDataSource.clearUser();
            debugPrint('[AuthNotifier] Cached user data cleared after composite auth failure');
          } catch (e) {
            debugPrint('[AuthNotifier] Failed to clear cached user data: $e');
          }

          state = AuthState.error(failure);
        },
        (compositeResult) async {
          debugPrint('Composite sign-in succeeded for user: ${compositeResult.user.email}');
          debugPrint('Firebase token present: ${compositeResult.user.token != null}');
          debugPrint('Backend auth successful: ${compositeResult.hasBackendAuth}');

          if (compositeResult.hasBackendAuth) {
            debugPrint('Backend token present: ${compositeResult.backendToken.isNotEmpty}');
            debugPrint('Backend user data: ${compositeResult.backendUserData}');
          }

          // Create enhanced user model with backend data
          final enhancedUser = EnhancedUserModel.fromUserAndBackendAuth(
            user: compositeResult.user,
            backendAuthResult: compositeResult.backendAuthResult,
          );

          // Cache the enhanced user for future app starts
          await _cacheEnhancedUser(enhancedUser);

          state = AuthState.authenticated(enhancedUser);
        },
      );
    } catch (e, stackTrace) {
      debugPrint('Exception in composite sign-in: $e');
      debugPrint('Stack trace: $stackTrace');

      // Clear any cached user data when exception occurs
      try {
        final localDataSource = ref.read(authLocalDataSourceProvider);
        await localDataSource.clearUser();
        debugPrint('[AuthNotifier] Cached user data cleared after exception');
      } catch (clearError) {
        debugPrint('[AuthNotifier] Failed to clear cached user data: $clearError');
      }

      state = AuthState.error(
        AuthFailure(message: 'Failed to sign in with Google: ${e.toString()}'),
      );
    }
  }

  /// Sign in with Apple using composite authentication
  Future<void> signInWithApple() async {
    // Try to use composite use case first (Firebase + Backend)
    final compositeUseCase = ref.read(compositeAppleSignInUseCaseProvider);
    if (compositeUseCase != null) {
      await _signInWithCompositeAppleUseCase(compositeUseCase);
    } else {
      // Fallback to Firebase-only authentication
      await signInWithAppleFirebaseOnly();
    }
  }

  /// Sign in with Apple (Firebase only) - for modal authentication
  Future<void> signInWithAppleFirebaseOnly() async {
    final useCase = ref.read(appleSignInUseCaseProvider);

    if (useCase == null) {
      debugPrint('Apple Sign-In use case is null');
      state = AuthState.error(
        AuthFailure(message: 'Apple Sign-In services are not available'),
      );
      return;
    }

    try {
      debugPrint('Starting Firebase-only Apple Sign-In');

      final result = await useCase();

      state = result.fold(
        (failure) {
          debugPrint('Apple sign-in failed: ${failure.message}');
          return AuthState.error(failure);
        },
        (user) {
          debugPrint('Apple sign-in succeeded for user: ${user.email}');
          return AuthState.authenticated(user);
        },
      );
    } catch (e, stackTrace) {
      debugPrint('Exception in Firebase-only Apple sign-in: $e');
      debugPrint('Stack trace: $stackTrace');
      state = AuthState.error(
        AuthFailure(message: 'Failed to sign in with Apple: ${e.toString()}'),
      );
    }
  }

  /// Sign in using composite Apple use case (Firebase + Backend)
  Future<void> _signInWithCompositeAppleUseCase(CompositeAppleSignInUseCase useCase) async {
    try {
      debugPrint('Starting composite Apple Sign-In (Firebase + Backend)');
      // state = const AuthState.loading();

      final result = await useCase();

      await result.fold(
        (failure) async {
          debugPrint('Composite Apple sign-in failed: ${failure.message}');

          // Clear any cached user data when composite authentication fails
          try {
            final localDataSource = ref.read(authLocalDataSourceProvider);
            await localDataSource.clearUser();
            debugPrint('[AuthNotifier] Cached user data cleared after composite Apple auth failure');
          } catch (e) {
            debugPrint('[AuthNotifier] Failed to clear cached user data: $e');
          }

          state = AuthState.error(failure);
        },
        (compositeResult) async {
          debugPrint('Composite Apple sign-in succeeded for user: ${compositeResult.user.email}');
          debugPrint('Firebase token present: ${compositeResult.user.token != null}');
          debugPrint('Backend auth successful: ${compositeResult.hasBackendAuth}');

          if (compositeResult.hasBackendAuth) {
            debugPrint('Backend token present: ${compositeResult.backendToken.isNotEmpty}');
            debugPrint('Backend user data: ${compositeResult.backendUserData}');
          }

          // Create enhanced user model with backend data
          final enhancedUser = EnhancedUserModel.fromUserAndBackendAuth(
            user: compositeResult.user,
            backendAuthResult: compositeResult.backendAuthResult,
          );

          // Cache the enhanced user for future app starts
          await _cacheEnhancedUser(enhancedUser);

          state = AuthState.authenticated(enhancedUser);
        },
      );
    } catch (e, stackTrace) {
      debugPrint('Exception in composite Apple sign-in: $e');
      debugPrint('Stack trace: $stackTrace');

      // Clear any cached user data when exception occurs
      try {
        final localDataSource = ref.read(authLocalDataSourceProvider);
        await localDataSource.clearUser();
        debugPrint('[AuthNotifier] Cached user data cleared after Apple auth exception');
      } catch (clearError) {
        debugPrint('[AuthNotifier] Failed to clear cached user data: $clearError');
      }

      state = AuthState.error(
        AuthFailure(message: 'Failed to sign in with Apple: ${e.toString()}'),
      );
    }
  }

  /// Verify backend token and handle reauthentication if needed
  Future<bool> verifyBackendToken() async {
    final currentState = state;

    // Check if user is authenticated and has backend token
    final user = currentState.maybeMap(
      authenticated: (authState) => authState.user,
      orElse: () => null,
    );

    // Check if user is an EnhancedUserModel with backend auth
    if (user == null || user is! EnhancedUserModel) {
      debugPrint('[AuthNotifier] Cannot verify token: user not authenticated or not enhanced user');
      return false;
    }

    if (!user.hasBackendAuth || user.backendToken == null) {
      debugPrint('[AuthNotifier] Cannot verify token: no backend auth or token');
      return false;
    }

    try {
      debugPrint('[AuthNotifier] Verifying backend token...');
      final backendAuthRepository = ref.read(backendAuthRepositoryProvider);

      final result = await backendAuthRepository.validateBackendToken(user.backendToken!);

      return await result.fold(
        (failure) async {
          debugPrint('[AuthNotifier] Token verification failed: ${failure.message}');

          // Check for various token expiration/invalid scenarios
          final errorMessage = failure.message.toLowerCase();
          final isTokenExpired = errorMessage.contains('invalid or expired token') ||
                                errorMessage.contains('expired') ||
                                errorMessage.contains('unauthorized') ||
                                errorMessage.contains('401') ||
                                errorMessage.contains('invalid token') ||
                                errorMessage.contains('token not found') ||
                                errorMessage.contains('authentication failed');

          // Check for server errors that should trigger sign out (500 errors, database connection issues)
          final isServerError = errorMessage.contains('server error') ||
                               errorMessage.contains('500') ||
                               errorMessage.contains('no primary_preferred server') ||
                               errorMessage.contains('cluster topology=unknown') ||
                               errorMessage.contains('mongodb') ||
                               errorMessage.contains('database connection');

          if (isTokenExpired) {
            debugPrint('[AuthNotifier] Token expired/invalid detected, attempting fresh reauthentication...');
            return await _handleTokenExpiredReauthentication();
          }

          if (isServerError) {
            debugPrint('[AuthNotifier] Server error detected, signing out user and redirecting to home...');
            await _handleServerErrorSignOut();
            return false;
          }

          // For other types of failures, don't attempt reauthentication
          debugPrint('[AuthNotifier] Non-token error, not attempting reauthentication: ${failure.message}');
          return false;
        },
        (isValid) async {
          debugPrint('[AuthNotifier] Token verification result: $isValid');

          if (!isValid) {
            debugPrint('[AuthNotifier] Token is invalid, attempting fresh reauthentication...');
            return await _handleTokenExpiredReauthentication();
          }

          return true;
        },
      );
    } catch (e) {
      debugPrint('[AuthNotifier] Exception during token verification: $e');
      return false;
    }
  }

  /// Handle token expiration by clearing cache and attempting fresh reauthentication
  Future<bool> _handleTokenExpiredReauthentication() async {
    try {
      debugPrint('[AuthNotifier] Backend token expired - starting fresh reauthentication...');

      // Step 1: Clear all cached tokens and user data
      await _clearAllCachedTokens();

      // Step 2: Get fresh Firebase token from current Firebase user
      final freshFirebaseToken = await _getFreshFirebaseToken();

      if (freshFirebaseToken == null) {
        debugPrint('[AuthNotifier] Cannot get fresh Firebase token - forcing sign out');
        await signOut();
        return false;
      }

      // Step 3: Try to authenticate with fresh Firebase token
      final success = await _authenticateWithFreshFirebaseToken(freshFirebaseToken);

      if (success) {
        debugPrint('[AuthNotifier] Fresh reauthentication successful');
        return true;
      } else {
        debugPrint('[AuthNotifier] Fresh reauthentication failed - forcing sign out');
        await signOut();
        return false;
      }
    } catch (e) {
      debugPrint('[AuthNotifier] Exception during fresh reauthentication: $e');
      await signOut();
      return false;
    }
  }

  /// Clear all cached tokens and user data
  Future<void> _clearAllCachedTokens() async {
    try {
      debugPrint('[AuthNotifier] Clearing all cached tokens and user data...');

      final localDataSource = ref.read(authLocalDataSourceProvider);
      await localDataSource.clearUser();

      debugPrint('[AuthNotifier] All cached tokens cleared successfully');
    } catch (e) {
      debugPrint('[AuthNotifier] Failed to clear cached tokens: $e');
    }
  }

  /// Handle server errors by signing out user and redirecting to home
  Future<void> _handleServerErrorSignOut() async {
    try {
      debugPrint('[AuthNotifier] Handling server error - clearing all tokens and signing out...');

      // Clear all cached tokens and user data
      await _clearAllCachedTokens();

      // Sign out the user completely
      await signOut();

      debugPrint('[AuthNotifier] Server error sign out completed');
    } catch (e) {
      debugPrint('[AuthNotifier] Error during server error sign out: $e');
      // Force state to unauthenticated even if sign out fails
      state = const AuthState.unauthenticated();
    }
  }

  /// Get fresh Firebase ID token from current Firebase user
  Future<String?> _getFreshFirebaseToken() async {
    try {
      debugPrint('[AuthNotifier] Getting fresh Firebase token...');

      final authDataSource = ref.read(authDataSourceProvider);

      if (authDataSource == null) {
        debugPrint('[AuthNotifier] Auth data source not available');
        return null;
      }

      // Get current Firebase user directly from Firebase Auth
      final firebaseUser = authDataSource.getCurrentFirebaseUser();

      if (firebaseUser == null) {
        debugPrint('[AuthNotifier] No current Firebase user found');
        return null;
      }

      // Force refresh the Firebase ID token
      final freshToken = await firebaseUser.getIdToken(true); // true forces refresh

      if (freshToken != null && freshToken.isNotEmpty) {
        debugPrint('[AuthNotifier] Fresh Firebase token obtained successfully');
        debugPrint('[AuthNotifier] Fresh token length: ${freshToken.length}');
        return freshToken;
      } else {
        debugPrint('[AuthNotifier] Failed to get fresh Firebase token');
        return null;
      }
    } catch (e) {
      debugPrint('[AuthNotifier] Exception getting fresh Firebase token: $e');
      return null;
    }
  }

  /// Authenticate with backend using fresh Firebase token
  Future<bool> _authenticateWithFreshFirebaseToken(String freshFirebaseToken) async {
    try {
      debugPrint('[AuthNotifier] Authenticating with fresh Firebase token...');

      final backendAuthRepository = ref.read(backendAuthRepositoryProvider);
      final backendResult = await backendAuthRepository.authenticateWithBackend(freshFirebaseToken);

      return await backendResult.fold(
        (failure) async {
          debugPrint('[AuthNotifier] Fresh backend authentication failed: ${failure.message}');
          return false;
        },
        (backendAuthResult) async {
          debugPrint('[AuthNotifier] Fresh backend authentication successful');

          // Get Firebase user directly for creating UserModel
          final authDataSource = ref.read(authDataSourceProvider);
          final firebaseUser = authDataSource?.getCurrentFirebaseUser();

          if (firebaseUser == null) {
            debugPrint('[AuthNotifier] No Firebase user available for enhanced model');
            return false;
          }

          // Create user model with fresh token
          final userModel = UserModel(
            id: firebaseUser.uid,
            email: firebaseUser.email ?? '',
            name: firebaseUser.displayName ?? 'User',
            profilePhoto: firebaseUser.photoURL,
            isEmailVerified: firebaseUser.emailVerified,
            token: freshFirebaseToken,
          );

          // Create enhanced user model with backend data
          final enhancedUser = EnhancedUserModel.fromUserAndBackendAuth(
            user: userModel,
            backendAuthResult: backendAuthResult,
          );

          // Cache the enhanced user for future app starts
          await _cacheEnhancedUser(enhancedUser);

          // Update state with enhanced user
          state = AuthState.authenticated(enhancedUser);

          return true;
        },
      );
    } catch (e) {
      debugPrint('[AuthNotifier] Exception during fresh backend authentication: $e');
      return false;
    }
  }

  /// Authenticate with backend using current Firebase token
  Future<bool> authenticateWithBackend() async {
    final currentState = state;

    // Check if user is authenticated with Firebase and get user
    final user = currentState.user;
    if (user == null) {
      debugPrint('[AuthNotifier] Cannot authenticate with backend: user not authenticated with Firebase');
      return false;
    }

    final firebaseToken = user.token;

    if (firebaseToken == null || firebaseToken.isEmpty) {
      debugPrint('[AuthNotifier] Cannot authenticate with backend: no Firebase token available');
      return false;
    }

    try {
      debugPrint('[AuthNotifier] Starting backend authentication...');

      final backendAuthRepository = ref.read(backendAuthRepositoryProvider);
      final backendResult = await backendAuthRepository.authenticateWithBackend(firebaseToken);

      return await backendResult.fold(
        (failure) async {
          debugPrint('[AuthNotifier] Backend authentication failed: ${failure.message}');
          return false;
        },
        (backendAuthResult) async {
          debugPrint('[AuthNotifier] Backend authentication successful');

          // Create enhanced user model with backend data
          final enhancedUser = EnhancedUserModel.fromUserAndBackendAuth(
            user: user,
            backendAuthResult: backendAuthResult,
          );

          // Cache the enhanced user for future app starts
          await _cacheEnhancedUser(enhancedUser);

          // Update state with enhanced user
          state = AuthState.authenticated(enhancedUser);

          return true;
        },
      );
    } catch (e) {
      debugPrint('[AuthNotifier] Exception during backend authentication: $e');
      return false;
    }
  }

  /// Cache enhanced user data for persistence across app restarts
  Future<void> _cacheEnhancedUser(EnhancedUserModel user) async {
    try {
      final localDataSource = ref.read(authLocalDataSourceProvider);
      await localDataSource.cacheEnhancedUser(user);
      debugPrint('[AuthNotifier] Enhanced user cached successfully');
    } catch (e) {
      debugPrint('[AuthNotifier] Failed to cache enhanced user: $e');
      // Don't throw error, just log it as caching is not critical
    }
  }
}
