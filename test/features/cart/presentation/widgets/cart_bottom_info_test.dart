import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'package:flutter_travelgator/features/cart/presentation/widgets/cart_bottom_info.dart';
import 'package:flutter_travelgator/l10n/app_localizations.dart';

void main() {
  group('CartBottomInfo Widget', () {
    Widget createTestWidget({
      bool isAllSelected = false,
      double totalPrice = 0.0,
      bool hasSelectedItems = false,
    }) {
      return MaterialApp(
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en'),
          Locale('vi'),
        ],
        home: Scaffold(
          body: CartBottomInfo(
            isAllSelected: isAllSelected,
            onSelectAllChanged: (value) {},
            totalPrice: totalPrice,
            onCheckout: () {},
            hasSelectedItems: hasSelectedItems,
          ),
        ),
      );
    }

    testWidgets('should display cart bottom info without count', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        isAllSelected: true,
        totalPrice: 59.98,
        hasSelectedItems: true,
      ));

      // Verify the total price is displayed
      expect(find.text('\$59.98'), findsOneWidget);
      
      // Verify the checkout button is present
      expect(find.text('Check out'), findsOneWidget);
      
      // Verify that count text like "2 of 2" is NOT displayed
      expect(find.textContaining('of'), findsNothing);
      expect(find.textContaining('2 of 2'), findsNothing);
      expect(find.textContaining('1 of 1'), findsNothing);
    });

    testWidgets('should display select all checkbox', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        isAllSelected: false,
        totalPrice: 29.99,
        hasSelectedItems: false,
      ));

      // Verify the select all checkbox is present
      expect(find.byType(Checkbox), findsOneWidget);
      
      // Verify the total price is displayed
      expect(find.text('\$29.99'), findsOneWidget);
      
      // Verify no count text is displayed
      expect(find.textContaining('of'), findsNothing);
    });

    testWidgets('should handle zero total price', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        isAllSelected: false,
        totalPrice: 0.0,
        hasSelectedItems: false,
      ));

      // Verify zero price is displayed correctly
      expect(find.text('\$0.00'), findsOneWidget);
      
      // Verify no count text is displayed
      expect(find.textContaining('of'), findsNothing);
    });

    testWidgets('should not display any count-related text', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        isAllSelected: true,
        totalPrice: 100.50,
        hasSelectedItems: true,
      ));

      // Verify that no count-related text patterns are found
      expect(find.textContaining('of'), findsNothing);
      expect(find.textContaining('1 of'), findsNothing);
      expect(find.textContaining('2 of'), findsNothing);
      expect(find.textContaining('3 of'), findsNothing);
      expect(find.textContaining('selected'), findsNothing);
      expect(find.textContaining('items'), findsNothing);
    });
  });
}
