import 'package:flutter/material.dart';
import '../../domain/entities/order.dart' as entities;

/// Badge widget for displaying transaction type (Purchase/Redeem/Earn)
class TransactionTypeBadge extends StatelessWidget {
  final entities.TransactionType type;

  const TransactionTypeBadge({
    super.key,
    required this.type,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _getIcon(),
          const SizedBox(width: 4),
          Text(
            type.displayName,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: _getTextColor(),
            ),
          ),
        ],
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (type) {
      case entities.TransactionType.purchase:
        return const Color(0xFFDBEAFE); // Blue background
      case entities.TransactionType.redeem:
        return const Color(0xFFFFEDD5); // Orange background
      case entities.TransactionType.earn:
        return const Color(0xFFECFCCB); // Green background
    }
  }

  Color _getTextColor() {
    switch (type) {
      case entities.TransactionType.purchase:
        return const Color(0xFF2563EB); // Blue text
      case entities.TransactionType.redeem:
        return const Color(0xFFEA580C); // Orange text
      case entities.TransactionType.earn:
        return const Color(0xFF65A30D); // Green text
    }
  }

  Widget _getIcon() {
    switch (type) {
      case entities.TransactionType.purchase:
        return Icon(
          Icons.swap_horiz,
          size: 16,
          color: _getTextColor(),
        );
      case entities.TransactionType.redeem:
        return Icon(
          Icons.star_outline,
          size: 16,
          color: _getTextColor(),
        );
      case entities.TransactionType.earn:
        return Icon(
          Icons.account_balance_wallet_outlined,
          size: 16,
          color: _getTextColor(),
        );
    }
  }
}
