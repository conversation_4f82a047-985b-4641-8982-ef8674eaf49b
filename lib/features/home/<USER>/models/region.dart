class Region {
  final String name;
  final String? description;
  final String code;
  final String flagPath;
  final String price;

  Region({
    required this.name,
    this.description,
    required this.code,
    String? price,
  }) : flagPath = 'assets/images/flags/${code.toLowerCase()}.svg',
       price = price ?? _getDefaultPrice(code);

  /// Get default price for region based on code
  static String _getDefaultPrice(String regionCode) {
    // Return $0 as static fallback, consistent with countries
    // Dynamic prices will be fetched via minPriceByRegionProvider
    return '\$0';
  }

  /// Create Region from Shopify metaobject data
  factory Region.fromShopifyMetaobject(Map<String, dynamic> metaobject) {
    final handle = metaobject['handle'] as String;
    final fields = <String, String>{};

    // Parse fields array into map
    final fieldsArray = metaobject['fields'] as List<dynamic>;
    for (final field in fieldsArray) {
      final key = field['key'] as String;
      final value = field['value'] as String;
      fields[key] = value;
    }

    // Validate required fields
    final name = fields['name'];
    final description = fields['description'];
    final code = fields['code'];

    // Name is required
    if (name == null || name.isEmpty) {
      throw ArgumentError('Region metaobject must have a non-empty "name" field. Handle: $handle, Available fields: ${fields.keys.toList()}');
    }

    // Description is optional - keep null if not provided
    // debugPrint('🌏 [Region] Created: $name (${code ?? handle}) - Description: ${description != null ? '"$description"' : 'null'}');

    final region = Region(
      name: name,
      description: description, // Keep null if missing from metaobject
      code: code ?? handle,
      // Don't pass price - let it use default static prices
    );

    return region;
  }
}
