import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/models/product_model.dart';
import '../../../../l10n/app_localizations.dart';

class VariantSelector extends StatelessWidget {
  final List<Variant> variants;
  final String? selectedVariantId;
  final Function(Variant) onVariantSelected;

  const VariantSelector({
    super.key,
    required this.variants,
    this.selectedVariantId,
    required this.onVariantSelected,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    // If only one variant, don't show dropdown
    if (variants.length <= 1) {
      return const SizedBox.shrink();
    }

    // Find selected variant or use first available
    final selectedVariant = variants.firstWhere(
      (variant) => variant.id == selectedVariantId,
      orElse: () => variants.first,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.variant,
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFD1D5DB)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<Variant>(
              value: selectedVariant,
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              icon: const Icon(
                Icons.keyboard_arrow_down,
                color: Color(0xFF6B7280),
              ),
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF1F2937),
              ),
              items: variants.map((variant) {
                return DropdownMenuItem<Variant>(
                  value: variant,
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          variant.displayName,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '\$${variant.price.amount}',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFFFF6982),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (Variant? newVariant) {
                if (newVariant != null) {
                  onVariantSelected(newVariant);
                }
              },
            ),
          ),
        ),
      ],
    );
  }
}
