import 'package:flutter/material.dart';
import '../services/legal_documents_service.dart';

/// Test screen for legal documents functionality
/// This can be used for testing and demonstration purposes
class LegalTestScreen extends StatelessWidget {
  const LegalTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Legal Documents Test'),
        backgroundColor: const Color(0xFFFF6982),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test Legal Documents Modal',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),
            
            ElevatedButton(
              onPressed: () {
                LegalDocumentsService.showLegalDocumentsModal(context);
              },
              child: const Text('Show Legal Documents Modal'),
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () {
                LegalDocumentsService.showUserAgreement(context);
              },
              child: const Text('Show User Agreement'),
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () {
                LegalDocumentsService.showPrivacyPolicy(context);
              },
              child: const Text('Show Privacy Policy'),
            ),
            
            const SizedBox(height: 32),
            
            const Text(
              'Safe Methods (with error handling):',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () {
                LegalDocumentsService.showUserAgreementSafe(
                  context,
                  onError: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Error showing User Agreement'),
                      ),
                    );
                  },
                );
              },
              child: const Text('Show User Agreement (Safe)'),
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () {
                LegalDocumentsService.showPrivacyPolicySafe(
                  context,
                  onError: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Error showing Privacy Policy'),
                      ),
                    );
                  },
                );
              },
              child: const Text('Show Privacy Policy (Safe)'),
            ),
            
            const Spacer(),
            
            const Text(
              'Note: These buttons demonstrate the legal documents modal functionality. '
              'In the actual app, these modals are accessible from the account settings '
              'and auth modal legal links.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
