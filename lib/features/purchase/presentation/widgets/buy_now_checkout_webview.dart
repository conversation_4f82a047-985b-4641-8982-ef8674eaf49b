import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../../cart/presentation/screens/checkout_success_screen.dart';

/// Buy Now checkout WebView with comprehensive completion detection
/// Uses same robust completion logic as cart checkout
class BuyNowCheckoutWebView extends StatefulWidget {
  final String checkoutUrl;
  final VoidCallback? onCheckoutComplete;
  final Function(String)? onCheckoutError;
  final VoidCallback? onCloseModal;
  final bool showSuccessScreen;

  const BuyNowCheckoutWebView({
    super.key,
    required this.checkoutUrl,
    this.onCheckoutComplete,
    this.onCheckoutError,
    this.onCloseModal,
    this.showSuccessScreen = false,
  });

  @override
  State<BuyNowCheckoutWebView> createState() => _BuyNowCheckoutWebViewState();
}

class _BuyNowCheckoutWebViewState extends State<BuyNowCheckoutWebView> {
  late WebViewController _controller;
  bool _isLoading = true;
  bool _hasCompletedCheckout = false; // Flag to prevent duplicate completion handling
  DateTime? _checkoutStartTime;

  @override
  void initState() {
    super.initState();
    debugPrint('[BuyNowCheckoutWebView] 🚀 INITIALIZING BUY NOW CHECKOUT WEBVIEW');
    debugPrint('[BuyNowCheckoutWebView] Checkout URL: ${widget.checkoutUrl}');
    _checkoutStartTime = DateTime.now();
    _initializeWebView();
  }

  @override
  void dispose() {
    debugPrint('[BuyNowCheckoutWebView] 🧹 DISPOSING BUY NOW CHECKOUT WEBVIEW');
    super.dispose();
  }

  void _initializeWebView() {
    try {
      _controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(const Color(0x00000000))
        ..setNavigationDelegate(
          NavigationDelegate(
            onProgress: (int progress) {
              // Update loading progress if needed
            },
            onPageStarted: (String url) {
              if (mounted) {
                setState(() {
                  _isLoading = true;
                });
              }
              debugPrint('[BuyNowCheckoutWebView] Page started loading: $url');

              // Mark that checkout has started if we're on a checkout page
              if (url.contains('/checkouts/') || url.contains('/cart/c/')) {
                debugPrint('[BuyNowCheckoutWebView] Checkout process started');
              }
            },
            onPageFinished: (String url) {
              if (mounted) {
                setState(() {
                  _isLoading = false;
                });
              }
              debugPrint('[SimpleCheckoutWebView] Page finished loading: $url');

              // Check for checkout completion using comprehensive detection
              if (_isCheckoutComplete(url)) {
                debugPrint('[BuyNowCheckoutWebView] ⚠️ COMPLETION DETECTED ON PAGE FINISH: $url');
                _handleCheckoutSuccess(url);
              } else {
                debugPrint('[BuyNowCheckoutWebView] Page finish - not a completion page, continuing...');
              }
            },
            onNavigationRequest: (NavigationRequest request) {
              debugPrint('[BuyNowCheckoutWebView] Navigation to: ${request.url}');

              // Option B: Detect custom app deep links (preferred)
              if (request.url.startsWith('myapp://checkout/success')) {
                debugPrint('[BuyNowCheckoutWebView] Deep link detected: ${request.url}');
                _handleCheckoutSuccess(request.url);
                return NavigationDecision.prevent;
              }

              // Check for checkout completion using comprehensive detection
              debugPrint('[BuyNowCheckoutWebView] Checking if navigation indicates completion...');
              if (_isCheckoutComplete(request.url)) {
                debugPrint('[BuyNowCheckoutWebView] ⚠️ COMPLETION DETECTED VIA NAVIGATION: ${request.url}');
                // Add a small delay to ensure the page loads before handling completion
                Future.delayed(const Duration(milliseconds: 500), () {
                  if (mounted) {
                    debugPrint('[BuyNowCheckoutWebView] Executing delayed completion handler');
                    _handleCheckoutSuccess(request.url);
                  }
                });
                return NavigationDecision.navigate; // Let it load first
              } else {
                debugPrint('[BuyNowCheckoutWebView] Navigation - not a completion page, continuing...');
              }

              // Allow external links to open in system browser
              if (_isExternalLink(request.url)) {
                debugPrint('[BuyNowCheckoutWebView] External link detected, opening in system browser');
                // You could use url_launcher here to open in system browser
                return NavigationDecision.prevent;
              }

              return NavigationDecision.navigate;
            },
            onWebResourceError: (WebResourceError error) {
              debugPrint('[BuyNowCheckoutWebView] WebResource Error: ${error.description}');
              debugPrint('[BuyNowCheckoutWebView] Error Type: ${error.errorType}');
              debugPrint('[BuyNowCheckoutWebView] Error Code: ${error.errorCode}');
              _handleCheckoutError('Network error: ${error.description}');
            },
            onHttpError: (HttpResponseError error) {
              debugPrint('[BuyNowCheckoutWebView] HTTP Error: ${error.response?.statusCode}');
              _handleCheckoutError('HTTP error: ${error.response?.statusCode ?? 'Unknown'}');
            },
            onUrlChange: (UrlChange change) {
              debugPrint('[SimpleCheckoutWebView] URL changed to: ${change.url}');
            },
          ),
        );

      // Load the checkout URL with error handling
      _controller.loadRequest(Uri.parse(widget.checkoutUrl)).catchError((error) {
        debugPrint('[BuyNowCheckoutWebView] Error loading checkout URL: $error');
        _handleCheckoutError('Failed to load checkout page: $error');
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      });
    } catch (e) {
      debugPrint('[BuyNowCheckoutWebView] Error initializing WebView: $e');
      _handleCheckoutError('Failed to initialize checkout: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Comprehensive checkout completion detection (same as cart checkout)
  bool _isCheckoutComplete(String url) {
    // Extract the base domain from the original checkout URL
    final checkoutUri = Uri.tryParse(widget.checkoutUrl);
    final baseDomain = checkoutUri?.host ?? '';

    debugPrint('[BuyNowCheckoutWebView] Checking completion for URL: $url');
    debugPrint('[BuyNowCheckoutWebView] Base domain: $baseDomain');

    // Prevent premature completion detection - require minimum time
    if (_checkoutStartTime != null) {
      final elapsed = DateTime.now().difference(_checkoutStartTime!);
      if (elapsed.inSeconds < 5) { // Require at least 5 seconds
        debugPrint('[BuyNowCheckoutWebView] Too early for completion (${elapsed.inSeconds}s), waiting...');
        return false;
      }
    }

    // IMPORTANT: Don't trigger completion for initial checkout URLs
    if (url.contains('/cart/c/') || url.contains('/checkouts/cn/')) {
      // Only trigger if it's clearly a completion URL, not just the checkout page
      if (!url.contains('/complete') && !url.contains('/success') && !url.contains('/thank_you') && !url.contains('/processing')) {
        debugPrint('[BuyNowCheckoutWebView] Still on checkout page, not completed yet');
        return false;
      }
    }

    // Detect various Shopify checkout completion patterns (2025 format)

    // Pattern 1: Redirect to home page after checkout completion - BE VERY SPECIFIC
    if ((url == 'https://$baseDomain/' || url == 'https://$baseDomain') &&
        !widget.checkoutUrl.contains(url)) { // Make sure it's not the original URL
      debugPrint('[BuyNowCheckoutWebView] Detected redirect to home page - checkout likely completed');
      return true;
    }

    // Pattern 2: Traditional thank you pages - BE MORE SPECIFIC
    if (url.contains('/checkout/thank_you') ||
        url.contains('/thank_you') ||
        url.contains('order_status_url')) {
      debugPrint('[BuyNowCheckoutWebView] Traditional thank you page detected');
      return true;
    }

    // Pattern 2b: Order pages - but only if clearly completion related
    if (url.contains('/orders/') && (url.contains('/confirmation') || url.contains('/receipt') || url.contains('/status'))) {
      debugPrint('[BuyNowCheckoutWebView] Order confirmation page detected');
      return true;
    }

    // Pattern 3: Order status pages
    if (url.contains('/orders/') && url.contains('status')) {
      return true;
    }

    // Pattern 4: Checkout completion indicators
    if (url.contains('checkout_complete=true') ||
        url.contains('order_id=') ||
        url.contains('order_number=')) {
      return true;
    }

    // Pattern 5: Modern Shopify checkout completion (redirect patterns)
    if (url.contains('/checkouts/') && url.contains('/complete')) {
      return true;
    }

    // Pattern 6: Shopify invoice completion patterns
    if (url.contains('/invoices/') && (url.contains('/paid') || url.contains('/complete') || url.contains('/success'))) {
      return true;
    }

    // Pattern 7: Payment completion redirects
    if (url.contains('payment_status=paid') || url.contains('status=completed') || url.contains('payment=success')) {
      return true;
    }

    // Pattern 8: Shopify draft order completion
    if (url.contains('draft_order') && (url.contains('complete') || url.contains('paid'))) {
      return true;
    }

    // Pattern 9: Modern Shopify cart checkout completion patterns (2025)
    // Pattern: /checkouts/cn/[token] followed by completion - BE MORE SPECIFIC
    if (url.contains('/checkouts/cn/') &&
        (url.contains('/complete') || url.contains('/success') || url.contains('/thank_you') || url.contains('/processing'))) {
      debugPrint('[BuyNowCheckoutWebView] Modern checkout completion pattern detected');
      return true;
    }

    // Pattern 10: Shopify checkout completion via redirect to home - BE MORE CAREFUL
    final uri = Uri.tryParse(url);
    if (uri != null && uri.host.contains('myshopify.com')) {
      // Only trigger on actual home page redirect (not just any path)
      if ((uri.path == '/' || uri.path.isEmpty) && !url.contains('/checkouts/') && !url.contains('/cart/')) {
        debugPrint('[BuyNowCheckoutWebView] Home page redirect detected - likely checkout completion');
        return true;
      }

      // Check for order confirmation pages - be more specific
      if (uri.path.contains('/orders/') && (uri.path.contains('/status') || uri.path.contains('/confirmation'))) {
        debugPrint('[BuyNowCheckoutWebView] Order confirmation page detected');
        return true;
      }

      // Check for account order pages
      if (uri.path.contains('/account/orders/')) {
        debugPrint('[BuyNowCheckoutWebView] Account orders page detected');
        return true;
      }
    }

    debugPrint('[BuyNowCheckoutWebView] URL does not match completion patterns: $url');
    return false;
  }

  bool _isExternalLink(String url) {
    // Extract the base domain from the original checkout URL
    final checkoutUri = Uri.tryParse(widget.checkoutUrl);
    final baseDomain = checkoutUri?.host ?? '';

    // Allow Shopify-related domains
    if (url.contains('shopify.com') ||
        url.contains('shopifyinc.com') ||
        url.contains('shopifycs.com') ||
        url.contains(baseDomain)) {
      return false;
    }

    // Detect external links that should open in system browser
    return url.startsWith('mailto:') ||
           url.startsWith('tel:') ||
           url.startsWith('sms:') ||
           url.startsWith('http') && !url.contains(baseDomain);
  }

  void _handleCheckoutSuccess(String url) {
    debugPrint('[BuyNowCheckoutWebView] ⚠️ CHECKOUT SUCCESS TRIGGERED! URL: $url');

    // Prevent duplicate completion handling
    if (_hasCompletedCheckout) {
      debugPrint('[BuyNowCheckoutWebView] ⚠️ Checkout already completed, ignoring duplicate call');
      return;
    }

    _hasCompletedCheckout = true;
    debugPrint('[BuyNowCheckoutWebView] This should only happen on actual completion!');

    // Extract order information if available
    String? orderId = _extractOrderId(url);
    Map<String, String> orderData = _extractOrderData(url);

    if (orderId != null) {
      debugPrint('[BuyNowCheckoutWebView] Order ID extracted: $orderId');
    }

    if (mounted) {
      if (widget.showSuccessScreen) {
        debugPrint('[BuyNowCheckoutWebView] Navigating to success screen');
        // Navigate to dedicated success screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => CheckoutSuccessScreen(
              orderId: orderData['order_id'],
              orderNumber: orderData['order_number'],
              total: orderData['total'],
              currency: orderData['currency'],
            ),
          ),
        );
      } else {
        // If there's a callback, let it handle everything (including navigation and messages)
        if (widget.onCheckoutComplete != null) {
          debugPrint('[BuyNowCheckoutWebView] Calling onCheckoutComplete callback - this will close the WebView!');
          // Call completion callback immediately
          if (mounted) {
            widget.onCheckoutComplete!();
          }
        } else {
          debugPrint('[BuyNowCheckoutWebView] No callback, showing default success message');
          // No callback provided, show default success message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('🎉 Order completed successfully!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    }
  }

  void _handleCheckoutError(String errorMessage) {
    debugPrint('[BuyNowCheckoutWebView] Checkout error: $errorMessage');

    if (mounted) {
      // Call error callback if provided
      if (widget.onCheckoutError != null) {
        debugPrint('[BuyNowCheckoutWebView] Calling onCheckoutError callback');
        widget.onCheckoutError!(errorMessage);
      } else {
        // Fallback: show default error message
        debugPrint('[BuyNowCheckoutWebView] No error callback provided, showing default error message');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Checkout failed: $errorMessage'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  String? _extractOrderId(String url) {
    // Try to extract order ID from various URL patterns
    final patterns = [
      RegExp(r'order_id=([^&]+)'),
      RegExp(r'orders/([^/?]+)'),
      RegExp(r'order=([^&]+)'),
      RegExp(r'/(\d+)/?$'),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(url);
      if (match != null && match.group(1) != null) {
        return match.group(1);
      }
    }

    return null;
  }

  Map<String, String> _extractOrderData(String url) {
    final uri = Uri.tryParse(url);
    final data = <String, String>{};

    if (uri != null) {
      // Extract from query parameters
      data.addAll(uri.queryParameters);

      // Try to extract order ID from path if not in query
      if (!data.containsKey('order_id')) {
        final orderId = _extractOrderId(url);
        if (orderId != null) {
          data['order_id'] = orderId;
        }
      }
    }

    return data;
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('[BuyNowCheckoutWebView] 🎨 Building widget - isLoading: $_isLoading');
    return Scaffold(
      appBar: AppBar(
        title: const Text('Complete Your Order'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            _showExitConfirmation();
          },
        ),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              ),
            ),
        ],
      ),
      body: Stack(
        children: [
          // Wrap WebView in a Container to help with platform view stability
          Container(
            key: ValueKey('buy_now_checkout_container_${widget.checkoutUrl.hashCode}'),
            child: WebViewWidget(
              key: ValueKey('buy_now_checkout_webview_${widget.checkoutUrl.hashCode}'),
              controller: _controller,
            ),
          ),
          if (_isLoading)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'Loading checkout...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  void _showExitConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Exit Checkout?'),
          content: const Text(
            'Are you sure you want to exit? Your order will not be completed.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog

                // Close parent modal if provided
                if (widget.onCloseModal != null) {
                  debugPrint('[BuyNowCheckoutWebView] Closing parent modal on manual exit');
                  widget.onCloseModal!();
                }

                Navigator.of(context).pop(); // Close webview
              },
              child: const Text(
                'Exit',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
