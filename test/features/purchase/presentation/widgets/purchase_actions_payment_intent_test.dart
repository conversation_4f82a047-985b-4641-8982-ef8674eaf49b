import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_travelgator/features/purchase/domain/entities/buy_now_response.dart';

void main() {
  group('PurchaseActions Payment Intent Flow', () {
    group('BuyNowResponse Payment Intent Detection', () {
      test('should detect payment intent response correctly', () {
        final response = BuyNowResponse(
          message: 'Buy now successful',
          checkoutUrl: '',
          orderId: 'order_123',
          shopifyCartId: '',
          totalPrice: 29.99,
          currency: 'USD',
          status: 'success',
          orderType: 'buy_now',
          item: BuyNowItem(
            variantId: 'variant_456',
            productId: 'product_789',
            quantity: 1,
            price: 29.99,
            title: 'Test Product',
          ),
          clientSecret: 'pi_test_secret_123',
          paymentIntentId: 'pi_test_123',
        );

        expect(response.hasPaymentIntent, isTrue);
        expect(response.hasCheckoutUrl, isFalse);
        expect(response.clientSecret, equals('pi_test_secret_123'));
        expect(response.paymentIntentId, equals('pi_test_123'));
      });

      test('should detect checkout URL response correctly', () {
        final response = BuyNowResponse(
          message: 'Buy now successful',
          checkoutUrl: 'https://checkout.stripe.com/pay/cs_test',
          orderId: 'order_123',
          shopifyCartId: 'cart_456',
          totalPrice: 29.99,
          currency: 'USD',
          status: 'success',
          orderType: 'buy_now',
          item: BuyNowItem(
            variantId: 'variant_456',
            productId: 'product_789',
            quantity: 1,
            price: 29.99,
            title: 'Test Product',
          ),
          clientSecret: null,
          paymentIntentId: null,
        );

        expect(response.hasPaymentIntent, isFalse);
        expect(response.hasCheckoutUrl, isTrue);
        expect(response.checkoutUrl, equals('https://checkout.stripe.com/pay/cs_test'));
        expect(response.shopifyCartId, equals('cart_456'));
      });

      test('should handle response with both payment intent and checkout URL', () {
        final response = BuyNowResponse(
          message: 'Buy now successful',
          checkoutUrl: 'https://checkout.stripe.com/pay/cs_test',
          orderId: 'order_123',
          shopifyCartId: 'cart_456',
          totalPrice: 29.99,
          currency: 'USD',
          status: 'success',
          orderType: 'buy_now',
          item: BuyNowItem(
            variantId: 'variant_456',
            productId: 'product_789',
            quantity: 1,
            price: 29.99,
            title: 'Test Product',
          ),
          clientSecret: 'pi_test_secret_123',
          paymentIntentId: 'pi_test_123',
        );

        // Payment intent should take precedence
        expect(response.hasPaymentIntent, isTrue);
        expect(response.hasCheckoutUrl, isTrue);
      });

      test('should handle response with neither payment intent nor checkout URL', () {
        final response = BuyNowResponse(
          message: 'Buy now failed',
          checkoutUrl: '',
          orderId: 'order_123',
          shopifyCartId: '',
          totalPrice: 29.99,
          currency: 'USD',
          status: 'failed',
          orderType: 'buy_now',
          item: BuyNowItem(
            variantId: 'variant_456',
            productId: 'product_789',
            quantity: 1,
            price: 29.99,
            title: 'Test Product',
          ),
          clientSecret: null,
          paymentIntentId: null,
        );

        expect(response.hasPaymentIntent, isFalse);
        expect(response.hasCheckoutUrl, isFalse);
      });
    });

    group('Cart Creation for Buy Now', () {
      test('should create cart from buy now response correctly', () {
        final response = BuyNowResponse(
          message: 'Buy now successful',
          checkoutUrl: '',
          orderId: 'order_123',
          shopifyCartId: '',
          totalPrice: 29.99,
          currency: 'USD',
          status: 'success',
          orderType: 'buy_now',
          item: BuyNowItem(
            variantId: 'variant_456',
            productId: 'product_789',
            quantity: 2,
            price: 14.995, // Half price since quantity is 2
            title: 'Test eSIM Product',
          ),
          clientSecret: 'pi_test_secret_123',
          paymentIntentId: 'pi_test_123',
        );

        // Simulate cart creation logic from PurchaseActions
        final expectedCartId = 'buy-now-${response.orderId}';
        final expectedVariantId = response.item.variantId.isNotEmpty ? response.item.variantId : 'buy-now-variant';
        final expectedProductId = response.item.productId.isNotEmpty ? response.item.productId : 'buy-now-product';
        final expectedTitle = response.item.title.isNotEmpty ? response.item.title : 'Buy Now Item';

        expect(expectedCartId, equals('buy-now-order_123'));
        expect(expectedVariantId, equals('variant_456'));
        expect(expectedProductId, equals('product_789'));
        expect(expectedTitle, equals('Test eSIM Product'));
        expect(response.item.quantity, equals(2));
        expect(response.item.price, equals(14.995));
        expect(response.totalPrice, equals(29.99));
        expect(response.currency, equals('USD'));
      });

      test('should handle empty item fields with fallbacks', () {
        final response = BuyNowResponse(
          message: 'Buy now successful',
          checkoutUrl: '',
          orderId: 'order_123',
          shopifyCartId: '',
          totalPrice: 19.99,
          currency: 'USD',
          status: 'success',
          orderType: 'buy_now',
          item: BuyNowItem(
            variantId: '',
            productId: '',
            quantity: 1,
            price: 19.99,
            title: '',
          ),
          clientSecret: 'pi_test_secret_123',
          paymentIntentId: 'pi_test_123',
        );

        // Test fallback logic
        final expectedVariantId = response.item.variantId.isNotEmpty ? response.item.variantId : 'buy-now-variant';
        final expectedProductId = response.item.productId.isNotEmpty ? response.item.productId : 'buy-now-product';
        final expectedTitle = response.item.title.isNotEmpty ? response.item.title : 'Buy Now Item';

        expect(expectedVariantId, equals('buy-now-variant'));
        expect(expectedProductId, equals('buy-now-product'));
        expect(expectedTitle, equals('Buy Now Item'));
      });
    });
  });
}
