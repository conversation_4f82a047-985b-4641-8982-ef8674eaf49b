import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_travelgator/core/theme/app_theme.dart';
import 'package:flutter_travelgator/routes/app_router.dart';
import 'package:flutter_travelgator/shared/providers/locale_provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_travelgator/l10n/app_localizations.dart';
import 'package:flutter_travelgator/features/auth/presentation/providers/auth_providers.dart';
import 'package:flutter_travelgator/core/services/app_lifecycle_service.dart';

/// Main application widget
class App extends ConsumerWidget {
  /// Creates a new App instance
  const App({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(routerProvider);
    final locale = ref.watch(localeProvider);

    // Keep a reference to AuthNotifier to prevent it from being disposed
    ref.watch(authNotifierProvider);

    // Initialize app lifecycle service for token verification
    ref.watch(appLifecycleServiceProvider);

    return MaterialApp.router(
      title: 'TravelGator',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.light,
      darkTheme: AppTheme.dark,
      themeMode: ThemeMode.system,
      routerConfig: router,
      locale: locale,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: MediaQuery.textScalerOf(context),
          ),
          child: child!,
        );
      },
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en', ''), // English
        Locale('es', ''), // Spanish
        Locale('vi', ''), // Vietnamese
        Locale('zh', ''), // Chinese (Simplified)
      ],
    );
  }
}
