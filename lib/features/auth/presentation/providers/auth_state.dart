import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_travelgator/features/auth/domain/entities/user.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';

part 'auth_state.freezed.dart';

/// Represents the authentication state
@Freezed(unionKey: 'type')
class AuthState with _$AuthState {
  /// User is not authenticated and UI is in initial state
  const factory AuthState.initial() = _Initial;

  /// Authentication in progress
  const factory AuthState.loading() = _Loading;

  /// User is authenticated
  const factory AuthState.authenticated(User user) = _Authenticated;

  /// User is not authenticated
  const factory AuthState.unauthenticated() = _Unauthenticated;

  /// Authentication failed with error
  const factory AuthState.error(Failure failure) = _Error;

  // Add these extension methods until code generation is fixed
  const AuthState._(); // Private constructor for the extension methods

  // Getter for user property - returns null if not authenticated state
  User? get user {
    return this is _Authenticated ? (this as _Authenticated).user : null;
  }

  // Getter for failure property - returns null if not error state
  Failure? get failure {
    return this is _Error ? (this as _Error).failure : null;
  }



}
