import 'package:flutter/foundation.dart';
import 'package:flutter_travelgator/features/home/<USER>/models/region.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import '../configs/graphql_config.dart';
import '../models/product_model.dart';
import '../repositories/product_repository.dart';
import '../../features/home/<USER>/models/country.dart';

// GraphQL client provider
final graphQLClientProvider = Provider<GraphQLClient>((ref) {
  return GraphQLConfig.initClient().value;
});

// Product repository provider
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  final client = ref.watch(graphQLClientProvider);
  return ProductRepository(client: client);
});

// Products state class
class ProductsState {
  final List<Product> products;
  final bool isLoading;
  final String? error;
  final bool hasNextPage;
  final String? endCursor;
  final String? activeRegion;
  final String? activeCountry;
  final double? minPrice;
  final double? maxPrice;

  ProductsState({
    this.products = const [],
    this.isLoading = false,
    this.error,
    this.hasNextPage = false,
    this.endCursor,
    this.activeRegion,
    this.activeCountry,
    this.minPrice,
    this.maxPrice,
  });

  ProductsState copyWith({
    List<Product>? products,
    bool? isLoading,
    String? error,
    bool? hasNextPage,
    String? endCursor,
    String? activeRegion,
    String? activeCountry,
    double? minPrice,
    double? maxPrice,
  }) {
    return ProductsState(
      products: products ?? this.products,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      endCursor: endCursor ?? this.endCursor,
      activeRegion: activeRegion ?? this.activeRegion,
      activeCountry: activeCountry ?? this.activeCountry,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
    );
  }
}

// Products notifier
class ProductsNotifier extends StateNotifier<ProductsState> {
  final ProductRepository _repository;

  ProductsNotifier(this._repository) : super(ProductsState());

  /// Helper method to convert technical errors to user-friendly messages
  String _getUserFriendlyError(dynamic error) {
    final errorString = error.toString();
    if (errorString.contains('Network connection error') ||
        errorString.contains('No internet connection')) {
      return 'No internet connection. Please check your network.';
    } else if (errorString.contains('timeout') || errorString.contains('timed out')) {
      return 'Request timed out. Please try again.';
    } else if (errorString.contains('Failed host lookup')) {
      return 'Unable to connect to server. Please try again.';
    }
    return 'Unable to load products. Please try again.';
  }

  Future<void> fetchProducts({int first = 50, String? queryText}) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final productsConnection = await _repository.getProductsWithPagination(
        first: first,
        query: queryText,
      );

      final products =
          productsConnection.edges.map((edge) => edge.node).toList();

      state = state.copyWith(
        products: products,
        isLoading: false,
        hasNextPage: productsConnection.pageInfo.hasNextPage,
        endCursor: productsConnection.pageInfo.endCursor,
      );
    } catch (e) {
      debugPrint('🔍 ProductsNotifier: Error fetching products: $e');
      state = state.copyWith(isLoading: false, error: _getUserFriendlyError(e));
    }
  }

  /// Force refresh products from network, skipping any cache
  Future<void> forceRefreshProducts({int first = 50}) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Clear GraphQL cache for this query
      await _repository.clearProductCache();

      final productsConnection = await _repository.getProductsWithPagination(
        first: first,
        fetchPolicy: FetchPolicy.networkOnly, // Force network fetch
      );

      final products =
          productsConnection.edges.map((edge) => edge.node).toList();

      state = state.copyWith(
        products: products,
        isLoading: false,
        hasNextPage: productsConnection.pageInfo.hasNextPage,
        endCursor: productsConnection.pageInfo.endCursor,
      );
    } catch (e) {
      debugPrint('🔍 ProductsNotifier: Error force refreshing products: $e');
      state = state.copyWith(isLoading: false, error: _getUserFriendlyError(e));
    }
  }

  Future<void> fetchMoreProducts({int first = 10, String? queryText}) async {
    if (!state.hasNextPage || state.isLoading) return;

    try {
      state = state.copyWith(isLoading: true, error: null);

      final productsConnection = await _repository.getProductsWithPagination(
        first: first,
        query: queryText,
        after: state.endCursor,
      );

      final newProducts =
          productsConnection.edges.map((edge) => edge.node).toList();

      state = state.copyWith(
        products: [...state.products, ...newProducts],
        isLoading: false,
        hasNextPage: productsConnection.pageInfo.hasNextPage,
        endCursor: productsConnection.pageInfo.endCursor,
      );
    } catch (e) {
      debugPrint('🔍 ProductsNotifier: Error fetching more products: $e');
      state = state.copyWith(isLoading: false, error: _getUserFriendlyError(e));
    }
  }

  /// Fetch ALL products by continuously loading until no more pages
  Future<void> fetchAllProducts({String? queryText}) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Start with initial fetch
      await fetchProducts(first: 50, queryText: queryText);

      // Continue fetching until no more pages
      while (state.hasNextPage && state.error == null) {
        await fetchMoreProducts(first: 50, queryText: queryText);
      }

      // debugPrint('🔍 ProductsNotifier: Loaded all ${state.products.length} products');
    } catch (e) {
      // debugPrint('🔍 ProductsNotifier: Error fetching all products: $e');
      state = state.copyWith(isLoading: false, error: _getUserFriendlyError(e));
    }
  }

  /// Set active region and fetch products
  Future<void> setRegionFilter(String? region) async {
    debugPrint('🔍 ProductsNotifier: setRegionFilter called with region: $region');
    try {
      debugPrint('🔍 ProductsNotifier: Setting loading state...');
      state = state.copyWith(
        activeRegion: region,
        activeCountry: null, // Clear country filter when setting region
        isLoading: true,
        error: null,
        products: [],
        hasNextPage: false,
        endCursor: null,
      );

      debugPrint('🔍 ProductsNotifier: Fetching products from API...');
      final productsConnection = await _repository.getProductsByRegionAndCountryWithPagination(
        first: 10,
        region: region,
      );

      final products = productsConnection.edges.map((edge) => edge.node).toList();
      debugPrint('🔍 ProductsNotifier: Received ${products.length} products');

      state = state.copyWith(
        products: products,
        isLoading: false,
        hasNextPage: productsConnection.pageInfo.hasNextPage,
        endCursor: productsConnection.pageInfo.endCursor,
      );
    } catch (e) {
      debugPrint('🔍 ProductsNotifier: Error setting region filter: $e');
      state = state.copyWith(
        isLoading: false,
        error: _getUserFriendlyError(e),
      );
    }
  }

  /// Set active country and fetch products
  Future<void> setCountryFilter(String? country) async {
    // debugPrint('🔍 ProductsNotifier: setCountryFilter called with country: $country');
    try {
      // debugPrint('🔍 ProductsNotifier: Setting loading state...');
      state = state.copyWith(
        activeCountry: country,
        isLoading: true,
        error: null,
        products: [],
        hasNextPage: false,
        endCursor: null,
      );

      // debugPrint('🔍 ProductsNotifier: Fetching products from API...');
      final productsConnection = await _repository.getProductsByRegionAndCountryWithPagination(
        first: 10,
        country: country,
      );

      final products = productsConnection.edges.map((edge) => edge.node).toList();
      debugPrint('🔍 ProductsNotifier: Received ${products.length} products');

      state = state.copyWith(
        products: products,
        isLoading: false,
        hasNextPage: productsConnection.pageInfo.hasNextPage,
        endCursor: productsConnection.pageInfo.endCursor,
      );
    } catch (e) {
      debugPrint('🔍 ProductsNotifier: Error setting country filter: $e');
      state = state.copyWith(
        isLoading: false,
        error: _getUserFriendlyError(e),
      );
    }
  }

  Future<void> clearAndFetchProducts() async {
    try {
      // debugPrint('🔍 ProductsNotifier: Starting clearAndFetchProducts...');

      if (state.activeRegion != null) {
        await setRegionFilter(null);
      }
      if (state.activeCountry != null) {
        await setCountryFilter(null);
      }

      // Reset all state to initial values
      state = ProductsState(
        products: [],
        isLoading: true,
        error: null,
        hasNextPage: false,
        endCursor: null,
        activeRegion: null,
        activeCountry: null,
      );

      // Force refresh products with no cache, exactly like first run
      await forceRefreshProducts(first: 50);

      // debugPrint('🔍 ProductsNotifier: clearAndFetchProducts completed successfully');
    } catch (e) {
      // debugPrint('🔍 ProductsNotifier: Error in clearAndFetchProducts: $e');
      // Ensure loading state is reset even on error
      state = state.copyWith(
        isLoading: false,
        error: _getUserFriendlyError(e),
      );
      rethrow; // Re-throw to let caller handle if needed
    }
  }

  /// Load more products by country with pagination
  Future<void> loadMoreProductsByCountry(String country) async {
    if (state.isLoading || !state.hasNextPage) return;

    try {
      state = state.copyWith(isLoading: true);

      final productsConnection = await _repository.getProductsByRegionAndCountryWithPagination(
        first: 10,
        after: state.endCursor,
        country: country,
      );

      final newProducts = productsConnection.edges.map((edge) => edge.node).toList();

      state = state.copyWith(
        products: [...state.products, ...newProducts],
        isLoading: false,
        hasNextPage: productsConnection.pageInfo.hasNextPage,
        endCursor: productsConnection.pageInfo.endCursor,
      );
    } catch (e) {
      debugPrint('🔍 ProductsNotifier: Error loading more by region: $e');
      state = state.copyWith(
        isLoading: false,
        error: _getUserFriendlyError(e),
      );
    }
  }

  /// Load more products by region with pagination
  Future<void> loadMoreProductsByRegion(String region) async {
    if (state.isLoading || !state.hasNextPage) return;

    try {
      state = state.copyWith(isLoading: true);

      final productsConnection = await _repository.getProductsByRegionAndCountryWithPagination(
        first: 10,
        after: state.endCursor,
        region: region,
      );

      final newProducts = productsConnection.edges.map((edge) => edge.node).toList();

      state = state.copyWith(
        products: [...state.products, ...newProducts],
        isLoading: false,
        hasNextPage: productsConnection.pageInfo.hasNextPage,
        endCursor: productsConnection.pageInfo.endCursor,
      );
    } catch (e) {
      debugPrint('🔍 ProductsNotifier: Error loading more by country: $e');
      state = state.copyWith(
        isLoading: false,
        error: _getUserFriendlyError(e),
      );
    }
  }

  /// Set price filter and fetch products
  Future<void> setPriceFilter({double? minPrice, double? maxPrice}) async {
    debugPrint('🔍 ProductsNotifier: setPriceFilter called with min: $minPrice, max: $maxPrice');
    try {
      debugPrint('🔍 ProductsNotifier: Setting loading state...');
      state = state.copyWith(
        minPrice: minPrice,
        maxPrice: maxPrice,
        isLoading: true,
        error: null,
        products: [],
        hasNextPage: false,
        endCursor: null,
      );

      debugPrint('🔍 ProductsNotifier: Fetching products with price filter...');
      final productsConnection = await _repository.getProductsByPriceWithPagination(
        first: 10,
        minPrice: minPrice,
        maxPrice: maxPrice,
        country: state.activeCountry,
        region: state.activeRegion,
      );

      final products = productsConnection.edges.map((edge) => edge.node).toList();
      // debugPrint('🔍 ProductsNotifier: Received ${products.length} products');

      state = state.copyWith(
        products: products,
        isLoading: false,
        hasNextPage: productsConnection.pageInfo.hasNextPage,
        endCursor: productsConnection.pageInfo.endCursor,
      );
    } catch (e) {
      // debugPrint('🔍 ProductsNotifier: Error setting price filter: $e');
      state = state.copyWith(
        isLoading: false,
        error: _getUserFriendlyError(e),
      );
    }
  }

  /// Clear all filters without refetching products
  void clearFilterState() {
    state = state.copyWith(
      activeCountry: null,
      activeRegion: null,
      minPrice: null,
      maxPrice: null,
    );
  }

  /// Clear all filters and refetch products
  Future<void> clearFilters() async {
    state = state.copyWith(
      activeCountry: null,
      activeRegion: null,
      isLoading: true,
    );

    await fetchProducts(first: 10);
  }
}

// Product state provider
final productsProvider = StateNotifierProvider<ProductsNotifier, ProductsState>(
  (ref) {
    final repository = ref.watch(productRepositoryProvider);
    return ProductsNotifier(repository);
  },
);

/// Provider for regions
final regionsProvider = FutureProvider<List<Region>>((ref) async {
  final repository = ref.watch(productRepositoryProvider);
  return repository.getRegions();
});

/// Provider for countries
final countriesProvider = FutureProvider<List<Country>>((ref) async {
  final repository = ref.watch(productRepositoryProvider);
  return repository.getCountries();
});

/// Provider for getting minimum price by country
final minPriceByCountryProvider = FutureProvider.family<double?, String>((ref, countryCode) async {
  final repository = ref.watch(productRepositoryProvider);
  return repository.getMinPriceByCountry(countryCode);
});

/// Provider for getting minimum price by region
final minPriceByRegionProvider = FutureProvider.family<double?, String>((ref, regionCode) async {
  final repository = ref.watch(productRepositoryProvider);
  return repository.getMinPriceByRegion(regionCode);
});
