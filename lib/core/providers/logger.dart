import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A Logger for Riverpod that logs all state changes
class Logger extends ProviderObserver {
  @override
  void didUpdateProvider(
    ProviderBase provider,
    Object? previousValue,
    Object? newValue,
    ProviderContainer container,
  ) {
    // if (kDebugMode) {
    //   debugPrint('''
    // {
    //   "provider": "${provider.name ?? provider.runtimeType}",
    //   "newValue": "$newValue"
    // }''');
    // }
  }

  @override
  void didAddProvider(
    ProviderBase provider,
    Object? value,
    ProviderContainer container,
  ) {
    // if (kDebugMode) {
    //   debugPrint('''
    // {
    //   "provider": "${provider.name ?? provider.runtimeType}",
    //   "value": "$value"
    // }''');
    // }
  }

  @override
  void didDisposeProvider(ProviderBase provider, ProviderContainer container) {
    // if (kDebugMode) {
    //   debugPrint('''
    // {
    //   "provider": "${provider.name ?? provider.runtimeType}",
    //   "action": "disposed"
    // }''');
    // }
  }
}
