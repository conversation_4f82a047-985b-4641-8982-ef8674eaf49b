import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/network/api_config.dart';
import '../../../../core/utils/shopify_utils.dart';
import '../models/payment_intent_model.dart';
import '../models/payment_status_model.dart';
import '../../domain/exceptions/payment_exceptions.dart';

/// Abstract data source for buy now payment operations following documented API
abstract class BuyNowPaymentDataSource {
  /// Create payment intent for buy now
  Future<PaymentIntentModel> createBuyNowPaymentIntent({
    required String variantId,
    required int quantity,
    required double price,
    required String productName,
    required String authToken,
    String? voucherCode,
  });

  /// Check buy now payment status
  Future<PaymentStatusModel> getBuyNowPaymentStatus({
    required String paymentIntentId,
    required String authToken,
  });

  /// Confirm buy now payment with payment method
  Future<PaymentStatusModel> confirmBuyNowPayment({
    required String paymentIntentId,
    required String paymentMethodId,
    required String authToken,
  });
}

/// Implementation of buy now payment data source using Dio HTTP client
class BuyNowPaymentDataSourceImpl implements BuyNowPaymentDataSource {
  final Dio _dio;

  BuyNowPaymentDataSourceImpl({required Dio dio}) : _dio = dio;

  @override
  Future<PaymentIntentModel> createBuyNowPaymentIntent({
    required String variantId,
    required int quantity,
    required double price,
    required String productName,
    required String authToken,
    String? voucherCode,
  }) async {
    try {
      debugPrint('[BuyNowPaymentDataSource] Creating buy now payment intent...');
      debugPrint('[BuyNowPaymentDataSource] Product: $productName, Price: $price, Quantity: $quantity');
      
      // Convert Shopify GID to numeric ID if needed (to match backend expectations)
      final numericVariantId = ShopifyUtils.extractNumericId(variantId);

      final response = await _dio.post(
        ApiConfig.buyNowPaymentUrl,
        data: {
          'variant_id': numericVariantId,
          'quantity': quantity,
          'price': price,
          'title': productName, // Backend might expect 'title' instead of 'product_name'
          if (voucherCode != null && voucherCode.isNotEmpty) 'voucher_code': voucherCode,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[BuyNowPaymentDataSource] Buy now payment intent response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;

        // Check for error response format first
        if (data['success'] == false && data['error'] != null) {
          debugPrint('[BuyNowPaymentDataSource] API returned error: ${data['error']}');
          throw PaymentNetworkException(message: data['error']);
        }

        // Handle success response format
        Map<String, dynamic> paymentData;
        if (data['success'] == true && data['data'] != null) {
          paymentData = data['data'] as Map<String, dynamic>;
        } else if (data['data'] != null) {
          paymentData = data['data'] as Map<String, dynamic>;
        } else {
          paymentData = data as Map<String, dynamic>;
        }

        return PaymentIntentModel.fromJson(paymentData);
      } else {
        throw const PaymentNetworkException(
          message: 'Failed to create buy now payment intent',
        );
      }
    } on DioException catch (e) {
      debugPrint('[BuyNowPaymentDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[BuyNowPaymentDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  @override
  Future<PaymentStatusModel> getBuyNowPaymentStatus({
    required String paymentIntentId,
    required String authToken,
  }) async {
    try {
      debugPrint('[BuyNowPaymentDataSource] Getting buy now payment status: $paymentIntentId');
      
      final response = await _dio.get(
        ApiConfig.buyNowPaymentStatusUrl,
        queryParameters: {
          'payment_intent_id': paymentIntentId,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
          },
        ),
      );

      debugPrint('[BuyNowPaymentDataSource] Buy now payment status response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;
        
        // Handle response format
        Map<String, dynamic> statusData;
        if (data['success'] == true && data['data'] != null) {
          statusData = data['data'] as Map<String, dynamic>;
        } else if (data['data'] != null) {
          statusData = data['data'] as Map<String, dynamic>;
        } else {
          statusData = data as Map<String, dynamic>;
        }

        return PaymentStatusModel.fromJson(statusData);
      } else {
        throw const PaymentNetworkException(
          message: 'Failed to get buy now payment status',
        );
      }
    } on DioException catch (e) {
      debugPrint('[BuyNowPaymentDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[BuyNowPaymentDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  @override
  Future<PaymentStatusModel> confirmBuyNowPayment({
    required String paymentIntentId,
    required String paymentMethodId,
    required String authToken,
  }) async {
    try {
      debugPrint('[BuyNowPaymentDataSource] Confirming buy now payment: $paymentIntentId');
      
      final response = await _dio.post(
        ApiConfig.buyNowConfirmPaymentUrl,
        data: {
          'payment_intent_id': paymentIntentId,
          'payment_method_id': paymentMethodId,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[BuyNowPaymentDataSource] Confirm buy now payment response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;
        
        // Handle response format
        Map<String, dynamic> statusData;
        if (data['success'] == true && data['data'] != null) {
          statusData = data['data'] as Map<String, dynamic>;
        } else if (data['data'] != null) {
          statusData = data['data'] as Map<String, dynamic>;
        } else {
          statusData = data as Map<String, dynamic>;
        }

        return PaymentStatusModel.fromJson(statusData);
      } else {
        throw const PaymentNetworkException(
          message: 'Failed to confirm buy now payment',
        );
      }
    } on DioException catch (e) {
      debugPrint('[BuyNowPaymentDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[BuyNowPaymentDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Handle Dio exceptions and convert to payment exceptions
  PaymentException _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const PaymentNetworkException(
          message: 'Connection timeout. Please check your internet connection.',
        );
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final responseData = e.response?.data;
        
        if (statusCode == 401) {
          return const PaymentAuthException(
            message: 'Authentication failed. Please log in again.',
          );
        } else if (statusCode == 400) {
          final errorMessage = responseData?['error'] ?? 'Invalid request';
          return PaymentValidationException(message: errorMessage);
        } else if (statusCode == 500) {
          return const PaymentServerException(
            message: 'Server error. Please try again later.',
          );
        } else {
          final errorMessage = responseData?['error'] ?? 'Payment request failed';
          return PaymentNetworkException(message: errorMessage);
        }
      case DioExceptionType.cancel:
        return const PaymentNetworkException(
          message: 'Request was cancelled',
        );
      case DioExceptionType.unknown:
      default:
        return PaymentNetworkException(
          message: 'Network error: ${e.message}',
        );
    }
  }
}
