import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../cart/domain/entities/cart.dart';
import '../widgets/discount_code_widget.dart';
import '../widgets/wallet_credits_widget.dart';

/// Credits and discount section widget for checkout screen
class CheckoutCreditsDiscountSection extends ConsumerWidget {
  final Cart cart;
  final Function(String) onDiscountApplied;
  final Function() onDiscountRemoved;
  final Function(double) onCreditsApplied;

  const CheckoutCreditsDiscountSection({
    super.key,
    required this.cart,
    required this.onDiscountApplied,
    required this.onDiscountRemoved,
    required this.onCreditsApplied,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Text(
            'Credits & Discounts',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Credits widget
          WalletCreditsWidget(
            availableCredits: 100.0, // TODO: Get from user profile
            onCreditsApplied: onCreditsApplied,
          ),
          
          const SizedBox(height: 16),
          
          // Discount code widget
          DiscountCodeWidget(
            currentDiscount: cart.voucherCode,
            orderAmount: cart.totalPrice,
            onDiscountApplied: onDiscountApplied,
            onDiscountRemoved: onDiscountRemoved,
          ),
        ],
      ),
    );
  }
}
