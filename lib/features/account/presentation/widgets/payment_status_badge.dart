import 'package:flutter/material.dart';
import '../../domain/entities/order.dart' as entities;

/// Badge widget for displaying payment status (Success/Fail/Pending)
class PaymentStatusBadge extends StatelessWidget {
  final entities.PaymentStatus status;

  const PaymentStatusBadge({
    super.key,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      status.displayName,
      style: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: _getTextColor(),
      ),
    );
  }

  Color _getTextColor() {
    switch (status) {
      case entities.PaymentStatus.success:
        return const Color(0xFF84CC16); // Green
      case entities.PaymentStatus.failed:
        return const Color(0xFFEF4444); // Red
      case entities.PaymentStatus.pending:
        return const Color(0xFF9CA3AF); // Gray
    }
  }
}
