import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../../core/providers/product_provider.dart';
import '../../../../../l10n/app_localizations.dart';

class TravelRegionCard extends ConsumerWidget {
  final String name;
  final String? description;
  final String price;
  final String code;
  final VoidCallback onTap;
  final bool isActive;

  const TravelRegionCard({
    super.key,
    required this.name,
    this.description,
    required this.price,
    required this.code,
    required this.onTap,
    this.isActive = false,
  });

  Widget _buildRegionFlag(String regionCode) {
    // Try to use actual region flags first, fallback to representative country flags
    String flagPath;
    switch (regionCode.toLowerCase()) {
      case 'asean':
        flagPath = 'assets/images/flags/asean.svg'; // Actual ASEAN flag
        break;
      case 'east-asia':
        flagPath = 'assets/images/flags/east_asia.svg'; // Custom East Asia flag
        break;
      case 'north-america':
        flagPath = 'assets/images/flags/north_america.svg'; // Custom North America flag
        break;
      case 'oceania':
        flagPath = 'assets/images/flags/oceania.svg'; // Actual Oceania flag
        break;
      default:
        flagPath = 'assets/images/flags/${regionCode.toLowerCase()}.svg';
    }

    return SvgPicture.asset(
      flagPath,
      width: 32,
      height: 32,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        // Fallback to representative country flags
        String fallbackFlag;
        switch (regionCode.toLowerCase()) {
          case 'asean':
            fallbackFlag = 'th'; // Thailand flag for ASEAN
            break;
          case 'east-asia':
            fallbackFlag = 'cn'; // China flag for East Asia
            break;
          case 'north-america':
            fallbackFlag = 'us'; // US flag for North America
            break;
          case 'oceania':
            fallbackFlag = 'nz'; // New Zealand flag for Oceania
            break;
          default:
            fallbackFlag = regionCode.toLowerCase();
        }

        return SvgPicture.asset(
          'assets/images/flags/$fallbackFlag.svg',
          width: 32,
          height: 32,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            // Final fallback to text icon
            return Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: const Color(0xFFFF6982).withAlpha((0.1 * 255).round()),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Center(
                child: Text(
                  regionCode.substring(0, 1).toUpperCase(),
                  style: const TextStyle(
                    color: Color(0xFFFF6982),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }



  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get dynamic min price for this region (with fallback to static)
    final minPriceAsync = ref.watch(minPriceByRegionProvider(code));
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
        decoration: ShapeDecoration(
          color:
              isActive
                  ? const Color(0xFFFF6982).withAlpha((0.05 * 255).round())
                  : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side:
                isActive
                    ? const BorderSide(color: Color(0xFFFF6982), width: 1)
                    : BorderSide.none,
          ),
        ),
        child: IntrinsicHeight(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  border: isActive
                      ? Border.all(color: const Color(0xFFFF6982), width: 2)
                      : null,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: _buildRegionFlag(code),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color:
                            isActive
                                ? const Color(0xFFFF6982)
                                : const Color(0xFF1F2937),
                        fontSize: 14,
                        fontFamily: 'Inter',
                        fontWeight:
                            isActive ? FontWeight.w600 : FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      description ?? '',
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        color: Color(0xFF4B5563),
                        fontSize: 12,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  minPriceAsync.when(
                    data: (minPrice) => Text(
                      minPrice != null
                          ? '${AppLocalizations.of(context).from} \$${minPrice.toStringAsFixed(2)}'
                          : '${AppLocalizations.of(context).from} $price', // Fallback to static price
                      style: const TextStyle(
                        color: Color(0xFFFF6982),
                        fontSize: 14,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    loading: () => Text(
                      '${AppLocalizations.of(context).from} $price', // Show static price while loading
                      style: const TextStyle(
                        color: Color(0xFFFF6982),
                        fontSize: 14,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    error: (_, __) => Text(
                      '${AppLocalizations.of(context).from} $price', // Fallback to static price on error
                      style: const TextStyle(
                        color: Color(0xFFFF6982),
                        fontSize: 14,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              if (isActive) ...[
                const SizedBox(width: 8),
                const Icon(
                  Icons.check_circle_rounded,
                  color: Color(0xFFFF6982),
                  size: 18,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
