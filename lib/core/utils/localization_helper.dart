import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';

class LocalizationHelper {
  /// Get localized country name by country code
  static String getCountryName(BuildContext context, String countryCode) {
    final localizations = AppLocalizations.of(context);
    
    switch (countryCode.toLowerCase()) {
      case 'vn':
      case 'vietnam':
        return localizations.countryVietnam;
      case 'cn':
      case 'china':
        return localizations.countryChina;
      case 'us':
      case 'united-states':
      case 'united states':
        return localizations.countryUnitedStates;
      case 'nz':
      case 'new-zealand':
      case 'new zealand':
        return localizations.countryNewZealand;
      default:
        // Fallback to original name if no localization available
        return countryCode.toUpperCase();
    }
  }

  /// Get localized region name by region code
  static String getRegionName(BuildContext context, String regionCode) {
    final localizations = AppLocalizations.of(context);
    
    switch (regionCode.toLowerCase()) {
      case 'asean':
        return localizations.regionAsean;
      case 'east-asia':
      case 'east asia':
        return localizations.regionEastAsia;
      case 'north-america':
      case 'north america':
        return localizations.regionNorthAmerica;
      case 'oceania':
        return localizations.regionOceania;
      default:
        // Fallback to original name if no localization available
        return regionCode.toUpperCase();
    }
  }

  /// Get localized eSIM description
  static String getESIMDescription(BuildContext context) {
    return AppLocalizations.of(context).eSIMRoamingData;
  }
}
