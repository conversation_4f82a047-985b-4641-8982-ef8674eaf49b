import 'package:flutter/material.dart';
import '../../domain/entities/order.dart' as entities;

/// Widget for displaying money amount with status-based styling
class MoneyStatusDisplay extends StatelessWidget {
  final double amount;
  final String currency;
  final entities.PaymentStatus status;
  final bool showPlus;

  const MoneyStatusDisplay({
    super.key,
    required this.amount,
    required this.currency,
    required this.status,
    this.showPlus = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          _getFormattedAmount(),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: _getAmountColor(),
          ),
        ),
        Text(
          '($currency)',
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Color(0xFF9CA3AF), // Gray
          ),
        ),
      ],
    );
  }

  String _getFormattedAmount() {
    final prefix = showPlus && status == entities.PaymentStatus.success ? '+' : '';
    return '$prefix\$${amount.toStringAsFixed(2)}';
  }

  Color _getAmountColor() {
    switch (status) {
      case entities.PaymentStatus.success:
        return showPlus
            ? const Color(0xFF84CC16) // Green for positive amounts
            : const Color(0xFF9CA3AF); // Gray for regular amounts
      case entities.PaymentStatus.failed:
      case entities.PaymentStatus.pending:
        return const Color(0xFF9CA3AF); // Gray
    }
  }
}
