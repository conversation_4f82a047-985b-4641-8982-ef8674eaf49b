import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/buy_now_voucher_provider.dart';

class BuyNowDiscountCodeWidget extends ConsumerStatefulWidget {
  final String? currentDiscount;
  final double orderAmount;
  final Function(String)? onDiscountApplied;
  final VoidCallback? onDiscountRemoved;
  // Enhanced product-specific validation fields
  final String? variantId;
  final String? productId;
  final int? quantity;
  final double? price;
  final String? productName;

  const BuyNowDiscountCodeWidget({
    super.key,
    this.currentDiscount,
    required this.orderAmount,
    this.onDiscountApplied,
    this.onDiscountRemoved,
    // Enhanced product-specific validation fields
    this.variantId,
    this.productId,
    this.quantity,
    this.price,
    this.productName,
  });

  @override
  ConsumerState<BuyNowDiscountCodeWidget> createState() => _BuyNowDiscountCodeWidgetState();
}

class _BuyNowDiscountCodeWidgetState extends ConsumerState<BuyNowDiscountCodeWidget> {
  final TextEditingController _discountController = TextEditingController();
  bool _isValidating = false;
  bool _isApplied = false;
  String? _validationError;

  @override
  void initState() {
    super.initState();

    // Initialize with current discount if provided
    if (widget.currentDiscount != null) {
      _discountController.text = widget.currentDiscount!;
      _isApplied = true;
    }

    // Auto-remove existing voucher when entering checkout to allow fresh input
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final voucherState = ref.read(buyNowVoucherProvider);
      if (voucherState.hasVoucher) {
        debugPrint('[BuyNowDiscountCodeWidget] Auto-removing existing voucher on checkout entry');
        _removeVoucher();
      }
    });
  }

  @override
  void dispose() {
    _discountController.dispose();
    super.dispose();
  }

  /// Apply voucher code
  Future<void> _applyVoucher() async {
    final code = _discountController.text.trim();
    if (code.isEmpty) return;

    debugPrint('[BuyNowDiscountCodeWidget] Applying voucher: $code with order amount: \$${widget.orderAmount.toStringAsFixed(2)}');

    setState(() {
      _isValidating = true;
      _validationError = null;
    });

    try {
      await ref.read(buyNowVoucherProvider.notifier).applyVoucher(
        voucherCode: code,
        orderAmount: widget.orderAmount,
        // Enhanced product-specific validation fields
        variantId: widget.variantId,
        productId: widget.productId,
        quantity: widget.quantity,
        price: widget.price,
        productName: widget.productName,
      );

      final voucherState = ref.read(buyNowVoucherProvider);

      if (voucherState.hasVoucher && voucherState.errorMessage == null) {
        // Success
        setState(() {
          _isApplied = true;
          _isValidating = false;
          _validationError = null;
        });

        if (widget.onDiscountApplied != null) {
          widget.onDiscountApplied!(code);
        }
      } else {
        // Error
        setState(() {
          _validationError = voucherState.errorMessage ?? 'Failed to apply voucher';
          _isValidating = false;
        });
      }
    } catch (e) {
      setState(() {
        _validationError = 'Error applying voucher: $e';
        _isValidating = false;
      });
    }
  }

  /// Remove voucher code
  Future<void> _removeVoucher() async {
    setState(() {
      _isValidating = true;
    });

    try {
      await ref.read(buyNowVoucherProvider.notifier).removeVoucher();

      setState(() {
        _isApplied = false;
        _isValidating = false;
        _validationError = null;
      });

      _discountController.clear();

      if (widget.onDiscountRemoved != null) {
        widget.onDiscountRemoved!();
      }
    } catch (e) {
      setState(() {
        _validationError = 'Error removing voucher: $e';
        _isValidating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity, // Full screen width - responsive
      constraints: const BoxConstraints(minHeight: 100), // Increased height for better input experience
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.fromLTRB(8, 12, 16, 24), // Increased padding for better spacing
        child: Column(
          children: [
            Row(
              children: [
                // Discount code label section - flexible left side
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Discount code',
                          style: GoogleFonts.inter(
                            fontSize: 13, // Exact Figma font size
                            fontWeight: FontWeight.w600, // Exact Figma font weight
                            color: const Color(0xFF1F2937), // Exact Figma color
                          ),
                          textAlign: TextAlign.center, // Exact Figma: CENTER alignment
                        ),
                      ),
                    ],
                  ),
                ),

                // Input field section - flexible right side to FILL available space (same as credits)
                Expanded(
                  child: Container(
                    height: 48, // Same height as credits widget input
                    padding: const EdgeInsets.symmetric(horizontal: 8), // Same as credits widget
                    decoration: BoxDecoration(
                      color: Colors.white, // Same as credits widget
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Color(0xFFE5E7EB), width: 1), // Same as credits widget
                    ),
                    child: Center(
                      child: TextField(
                        controller: _discountController,
                        enabled: !_isApplied && !_isValidating,
                        textAlign: TextAlign.center, // Exact Figma: CENTER alignment
                        style: GoogleFonts.inter(
                          fontSize: 14, // Exact Figma font size
                          fontWeight: FontWeight.w500, // Exact Figma font weight (when filled)
                          color: const Color(0xFFFF6982), // Exact Figma color for PROMO2
                        ),
                        decoration: InputDecoration(
                          border: InputBorder.none, // Remove default TextField border
                          enabledBorder: InputBorder.none, // Remove border when enabled
                          focusedBorder: InputBorder.none, // Remove border when focused
                          filled: true,
                          fillColor: Colors.white,
                          hintText: 'Enter code', // Default hint when empty
                          hintStyle: GoogleFonts.inter(
                            fontSize: 14, // Exact Figma font size
                            fontWeight: FontWeight.w400, // Default weight for hint
                            color: const Color(0xFF9CA3AF), // Default hint color
                          ),
                          contentPadding: EdgeInsets.zero,
                          isDense: true,
                        ),
                        onSubmitted: (value) {
                          if (value.isNotEmpty && !_isApplied && !_isValidating) {
                            _applyVoucher();
                          }
                        },
                        onChanged: (value) {
                          // Reset applied state when user starts typing
                          if (_isApplied && value != widget.currentDiscount) {
                            setState(() {
                              _isApplied = false;
                              _validationError = null;
                            });
                          }
                        },
                      ),
                    ),
                  ),
                ),

                // Action button
                const SizedBox(width: 8),
                _buildActionButton(),
              ],
            ),

            // Error message
            if (_validationError != null)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  _validationError!,
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.red,
                  ),
                ),
              ),

            // Enhanced discount information display
            if (_isApplied)
              Consumer(
                builder: (context, ref, child) {
                  final voucherState = ref.watch(buyNowVoucherProvider);
                  if (voucherState.hasVoucher) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.green.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.check_circle,
                                  size: 16,
                                  color: Colors.green.shade600,
                                ),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    voucherState.voucherInfo?['name'] ?? 'Discount Applied',
                                    style: GoogleFonts.inter(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.green.shade700,
                                    ),
                                  ),
                                ),
                                Text(
                                  '-\$${voucherState.discountAmount.toStringAsFixed(2)}',
                                  style: GoogleFonts.inter(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.green.shade700,
                                  ),
                                ),
                              ],
                            ),
                            if (voucherState.appliedVoucherCode != null)
                              Padding(
                                padding: const EdgeInsets.only(top: 2),
                                child: Text(
                                  'Code: ${voucherState.appliedVoucherCode}',
                                  style: GoogleFonts.inter(
                                    fontSize: 11,
                                    color: Colors.green.shade600,
                                  ),
                                ),
                              ),
                            if (voucherState.finalAmount != voucherState.discountAmount)
                              Padding(
                                padding: const EdgeInsets.only(top: 2),
                                child: Text(
                                  'Final Amount: \$${voucherState.finalAmount.toStringAsFixed(2)}',
                                  style: GoogleFonts.inter(
                                    fontSize: 11,
                                    color: Colors.green.shade600,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
          ],
        ),
      ),
    );
  }

  /// Build action button (Apply/Remove)
  Widget _buildActionButton() {
    if (_isValidating) {
      return const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6982)),
        ),
      );
    }

    if (_isApplied) {
      return GestureDetector(
        onTap: _removeVoucher,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.red.shade200),
          ),
          child: Text(
            'Remove',
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.red.shade700,
            ),
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: _discountController.text.trim().isNotEmpty ? _applyVoucher : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _discountController.text.trim().isNotEmpty
              ? const Color(0xFFFF6982)
              : Colors.grey.shade300,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          'Apply',
          style: GoogleFonts.inter(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: _discountController.text.trim().isNotEmpty
                ? Colors.white
                : Colors.grey.shade600,
          ),
        ),
      ),
    );
  }
}
