import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../repositories/backend_auth_repository.dart';
import '../../presentation/providers/auth_providers.dart';
import '../../presentation/providers/backend_auth_providers.dart';
import '../../data/models/enhanced_user_model.dart';

/// Service for verifying backend tokens and handling reauthentication
class TokenVerificationService {
  final BackendAuthRepository _backendAuthRepository;
  final Ref _ref;

  TokenVerificationService({
    required BackendAuthRepository backendAuthRepository,
    required Ref ref,
  }) : _backendAuthRepository = backendAuthRepository,
       _ref = ref;

  /// Verify the current user's backend token
  /// Returns true if token is valid, false if invalid or expired
  /// Automatically handles reauthentication if token is expired
  Future<bool> verifyCurrentUserToken() async {
    try {
      debugPrint('[TokenVerification] Starting token verification...');
      
      // Use the auth notifier's verification method which handles reauthentication
      final authNotifier = _ref.read(authNotifierProvider.notifier);
      final isValid = await authNotifier.verifyBackendToken();
      
      debugPrint('[TokenVerification] Token verification result: $isValid');
      return isValid;
    } catch (e) {
      debugPrint('[TokenVerification] Error during token verification: $e');
      return false;
    }
  }

  /// Verify a specific backend token
  /// Returns true if token is valid, false otherwise
  /// Does not handle reauthentication - use verifyCurrentUserToken() for that
  Future<bool> verifyToken(String backendToken) async {
    try {
      debugPrint('[TokenVerification] Verifying specific token...');
      
      final result = await _backendAuthRepository.validateBackendToken(backendToken);
      
      return result.fold(
        (failure) {
          debugPrint('[TokenVerification] Token validation failed: ${failure.message}');
          return false;
        },
        (isValid) {
          debugPrint('[TokenVerification] Token validation result: $isValid');
          return isValid;
        },
      );
    } catch (e) {
      debugPrint('[TokenVerification] Error during token validation: $e');
      return false;
    }
  }

  /// Check if the current user has a valid backend token
  /// This is a quick check that doesn't make API calls
  bool hasValidBackendToken() {
    final authState = _ref.read(authNotifierProvider);
    
    return authState.maybeMap(
      authenticated: (state) {
        final user = state.user;
        if (user is! EnhancedUserModel) return false;
        return user.hasBackendAuth && user.backendToken != null;
      },
      orElse: () => false,
    );
  }

  /// Get the current user's backend token if available
  String? getCurrentBackendToken() {
    final authState = _ref.read(authNotifierProvider);
    
    return authState.maybeMap(
      authenticated: (state) {
        final user = state.user;
        if (user is! EnhancedUserModel) return null;
        return user.backendToken;
      },
      orElse: () => null,
    );
  }
}

/// Provider for TokenVerificationService
final tokenVerificationServiceProvider = Provider<TokenVerificationService>((ref) {
  final backendAuthRepository = ref.watch(backendAuthRepositoryProvider);
  return TokenVerificationService(
    backendAuthRepository: backendAuthRepository,
    ref: ref,
  );
});
