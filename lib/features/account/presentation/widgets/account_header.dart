import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

class AccountHeader extends ConsumerWidget {
  const AccountHeader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);

    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: [
        // Background image container
        Container(
          height: 300,
          width: double.infinity,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('assets/images/top_background.png'),
              fit: BoxFit.cover,
            ),
            borderRadius: BorderRadius.only(bottomLeft: Radius.circular(32)),
          ),
        ),

        // Avatar positioned at bottom
        Positioned(
          top: 80, // Move up by the avatar's radius
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2.0),
                ),
                child: authState.maybeMap(
                  authenticated:
                      (state) =>
                          state.user.profilePhoto != null
                              ? CircleAvatar(
                                radius: 42,
                                backgroundColor: Colors.transparent,
                                backgroundImage: NetworkImage(
                                  state.user.profilePhoto!,
                                ),
                              )
                              : const CircleAvatar(
                                radius: 42,
                                backgroundColor: Colors.transparent,
                                backgroundImage: AssetImage(
                                  'assets/images/avatar.png',
                                ),
                              ),
                  orElse:
                      () => const CircleAvatar(
                        radius: 42,
                        backgroundColor: Colors.transparent,
                        backgroundImage: AssetImage('assets/images/avatar.png'),
                      ),
                ),
              ),
              const SizedBox(height: 8),
            ],
          ),
        ),
      ],
    );
  }
}
