import '../../../../core/errors/failures.dart';

/// Base exception for payment-related errors
abstract class PaymentException implements Exception {
  final String message;
  final String? code;
  final Map<String, dynamic>? details;

  const PaymentException({
    required this.message,
    this.code,
    this.details,
  });

  @override
  String toString() => 'PaymentException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception for Stripe-specific errors
class StripeException extends PaymentException {
  final String? stripeErrorType;
  final String? stripeErrorCode;
  final String? declineCode;

  const StripeException({
    required super.message,
    super.code,
    super.details,
    this.stripeErrorType,
    this.stripeErrorCode,
    this.declineCode,
  });

  @override
  String toString() => 'StripeException: $message${stripeErrorCode != null ? ' (Stripe Code: $stripeErrorCode)' : ''}';
}

/// Exception for payment processing failures
class PaymentFailedException extends PaymentException {
  final String? paymentIntentId;
  final String? failureReason;

  const PaymentFailedException({
    required super.message,
    super.code,
    super.details,
    this.paymentIntentId,
    this.failureReason,
  });

  @override
  String toString() => 'PaymentFailedException: $message${failureReason != null ? ' (Reason: $failureReason)' : ''}';
}

/// Exception for payment cancellation
class PaymentCanceledException extends PaymentException {
  const PaymentCanceledException({
    super.message = 'Payment was canceled by user',
    super.code = 'payment_canceled',
    super.details,
  });
}

/// Exception for invalid payment configuration
class PaymentConfigurationException extends PaymentException {
  const PaymentConfigurationException({
    required super.message,
    super.code = 'configuration_error',
    super.details,
  });
}

/// Exception for network-related payment errors
class PaymentNetworkException extends PaymentException {
  const PaymentNetworkException({
    required super.message,
    super.code = 'network_error',
    super.details,
  });
}

/// Exception for authentication-related payment errors
class PaymentAuthenticationException extends PaymentException {
  const PaymentAuthenticationException({
    required super.message,
    super.code = 'authentication_error',
    super.details,
  });
}

/// Exception for authentication-related payment errors (alias)
class PaymentAuthException extends PaymentException {
  const PaymentAuthException({
    required super.message,
    super.code = 'auth_error',
    super.details,
  });
}

/// Exception for validation-related payment errors
class PaymentValidationException extends PaymentException {
  const PaymentValidationException({
    required super.message,
    super.code = 'validation_error',
    super.details,
  });
}

/// Exception for server-related payment errors
class PaymentServerException extends PaymentException {
  const PaymentServerException({
    required super.message,
    super.code = 'server_error',
    super.details,
  });
}

/// Convert payment exceptions to failures
class PaymentFailure extends Failure {
  const PaymentFailure({required super.message});

  factory PaymentFailure.fromException(PaymentException exception) {
    return PaymentFailure(message: exception.message);
  }
}

/// Convert Stripe exceptions to failures
class StripeFailure extends Failure {
  final String? stripeErrorCode;
  final String? declineCode;

  const StripeFailure({
    required super.message,
    this.stripeErrorCode,
    this.declineCode,
  });

  factory StripeFailure.fromException(StripeException exception) {
    return StripeFailure(
      message: exception.message,
      stripeErrorCode: exception.stripeErrorCode,
      declineCode: exception.declineCode,
    );
  }
}
