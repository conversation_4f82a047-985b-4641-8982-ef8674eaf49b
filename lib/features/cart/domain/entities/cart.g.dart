// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cart.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CartItem _$CartItemFromJson(Map<String, dynamic> json) => CartItem(
  variantId: json['variant_id'] as String,
  productId: json['product_id'] as String,
  quantity: (json['quantity'] as num).toInt(),
  price: (json['price'] as num).toDouble(),
  title: json['title'] as String,
  imageUrl: json['image_url'] as String?,
  selected: json['selected'] as bool? ?? true,
);

Map<String, dynamic> _$CartItemToJson(CartItem instance) => <String, dynamic>{
  'variant_id': instance.variantId,
  'product_id': instance.productId,
  'quantity': instance.quantity,
  'price': instance.price,
  'title': instance.title,
  'image_url': instance.imageUrl,
  'selected': instance.selected,
};

Cart _$CartFromJson(Map<String, dynamic> json) => Cart(
  id: json['id'] as String?,
  items:
      (json['items'] as List<dynamic>)
          .map((e) => CartItem.fromJson(e as Map<String, dynamic>))
          .toList(),
  totalPrice: (json['total_price'] as num).toDouble(),
  currency: json['currency'] as String?,
  itemsCount: (json['items_count'] as num).toInt(),
  selectedItemsCount: (json['selected_items_count'] as num?)?.toInt(),
  selectedTotalPrice: (json['selected_total_price'] as num?)?.toDouble(),
  allItemsSelected: json['all_items_selected'] as bool?,
  anyItemsSelected: json['any_items_selected'] as bool?,
  originalTotal: (json['original_total'] as num?)?.toDouble(),
  totalDiscount: (json['total_discount'] as num?)?.toDouble(),
  hasDiscount: json['has_discount'] as bool?,
  voucherCode: json['voucher_code'] as String?,
  discountAmount: (json['discount_amount'] as num?)?.toDouble(),
  discountedTotal: (json['discounted_total'] as num?)?.toDouble(),
  hasVoucher: json['has_voucher'] as bool?,
  shopifyDiscount:
      json['shopify_discount'] == null
          ? null
          : ShopifyDiscount.fromJson(
            json['shopify_discount'] as Map<String, dynamic>,
          ),
  discountSummary:
      json['discount_summary'] == null
          ? null
          : DiscountSummary.fromJson(
            json['discount_summary'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$CartToJson(Cart instance) => <String, dynamic>{
  'id': instance.id,
  'items': instance.items,
  'total_price': instance.totalPrice,
  'currency': instance.currency,
  'items_count': instance.itemsCount,
  'selected_items_count': instance.selectedItemsCount,
  'selected_total_price': instance.selectedTotalPrice,
  'all_items_selected': instance.allItemsSelected,
  'any_items_selected': instance.anyItemsSelected,
  'original_total': instance.originalTotal,
  'total_discount': instance.totalDiscount,
  'has_discount': instance.hasDiscount,
  'voucher_code': instance.voucherCode,
  'discount_amount': instance.discountAmount,
  'discounted_total': instance.discountedTotal,
  'has_voucher': instance.hasVoucher,
  'shopify_discount': instance.shopifyDiscount,
  'discount_summary': instance.discountSummary,
};
