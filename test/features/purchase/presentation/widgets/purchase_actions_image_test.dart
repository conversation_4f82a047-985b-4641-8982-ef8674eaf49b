import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_travelgator/features/cart/domain/entities/cart.dart';

void main() {
  group('PurchaseActions Buy Now Cart Creation', () {
    test('should create cart with correct product data including image', () {
      // Simulate the data that would be passed to PurchaseActions
      const variantId = 'gid://shopify/ProductVariant/46446505164938';
      const productId = 'gid://shopify/Product/8607038374026';
      const quantity = 1;
      const price = 1.3;
      const title = 'Turkey eSIM Roaming Data 1- 12GB, 1- 15 days | true no speed reduction | no daily limit - 1 GB / 1 Day - 1 GB for 1 Day (New eSIM)';
      const imageUrl = 'https://cdn.shopify.com/s/files/1/0123/4567/products/turkey-esim.jpg';
      const orderId = '6887470d77c4f29be583864b';
      const totalPrice = 1.3;
      const currency = 'USD';

      // Simulate cart creation logic from PurchaseActions
      final buyNowCart = Cart(
        id: 'buy-now-$orderId',
        items: [
          CartItem(
            variantId: variantId,
            productId: productId,
            quantity: quantity,
            price: price,
            title: title,
            imageUrl: imageUrl, // Now includes the actual product image
          ),
        ],
        totalPrice: totalPrice,
        currency: currency,
        itemsCount: quantity,
      );

      // Verify the cart is created correctly
      expect(buyNowCart.id, equals('buy-now-$orderId'));
      expect(buyNowCart.items.length, equals(1));
      expect(buyNowCart.totalPrice, equals(totalPrice));
      expect(buyNowCart.currency, equals(currency));
      expect(buyNowCart.itemsCount, equals(quantity));

      // Verify the cart item has all the correct data
      final cartItem = buyNowCart.items.first;
      expect(cartItem.variantId, equals(variantId));
      expect(cartItem.productId, equals(productId));
      expect(cartItem.quantity, equals(quantity));
      expect(cartItem.price, equals(price));
      expect(cartItem.title, equals(title));
      expect(cartItem.imageUrl, equals(imageUrl)); // Image URL is now included
    });

    test('should handle missing image URL gracefully', () {
      // Test case where imageUrl is null
      const variantId = 'variant-123';
      const productId = 'product-456';
      const quantity = 2;
      const price = 29.99;
      const title = 'Test Product';
      const String? imageUrl = null; // No image URL
      const orderId = 'order-789';
      const totalPrice = 59.98;
      const currency = 'USD';

      final buyNowCart = Cart(
        id: 'buy-now-$orderId',
        items: [
          CartItem(
            variantId: variantId,
            productId: productId,
            quantity: quantity,
            price: price,
            title: title,
            imageUrl: imageUrl, // null is handled gracefully
          ),
        ],
        totalPrice: totalPrice,
        currency: currency,
        itemsCount: quantity,
      );

      final cartItem = buyNowCart.items.first;
      expect(cartItem.imageUrl, isNull);
      expect(cartItem.variantId, equals(variantId));
      expect(cartItem.productId, equals(productId));
      expect(cartItem.title, equals(title));
    });

    test('should use fallback values when widget data is null', () {
      // Test fallback logic when widget properties are null
      const String? variantId = null;
      const String? productId = null;
      const int? quantity = null;
      const double? price = null;
      const String? title = null;
      const String? imageUrl = null;
      const orderId = 'fallback-order';
      const totalPrice = 19.99;
      const currency = 'USD';

      // Simulate the fallback logic from PurchaseActions
      final buyNowCart = Cart(
        id: 'buy-now-$orderId',
        items: [
          CartItem(
            variantId: variantId ?? 'buy-now-variant',
            productId: productId ?? 'buy-now-product',
            quantity: quantity ?? 1,
            price: price ?? totalPrice,
            title: title ?? 'Buy Now Item',
            imageUrl: imageUrl, // null is acceptable
          ),
        ],
        totalPrice: totalPrice,
        currency: currency,
        itemsCount: quantity ?? 1,
      );

      final cartItem = buyNowCart.items.first;
      expect(cartItem.variantId, equals('buy-now-variant'));
      expect(cartItem.productId, equals('buy-now-product'));
      expect(cartItem.quantity, equals(1));
      expect(cartItem.price, equals(totalPrice));
      expect(cartItem.title, equals('Buy Now Item'));
      expect(cartItem.imageUrl, isNull);
    });

    test('should preserve detailed product title from purchase state', () {
      // Test that the detailed title from purchase state is preserved
      const variantId = 'variant-detailed';
      const productId = 'product-detailed';
      const quantity = 1;
      const price = 15.99;
      const detailedTitle = 'Turkey eSIM - 5GB Data - 7 Days - New eSIM';
      const imageUrl = 'https://example.com/detailed-product.jpg';
      const orderId = 'detailed-order';
      const totalPrice = 15.99;
      const currency = 'USD';

      final buyNowCart = Cart(
        id: 'buy-now-$orderId',
        items: [
          CartItem(
            variantId: variantId,
            productId: productId,
            quantity: quantity,
            price: price,
            title: detailedTitle, // Detailed title with variant info
            imageUrl: imageUrl,
          ),
        ],
        totalPrice: totalPrice,
        currency: currency,
        itemsCount: quantity,
      );

      final cartItem = buyNowCart.items.first;
      expect(cartItem.title, equals(detailedTitle));
      expect(cartItem.imageUrl, equals(imageUrl));
      expect(cartItem.variantId, equals(variantId));
      expect(cartItem.productId, equals(productId));
    });
  });
}
