import 'package:equatable/equatable.dart';

/// Payment result entity representing the outcome of a payment attempt
class PaymentResult extends Equatable {
  final String id;
  final PaymentStatus status;
  final String? paymentIntentId;
  final String? paymentMethodId;
  final int? amount;
  final String? currency;
  final String? receiptUrl;
  final String? failureReason;
  final String? errorMessage;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;

  const PaymentResult({
    required this.id,
    required this.status,
    this.paymentIntentId,
    this.paymentMethodId,
    this.amount,
    this.currency,
    this.receiptUrl,
    this.failureReason,
    this.errorMessage,
    this.metadata,
    required this.createdAt,
  });

  /// Check if payment was successful
  bool get isSuccessful => status == PaymentStatus.succeeded;

  /// Check if payment failed
  bool get isFailed => status == PaymentStatus.failed;

  /// Check if payment is pending
  bool get isPending => status == PaymentStatus.pending;

  /// Check if payment was canceled
  bool get isCanceled => status == PaymentStatus.canceled;

  @override
  List<Object?> get props => [
        id,
        status,
        paymentIntentId,
        paymentMethodId,
        amount,
        currency,
        receiptUrl,
        failureReason,
        errorMessage,
        metadata,
        createdAt,
      ];

  @override
  String toString() {
    return 'PaymentResult(id: $id, status: $status, amount: $amount, '
        'currency: $currency, paymentIntentId: $paymentIntentId)';
  }
}

/// Payment status enumeration
enum PaymentStatus {
  pending,
  processing,
  succeeded,
  failed,
  canceled,
  requiresAction,
  requiresPaymentMethod,
}

/// Extension for PaymentStatus to provide display names
extension PaymentStatusExtension on PaymentStatus {
  String get displayName {
    switch (this) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.processing:
        return 'Processing';
      case PaymentStatus.succeeded:
        return 'Succeeded';
      case PaymentStatus.failed:
        return 'Failed';
      case PaymentStatus.canceled:
        return 'Canceled';
      case PaymentStatus.requiresAction:
        return 'Requires Action';
      case PaymentStatus.requiresPaymentMethod:
        return 'Requires Payment Method';
    }
  }

  bool get isTerminal {
    return this == PaymentStatus.succeeded ||
        this == PaymentStatus.failed ||
        this == PaymentStatus.canceled;
  }
}
