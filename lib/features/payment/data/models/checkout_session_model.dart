import '../../../cart/domain/entities/checkout_item.dart';

/// Data model for Stripe checkout session response from backend
class CheckoutSessionModel {
  final String checkoutUrl;
  final String orderId;
  final String stripeSessionId;
  final double totalAmount;
  final String currency;
  final DateTime expiresAt;
  final List<CheckoutItemModel> selectedItems;

  const CheckoutSessionModel({
    required this.checkoutUrl,
    required this.orderId,
    required this.stripeSessionId,
    required this.totalAmount,
    required this.currency,
    required this.expiresAt,
    required this.selectedItems,
  });

  /// Create from JSON response
  factory CheckoutSessionModel.fromJson(Map<String, dynamic> json) {
    return CheckoutSessionModel(
      checkoutUrl: json['checkout_url'] as String,
      orderId: json['order_id'] as String,
      stripeSessionId: json['stripe_session_id'] as String,
      totalAmount: (json['total_amount'] as num).toDouble(),
      currency: json['currency'] as String,
      expiresAt: DateTime.parse(json['expires_at'] as String),
      selectedItems: (json['selected_items'] as List<dynamic>?)
          ?.map((item) => CheckoutItemModel.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'checkout_url': checkoutUrl,
      'order_id': orderId,
      'stripe_session_id': stripeSessionId,
      'total_amount': totalAmount,
      'currency': currency,
      'expires_at': expiresAt.toIso8601String(),
      'selected_items': selectedItems.map((item) => item.toJson()).toList(),
    };
  }

  /// Convert to domain entity
  /// TODO: Create CheckoutSession domain entity if needed
  // CheckoutSession toDomain() {
  //   return CheckoutSession(
  //     checkoutUrl: checkoutUrl,
  //     orderId: orderId,
  //     stripeSessionId: stripeSessionId,
  //     totalAmount: totalAmount,
  //     currency: currency,
  //     expiresAt: expiresAt,
  //     selectedItems: selectedItems.map((item) => item.toDomain()).toList(),
  //   );
  // }

  @override
  String toString() {
    return 'CheckoutSessionModel(checkoutUrl: $checkoutUrl, orderId: $orderId, stripeSessionId: $stripeSessionId, totalAmount: $totalAmount, currency: $currency, expiresAt: $expiresAt, selectedItems: $selectedItems)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CheckoutSessionModel &&
        other.checkoutUrl == checkoutUrl &&
        other.orderId == orderId &&
        other.stripeSessionId == stripeSessionId &&
        other.totalAmount == totalAmount &&
        other.currency == currency &&
        other.expiresAt == expiresAt &&
        other.selectedItems == selectedItems;
  }

  @override
  int get hashCode {
    return checkoutUrl.hashCode ^
        orderId.hashCode ^
        stripeSessionId.hashCode ^
        totalAmount.hashCode ^
        currency.hashCode ^
        expiresAt.hashCode ^
        selectedItems.hashCode;
  }
}

/// Data model for checkout item
class CheckoutItemModel {
  final String variantId;
  final String productId;
  final int quantity;
  final double price;
  final String title;
  final double lineTotal;

  const CheckoutItemModel({
    required this.variantId,
    required this.productId,
    required this.quantity,
    required this.price,
    required this.title,
    required this.lineTotal,
  });

  /// Create from JSON response
  factory CheckoutItemModel.fromJson(Map<String, dynamic> json) {
    return CheckoutItemModel(
      variantId: json['variant_id'] as String,
      productId: json['product_id'] as String,
      quantity: json['quantity'] as int,
      price: (json['price'] as num).toDouble(),
      title: json['title'] as String,
      lineTotal: (json['line_total'] as num).toDouble(),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'variant_id': variantId,
      'product_id': productId,
      'quantity': quantity,
      'price': price,
      'title': title,
      'line_total': lineTotal,
    };
  }

  /// Convert to domain entity
  CheckoutItem toDomain() {
    return CheckoutItem(
      variantId: variantId,
      quantity: quantity,
    );
  }

  @override
  String toString() {
    return 'CheckoutItemModel(variantId: $variantId, productId: $productId, quantity: $quantity, price: $price, title: $title, lineTotal: $lineTotal)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CheckoutItemModel &&
        other.variantId == variantId &&
        other.productId == productId &&
        other.quantity == quantity &&
        other.price == price &&
        other.title == title &&
        other.lineTotal == lineTotal;
  }

  @override
  int get hashCode {
    return variantId.hashCode ^
        productId.hashCode ^
        quantity.hashCode ^
        price.hashCode ^
        title.hashCode ^
        lineTotal.hashCode;
  }
}
