import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/network/api_config.dart';
import '../models/payment_intent_model.dart';
import '../models/payment_status_model.dart';
import '../models/shipping_address_model.dart';
import '../../domain/exceptions/payment_exceptions.dart';

/// Abstract data source for cart payment operations following documented API
abstract class CartPaymentDataSource {
  /// Create payment intent for cart checkout
  Future<PaymentIntentModel> createCartPaymentIntent({
    required ShippingAddressModel shippingAddress,
    required String authToken,
  });

  /// Create payment intent for digital product cart checkout (no shipping required)
  Future<PaymentIntentModel> createCartPaymentIntentDigital({
    required String authToken,
  });

  /// Create payment intent for specific cart items (selected items checkout)
  Future<PaymentIntentModel> createCartPaymentIntentWithItems({
    required List<Map<String, dynamic>> cartItems,
    required String authToken,
  });

  /// Check payment status
  Future<PaymentStatusModel> getPaymentStatus({
    required String paymentIntentId,
    required String authToken,
  });

  /// Confirm payment with payment method
  Future<PaymentStatusModel> confirmPayment({
    required String paymentIntentId,
    required String paymentMethodId,
    required String authToken,
  });

  /// Confirm payment with card details via backend (PCI compliant)
  Future<PaymentStatusModel> confirmPaymentWithCardDetails({
    required String paymentIntentId,
    required String clientSecret,
    required Map<String, dynamic> cardDetails,
    required String authToken,
  });

  /// Cancel payment intent
  Future<void> cancelPayment({
    required String paymentIntentId,
    required String authToken,
  });
}

/// Implementation of cart payment data source using Dio HTTP client
class CartPaymentDataSourceImpl implements CartPaymentDataSource {
  final Dio _dio;

  CartPaymentDataSourceImpl({required Dio dio}) : _dio = dio;

  @override
  Future<PaymentIntentModel> createCartPaymentIntent({
    required ShippingAddressModel shippingAddress,
    required String authToken,
  }) async {
    try {
      debugPrint('[CartPaymentDataSource] Creating cart payment intent...');
      debugPrint('[CartPaymentDataSource] Endpoint: ${ApiConfig.cartCheckoutUrl}');
      debugPrint('[CartPaymentDataSource] Shipping address: ${shippingAddress.toJson()}');

      final response = await _dio.post(
        ApiConfig.cartCheckoutUrl,
        data: {
          'shipping_address': shippingAddress.toJson(),
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[CartPaymentDataSource] Cart payment intent response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;

        // Check for error response format first
        if (data['success'] == false && data['error'] != null) {
          debugPrint('[CartPaymentDataSource] API returned error: ${data['error']}');
          throw PaymentNetworkException(message: data['error']);
        }

        // Handle success response format
        Map<String, dynamic> paymentData;
        if (data['success'] == true && data['data'] != null) {
          paymentData = data['data'] as Map<String, dynamic>;
        } else if (data['data'] != null) {
          paymentData = data['data'] as Map<String, dynamic>;
        } else {
          paymentData = data as Map<String, dynamic>;
        }

        // Check if this is a Payment Intent response or Checkout Session response
        if (paymentData.containsKey('client_secret') && paymentData.containsKey('payment_intent_id')) {
          // Standard Payment Intent response
          return PaymentIntentModel.fromJson(paymentData);
        } else if (paymentData.containsKey('checkout_url') && paymentData.containsKey('stripe_session_id')) {
          // Checkout Session response - convert to PaymentIntentModel format
          debugPrint('[CartPaymentDataSource] Received checkout session response, converting to PaymentIntent format');
          return PaymentIntentModel.fromCheckoutSession(paymentData);
        } else {
          // Try standard format as fallback
          return PaymentIntentModel.fromJson(paymentData);
        }
      } else {
        throw const PaymentNetworkException(
          message: 'Failed to create cart payment intent',
        );
      }
    } on DioException catch (e) {
      debugPrint('[CartPaymentDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[CartPaymentDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  @override
  Future<PaymentIntentModel> createCartPaymentIntentDigital({
    required String authToken,
  }) async {
    try {
      debugPrint('[CartPaymentDataSource] Creating cart payment intent for digital product...');
      debugPrint('[CartPaymentDataSource] Endpoint: ${ApiConfig.cartCheckoutUrl}');
      debugPrint('[CartPaymentDataSource] Digital product - no shipping address required');

      final response = await _dio.post(
        ApiConfig.cartCheckoutUrl,
        data: {
          'digital_product': true, // Indicate this is a digital product
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[CartPaymentDataSource] Cart payment intent response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;

        // Check for error response format first
        if (data['success'] == false && data['error'] != null) {
          debugPrint('[CartPaymentDataSource] API returned error: ${data['error']}');
          throw PaymentNetworkException(message: data['error']);
        }

        // Handle success response format
        Map<String, dynamic> paymentData;
        if (data['success'] == true && data['data'] != null) {
          paymentData = data['data'] as Map<String, dynamic>;
        } else if (data['data'] != null) {
          paymentData = data['data'] as Map<String, dynamic>;
        } else {
          paymentData = data as Map<String, dynamic>;
        }

        // Check if this is a Payment Intent response or Checkout Session response
        if (paymentData.containsKey('client_secret') && paymentData.containsKey('payment_intent_id')) {
          // Standard Payment Intent response
          return PaymentIntentModel.fromJson(paymentData);
        } else if (paymentData.containsKey('checkout_url') && paymentData.containsKey('stripe_session_id')) {
          // Checkout Session response - convert to PaymentIntentModel format
          debugPrint('[CartPaymentDataSource] Received checkout session response, converting to PaymentIntent format');
          return PaymentIntentModel.fromCheckoutSession(paymentData);
        } else {
          // Try standard format as fallback
          return PaymentIntentModel.fromJson(paymentData);
        }
      } else {
        throw const PaymentNetworkException(
          message: 'Failed to create cart payment intent for digital product',
        );
      }
    } on DioException catch (e) {
      debugPrint('[CartPaymentDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[CartPaymentDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Create payment intent for specific cart items (selected items checkout)
  @override
  Future<PaymentIntentModel> createCartPaymentIntentWithItems({
    required List<Map<String, dynamic>> cartItems,
    required String authToken,
  }) async {
    try {
      debugPrint('[CartPaymentDataSource] Creating cart payment intent with specific items...');
      debugPrint('[CartPaymentDataSource] Endpoint: ${ApiConfig.cartCheckoutUrl}');
      debugPrint('[CartPaymentDataSource] Cart items: ${cartItems.length} items');
      debugPrint('[CartPaymentDataSource] Items data: $cartItems');

      final response = await _dio.post(
        ApiConfig.cartCheckoutUrl,
        data: {
          'items': cartItems,
          'digital_product': true, // eSIM products are digital
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[CartPaymentDataSource] Cart payment intent response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;

        // Check for error response format first
        if (data['success'] == false && data['error'] != null) {
          debugPrint('[CartPaymentDataSource] API returned error: ${data['error']}');
          throw PaymentNetworkException(message: data['error']);
        }

        // Handle success response format
        Map<String, dynamic> paymentData;
        if (data['success'] == true && data['data'] != null) {
          paymentData = data['data'] as Map<String, dynamic>;
        } else if (data['data'] != null) {
          paymentData = data['data'] as Map<String, dynamic>;
        } else {
          paymentData = data as Map<String, dynamic>;
        }

        // Check if this is a Payment Intent response or Checkout Session response
        if (paymentData.containsKey('client_secret') && paymentData.containsKey('payment_intent_id')) {
          // Standard Payment Intent response
          return PaymentIntentModel.fromJson(paymentData);
        } else if (paymentData.containsKey('checkout_url') && paymentData.containsKey('stripe_session_id')) {
          // Checkout Session response - convert to PaymentIntentModel format
          debugPrint('[CartPaymentDataSource] Received checkout session response, converting to PaymentIntent format');
          return PaymentIntentModel.fromCheckoutSession(paymentData);
        } else {
          // Try standard format as fallback
          return PaymentIntentModel.fromJson(paymentData);
        }
      } else {
        throw const PaymentNetworkException(
          message: 'Failed to create cart payment intent with items',
        );
      }
    } on DioException catch (e) {
      debugPrint('[CartPaymentDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[CartPaymentDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  @override
  Future<PaymentStatusModel> getPaymentStatus({
    required String paymentIntentId,
    required String authToken,
  }) async {
    try {
      debugPrint('[CartPaymentDataSource] Getting payment status: $paymentIntentId');
      
      final response = await _dio.get(
        ApiConfig.cartPaymentStatusUrl,
        queryParameters: {
          'payment_intent_id': paymentIntentId,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
          },
        ),
      );

      debugPrint('[CartPaymentDataSource] Payment status response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;
        
        // Handle response format
        Map<String, dynamic> statusData;
        if (data['success'] == true && data['data'] != null) {
          statusData = data['data'] as Map<String, dynamic>;
        } else if (data['data'] != null) {
          statusData = data['data'] as Map<String, dynamic>;
        } else {
          statusData = data as Map<String, dynamic>;
        }

        return PaymentStatusModel.fromJson(statusData);
      } else {
        throw const PaymentNetworkException(
          message: 'Failed to get payment status',
        );
      }
    } on DioException catch (e) {
      debugPrint('[CartPaymentDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[CartPaymentDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  @override
  Future<PaymentStatusModel> confirmPayment({
    required String paymentIntentId,
    required String paymentMethodId,
    required String authToken,
  }) async {
    try {
      debugPrint('[CartPaymentDataSource] Confirming payment: $paymentIntentId');
      
      final response = await _dio.post(
        ApiConfig.cartConfirmPaymentUrl,
        data: {
          'payment_intent_id': paymentIntentId,
          'payment_method_id': paymentMethodId,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[CartPaymentDataSource] Confirm payment response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;
        
        // Handle response format
        Map<String, dynamic> statusData;
        if (data['success'] == true && data['data'] != null) {
          statusData = data['data'] as Map<String, dynamic>;
        } else if (data['data'] != null) {
          statusData = data['data'] as Map<String, dynamic>;
        } else {
          statusData = data as Map<String, dynamic>;
        }

        return PaymentStatusModel.fromJson(statusData);
      } else {
        throw const PaymentNetworkException(
          message: 'Failed to confirm payment',
        );
      }
    } on DioException catch (e) {
      debugPrint('[CartPaymentDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[CartPaymentDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  @override
  Future<PaymentStatusModel> confirmPaymentWithCardDetails({
    required String paymentIntentId,
    required String clientSecret,
    required Map<String, dynamic> cardDetails,
    required String authToken,
  }) async {
    try {
      debugPrint('[CartPaymentDataSource] Confirming payment with card details: $paymentIntentId');
      
      final response = await _dio.post(
        ApiConfig.cartConfirmPaymentUrl, // Assuming this endpoint handles both methods
        data: {
          'payment_intent_id': paymentIntentId,
          'client_secret': clientSecret,
          'card_details': cardDetails,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[CartPaymentDataSource] Confirm payment with card details response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;
        
        // Handle response format
        Map<String, dynamic> statusData;
        if (data['success'] == true && data['data'] != null) {
          statusData = data['data'] as Map<String, dynamic>;
        } else if (data['data'] != null) {
          statusData = data['data'] as Map<String, dynamic>;
        } else {
          statusData = data as Map<String, dynamic>;
        }

        return PaymentStatusModel.fromJson(statusData);
      } else {
        throw const PaymentNetworkException(
          message: 'Failed to confirm payment with card details',
        );
      }
    } on DioException catch (e) {
      debugPrint('[CartPaymentDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[CartPaymentDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  @override
  Future<void> cancelPayment({
    required String paymentIntentId,
    required String authToken,
  }) async {
    try {
      debugPrint('[CartPaymentDataSource] Canceling payment: $paymentIntentId');
      
      final response = await _dio.post(
        ApiConfig.cartCancelPaymentUrl,
        data: {
          'payment_intent_id': paymentIntentId,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[CartPaymentDataSource] Cancel payment response: ${response.statusCode}');

      if (response.statusCode != 200) {
        throw const PaymentNetworkException(
          message: 'Failed to cancel payment',
        );
      }
    } on DioException catch (e) {
      debugPrint('[CartPaymentDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[CartPaymentDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Handle Dio exceptions and convert to payment exceptions
  PaymentException _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const PaymentNetworkException(
          message: 'Connection timeout. Please check your internet connection.',
        );
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final responseData = e.response?.data;
        
        if (statusCode == 401) {
          return const PaymentAuthException(
            message: 'Authentication failed. Please log in again.',
          );
        } else if (statusCode == 400) {
          final errorMessage = responseData?['error'] ?? 'Invalid request';
          return PaymentValidationException(message: errorMessage);
        } else if (statusCode == 500) {
          return const PaymentServerException(
            message: 'Server error. Please try again later.',
          );
        } else {
          final errorMessage = responseData?['error'] ?? 'Payment request failed';
          return PaymentNetworkException(message: errorMessage);
        }
      case DioExceptionType.cancel:
        return const PaymentNetworkException(
          message: 'Request was cancelled',
        );
      case DioExceptionType.unknown:
      default:
        return PaymentNetworkException(
          message: 'Network error: ${e.message}',
        );
    }
  }
}
