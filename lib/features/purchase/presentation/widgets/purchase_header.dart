import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../l10n/app_localizations.dart';


class PurchaseHeader extends StatelessWidget {
  final bool showDetails;

  const PurchaseHeader({
    super.key,
    this.showDetails = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
      ),
      child: Stack(
        children: [
          // Centered title (matching Figma layout)
          Center(
            child: Text(
              showDetails ? AppLocalizations.of(context).details : AppLocalizations.of(context).reviewYourPurchase,
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1F2937),
                letterSpacing: 0.0025,
                height: 1.21,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Close button positioned on the right (matching Figma)
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            child: SizedBox(
              width: 56,
              height: 48,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(
                  Icons.close,
                  color: Color(0xFF1F2937),
                  size: 24,
                ),
                padding: const EdgeInsets.all(14),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
