import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/banner_provider.dart';
import '../../domain/models/banner.dart' as banner_model;

class FeaturedBannerCard extends ConsumerStatefulWidget {
  const FeaturedBannerCard({super.key});

  @override
  ConsumerState<FeaturedBannerCard> createState() => _FeaturedBannerCardState();
}

class _FeaturedBannerCardState extends ConsumerState<FeaturedBannerCard> {
  @override
  void initState() {
    super.initState();
    // Load banners when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // debugPrint('🎯 FeaturedBannerCard: Initializing and loading banners...');
      ref.read(bannerProvider.notifier).loadBanners();
    });
  }

  void _handleBannerTap(String? linkUrl) {
    // debugPrint('🎯 FeaturedBannerCard: Banner tapped!');
    if (linkUrl == null || linkUrl.isEmpty) {
      // debugPrint('🎯   No link URL provided');
      return;
    }

    // debugPrint('🎯   Link URL: $linkUrl');
    // TODO: Implement URL launching
    // You can add url_launcher package and implement this later
  }

  @override
  Widget build(BuildContext context) {
    final bannerState = ref.watch(bannerProvider);
    final featuredBanner = bannerState.featuredBanner;

    // debugPrint('🎯 FeaturedBannerCard: Building widget...');
    // debugPrint('🎯   isLoading: ${bannerState.isLoading}');
    // debugPrint('🎯   error: ${bannerState.error}');
    // debugPrint('🎯   featuredBanner: ${featuredBanner?.handle ?? 'null'}');
    // debugPrint('🎯   banners count: ${bannerState.banners.length}');

    // Show loading state
    if (bannerState.isLoading && featuredBanner == null) {
      // debugPrint('🎯 FeaturedBannerCard: Showing loading state');
      return _buildLoadingBanner();
    }

    // Show error state - no fallback
    if (bannerState.error != null) {
      // debugPrint('🎯 FeaturedBannerCard: Showing error state: ${bannerState.error}');
      return _buildErrorBanner(bannerState.error!);
    }

    // No banner available - hide the widget completely
    if (featuredBanner == null) {
      // debugPrint('🎯 FeaturedBannerCard: No banner available - hiding widget');
      return const SizedBox.shrink(); // Hide the banner completely
    }

    // Show dynamic banner
    // debugPrint('🎯 FeaturedBannerCard: Showing dynamic banner: ${featuredBanner.handle}');
    return _buildDynamicBanner(featuredBanner);
  }

  Widget _buildLoadingBanner() {
    // debugPrint('🎯 FeaturedBannerCard: Building loading banner');
    return Container(
      width: double.infinity,
      height: 100,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Center(
        child: CircularProgressIndicator(
          color: Color(0xFFFF6982),
        ),
      ),
    );
  }

  Widget _buildErrorBanner(String error) {
    // debugPrint('🎯 FeaturedBannerCard: Building error banner: $error');
    return Container(
      width: double.infinity,
      height: 100,
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[200]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red[400],
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              'Banner Error',
              style: TextStyle(
                color: Colors.red[700],
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              error.length > 50 ? '${error.substring(0, 50)}...' : error,
              style: TextStyle(
                color: Colors.red[600],
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildDynamicBanner(banner_model.Banner banner) {
    // debugPrint('🎯 FeaturedBannerCard: Building dynamic banner');
    // debugPrint('🎯   Banner ID: ${banner.id}');
    // debugPrint('🎯   Banner Handle: ${banner.handle}');
    // debugPrint('🎯   Image URL: ${banner.imageUrl}');
    // debugPrint('🎯   Width: ${banner.width}');
    // debugPrint('🎯   Height: ${banner.height}');
    // debugPrint('🎯   Link URL: ${banner.linkUrl}');

    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final bannerWidth = banner.getWidthValue(screenWidth);
        final bannerHeight = banner.getHeightValue(screenWidth) ?? 100;

        // Remove debug prints to prevent render loop
        // debugPrint('🎯   Calculated Width: $bannerWidth');
        // debugPrint('🎯   Calculated Height: $bannerHeight');

        return GestureDetector(
          onTap: () => _handleBannerTap(banner.linkUrl),
          child: Container(
            width: double.infinity,
            height: bannerHeight,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            banner.imageUrl,
            fit: BoxFit.cover,
            width: bannerWidth,
            height: bannerHeight,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) {
                return child;
              }
              // Remove debug prints to prevent render loop
              return Container(
                color: Colors.grey[200],
                child: const Center(
                  child: CircularProgressIndicator(
                    color: Color(0xFFFF6982),
                  ),
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              // debugPrint('🎯 FeaturedBannerCard: Error loading banner image: $error');
              return Container(
                color: Colors.grey[200],
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.broken_image_outlined,
                        color: Colors.grey[400],
                        size: 32,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Image Load Error',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
      },
    );
  }
}
