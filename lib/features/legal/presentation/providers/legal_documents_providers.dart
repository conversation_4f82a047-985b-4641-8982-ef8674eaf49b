import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../shared/providers/locale_provider.dart';
import '../../data/repositories/legal_documents_repository.dart';
import '../../domain/entities/legal_document.dart';
import '../../domain/entities/legal_document_type.dart';

part 'legal_documents_providers.g.dart';

/// Provider for the legal documents repository
@riverpod
LegalDocumentsRepository legalDocumentsRepository(Ref ref) {
  return LegalDocumentsRepository();
}

/// Provider for all legal documents
@riverpod
List<LegalDocument> legalDocuments(Ref ref) {
  final repository = ref.watch(legalDocumentsRepositoryProvider);
  final locale = ref.watch(localeProvider);
  return repository.getAllDocuments(languageCode: locale.languageCode);
}

/// Provider for a specific legal document by type
@riverpod
LegalDocument? legalDocumentByType(Ref ref, LegalDocumentType type) {
  final repository = ref.watch(legalDocumentsRepositoryProvider);
  final locale = ref.watch(localeProvider);
  return repository.getDocumentByType(type, languageCode: locale.languageCode);
}

/// Provider for the currently selected document type in the modal
final selectedLegalDocumentTypeProvider = StateProvider<LegalDocumentType>((ref) {
  return LegalDocumentType.userAgreement;
});

/// Provider for the current document based on selected type
@riverpod
LegalDocument? currentLegalDocument(Ref ref) {
  final selectedType = ref.watch(selectedLegalDocumentTypeProvider);
  return ref.watch(legalDocumentByTypeProvider(selectedType));
}
