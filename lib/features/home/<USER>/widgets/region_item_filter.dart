import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../core/providers/regions_provider.dart';
import 'travel_region_card.dart';
import '../screens/filtered_products_screen.dart';

class RegionItemFilter extends ConsumerWidget {
  const RegionItemFilter({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final regionsState = ref.watch(regionsStateProvider);

    if (regionsState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFFFF6982)),
      );
    }

    if (regionsState.error != null) {
      return Center(
        child: Text(
          'Error loading regions: ${regionsState.error}',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    if (regionsState.regions.isEmpty) {
      return const Center(child: Text('No regions available'));
    }

    return Column(
      children:
          regionsState.regions.map((region) {
            final name = region.name;
            final description = region.description;
            final code = region.code;

            // All regions are displayed as non-active
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: TravelRegionCard(
                name: name,
                description: description,
                price: region.price,
                code: code,
                isActive: regionsState.activeRegion == region.name,
                onTap: () async {
                  debugPrint('🔥 Region tapped: ${region.name} (code: ${region.code})');

                  // Don't set active region here - just navigate directly
                  // FilteredProductsScreen will handle the filtering

                  // Show loading indicator
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) => const Center(
                      child: CircularProgressIndicator(color: Color(0xFFFF6982)),
                    ),
                  );

                  // Small delay to show loading
                  await Future.delayed(const Duration(milliseconds: 300)); // Reduced delay

                  if (context.mounted) {
                    Navigator.of(context).pop(); // Close loading dialog
                    debugPrint('🚀 Navigating to FilteredProductsScreen for region: ${region.name}');
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => FilteredProductsScreen(
                          regionCode: region.code, // Still pass code for backend filtering
                          filterTitle: region.name, // Use region name for display
                        ),
                      ),
                    );
                    // No need for .then() callback since we're not setting filter state
                  }
                },
              ),
            );
          }).toList(),
    );
  }
}
