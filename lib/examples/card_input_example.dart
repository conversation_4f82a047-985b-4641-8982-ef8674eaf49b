import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../core/utils/validators.dart';

/// Example of how to use the card validators in a simple form
class CardInputExample extends StatefulWidget {
  const CardInputExample({super.key});

  @override
  State<CardInputExample> createState() => _CardInputExampleState();
}

class _CardInputExampleState extends State<CardInputExample> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _cardNumberController = TextEditingController();
  final TextEditingController _expiryController = TextEditingController();
  final TextEditingController _securityCodeController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  
  bool _isFormValid = false;

  @override
  void dispose() {
    _cardNumberController.dispose();
    _expiryController.dispose();
    _securityCodeController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  void _checkFormValidity() {
    final isValid = Validators.isCardFormValid(
      cardNumber: _cardNumberController.text,
      expiryDate: _expiryController.text,
      securityCode: _securityCodeController.text,
      nameOnCard: _nameController.text,
    );
    
    setState(() {
      _isFormValid = isValid;
    });
  }

  void _submitForm() {
    if (_formKey.currentState?.validate() ?? false) {
      // Form is valid, process payment
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Card details are valid! Processing payment...'),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      // Form has errors
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fix the errors in the form'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Card Input Example'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Enter Card Details',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              
              // Card Number
              TextFormField(
                controller: _cardNumberController,
                validator: Validators.validateCardNumber,
                decoration: const InputDecoration(
                  labelText: 'Card Number',
                  hintText: '1234 5678 9012 3456',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.credit_card),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  _CardNumberFormatter(), // Apply formatting first and limit digits
                ],
                onChanged: (_) => _checkFormValidity(),
              ),
              const SizedBox(height: 16),
              
              // Expiry Date and Security Code Row
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _expiryController,
                      validator: Validators.validateExpiryDate,
                      decoration: const InputDecoration(
                        labelText: 'Expiry Date',
                        hintText: 'MM/YY',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.calendar_today),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(4),
                        _ExpiryDateFormatter(),
                      ],
                      onChanged: (_) => _checkFormValidity(),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _securityCodeController,
                      validator: Validators.validateSecurityCode,
                      decoration: const InputDecoration(
                        labelText: 'CVV',
                        hintText: '123',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.lock),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(4),
                      ],
                      onChanged: (_) => _checkFormValidity(),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Name on Card
              TextFormField(
                controller: _nameController,
                validator: Validators.validateNameOnCard,
                decoration: const InputDecoration(
                  labelText: 'Name on Card',
                  hintText: 'John Doe',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person),
                ),
                textCapitalization: TextCapitalization.words,
                onChanged: (_) => _checkFormValidity(),
              ),
              const SizedBox(height: 32),
              
              // Submit Button
              ElevatedButton(
                onPressed: _isFormValid ? _submitForm : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isFormValid ? Colors.blue : Colors.grey,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  _isFormValid ? 'Process Payment' : 'Enter Valid Card Details',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(height: 16),
              
              // Form Status
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _isFormValid ? Colors.green.shade50 : Colors.red.shade50,
                  border: Border.all(
                    color: _isFormValid ? Colors.green : Colors.red,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      _isFormValid ? Icons.check_circle : Icons.error,
                      color: _isFormValid ? Colors.green : Colors.red,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _isFormValid 
                        ? 'All card details are valid' 
                        : 'Please complete all fields correctly',
                      style: TextStyle(
                        color: _isFormValid ? Colors.green.shade700 : Colors.red.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Formatter for card number (adds spaces every 4 digits, limits based on card type)
class _CardNumberFormatter extends TextInputFormatter {

  /// Get maximum digits allowed based on card type
  static int _getMaxDigitsForCardType(String digits) {
    if (digits.isEmpty) return 19; // Allow typing initially

    // American Express: starts with 34 or 37, 15 digits
    if (digits.startsWith('34') || digits.startsWith('37')) {
      return 15;
    }

    // Diners Club: starts with 30, 36, 38, 14 digits
    if (digits.startsWith('30') || digits.startsWith('36') || digits.startsWith('38')) {
      return 14;
    }

    // Most other cards (Visa, MasterCard, etc.): 16 digits
    return 16;
  }

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Remove all non-digits
    final digitsOnly = newValue.text.replaceAll(RegExp(r'\D'), '');

    // Get max digits for this card type
    final maxDigits = _getMaxDigitsForCardType(digitsOnly);

    // Limit to maximum allowed digits for this card type
    final limitedDigits = digitsOnly.length > maxDigits
        ? digitsOnly.substring(0, maxDigits)
        : digitsOnly;

    // Format with spaces every 4 digits
    final buffer = StringBuffer();
    for (int i = 0; i < limitedDigits.length; i++) {
      if (i > 0 && i % 4 == 0) {
        buffer.write(' ');
      }
      buffer.write(limitedDigits[i]);
    }

    final formatted = buffer.toString();

    // Calculate cursor position
    int cursorPosition = formatted.length;
    if (newValue.selection.baseOffset < newValue.text.length) {
      // Try to maintain cursor position relative to digits
      final digitsBeforeCursor = newValue.text
          .substring(0, newValue.selection.baseOffset)
          .replaceAll(RegExp(r'\D'), '')
          .length;

      cursorPosition = 0;
      int digitCount = 0;
      for (int i = 0; i < formatted.length; i++) {
        if (formatted[i] != ' ') {
          digitCount++;
          if (digitCount > digitsBeforeCursor) break;
        }
        cursorPosition++;
      }
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: cursorPosition),
    );
  }
}

/// Formatter for expiry date (adds slash after 2 digits)
class _ExpiryDateFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;
    
    if (text.length == 2 && oldValue.text.length == 1) {
      return TextEditingValue(
        text: '$text/',
        selection: const TextSelection.collapsed(offset: 3),
      );
    }
    
    return newValue;
  }
}
