# TravelGator API Documentation

## Overview

This documentation covers all API endpoints for the TravelGator e-commerce platform, including the new Shopify discount integration.

### Base URL
```
Production: https://api.travelgator.com
Development: http://localhost:8889
```

### Authentication
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### ⚠️ Important: Endpoint URLs
All endpoints start with `/api/`. Make sure your client doesn't double the `/api/` prefix.

**Correct:** `GET https://api.travelgator.com/api/orders`
**Wrong:** `GET https://api.travelgator.com/api/api/orders`

## 🆕 New Features

### Shopify Discount Integration
- **Unified discount system**: Shopify discounts only (legacy vouchers removed)
- **Rich discount information**: Complete discount details in all responses
- **Advanced analytics**: Comprehensive discount usage tracking
- **Admin management**: Full discount management through Shopify Admin

### Enhanced Cart & Order Responses
- **Discount information**: All cart and order responses now include discount details
- **Savings tracking**: Clear visibility into customer savings
- **Analytics data**: Rich data for business intelligence

## 📚 Detailed API Documentation

### Core APIs
- **[Shopify Discount API](api/SHOPIFY_DISCOUNT_API.md)** - Complete discount management
- **[Deprecation Monitoring API](api/DEPRECATION_MONITORING_API.md)** - Migration tracking
- **[Order History](#order-history)** - Order management functionality

### Developer Resources
- **[Integration Guide](api/INTEGRATION_GUIDE.md)** - Step-by-step integration instructions
- **[Response Examples](api/RESPONSE_EXAMPLES.md)** - Comprehensive API response examples
- **[OpenAPI Specification](api/openapi.yaml)** - Machine-readable API specification
- **[Postman Collection](api/TravelGator_Shopify_Discounts.postman_collection.json)** - Ready-to-use API tests
- **[API Changelog](api/CHANGELOG.md)** - Version history and breaking changes

### Testing and Deployment
- **[Testing Script](../scripts/test_shopify_integration.rb)** - Automated integration testing
- **[Deployment Checklist](DEPLOYMENT_CHECKLIST.md)** - Production deployment guide
- **[Quick Reference](QUICK_REFERENCE.md)** - Common patterns and troubleshooting

## Order History

### Get Orders
`GET /api/orders`

**Enhanced Response:** Now includes complete Shopify discount information.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "60f7b*********0abcdef123",
      "total_price": 150.0,
      "original_total": 165.0,
      "currency": "USD",
      "status": "completed",
      "payment_status": "succeeded",
      "voucher_code": "SAVE10",
      "discount_amount": 15.0,
      "items": [
        {
          "variant_id": "*********",
          "quantity": 2,
          "price": 75.0,
          "title": "Premium eSIM - Europe"
        }
      ],
      "created_at": "2025-07-29T10:30:00.000Z"
    }
  ]
}
```

### Get Order Details
`GET /api/orders/:id`

**Response:** Same format as above for single order

### Check Payment Status (Updates Order Status)
`GET /api/cart/payment_status?payment_intent_id=pi_xxx`

**Important:** This endpoint automatically checks with Stripe and updates the order status if payment has completed.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "succeeded",
    "order_id": "60f7b*********0abcdef123",
    "amount_received": 15000,
    "payment_intent_id": "pi_xxx"
  }
}
```

## Voucher Code

### Validate Voucher
`POST /api/vouchers/validate_code`

**Request:**
```json
{
  "code": "SAVE10",
  "order_amount": 100.0
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "discount_amount": 10.0,
    "final_amount": 90.0
  }
}
```

### Apply Voucher to Cart
`POST /api/cart/apply_voucher`

**Request:**
```json
{
  "voucher_code": "SAVE10"
}
```

**Enhanced Response (Shopify Discount):**
```json
{
  "success": true,
  "data": {
    "cart": {
      "id": "cart_123",
      "total_price": 150.0,
      "discounted_total": 135.0,
      "total_discount": 15.0,
      "has_discount": true,
      "currency": "USD",
      "items": [
        {
          "variant_id": "variant_123",
          "product_id": "product_456",
          "quantity": 2,
          "price": 50.0,
          "title": "Premium Travel Backpack"
        }
      ],
      "shopify_discount": {
        "code": "SAVE10",
        "id": "gid://shopify/DiscountCodeNode/123",
        "title": "10% Off Everything",
        "discount_amount": 15.0
      },
      "discount_summary": {
        "type": "shopify",
        "code": "SAVE10",
        "title": "10% Off Everything",
        "amount": 15.0,
        "id": "gid://shopify/DiscountCodeNode/123"
      }
    },
    "discount": {
      "type": "shopify",
      "code": "SAVE10",
      "title": "10% Off Everything",
      "discount_amount": 15.0,
      "final_amount": 135.0
    }
  }
}
```

**Note:** Legacy voucher system has been removed. All discounts now use Shopify's discount system only.
```json
{
  "success": true,
  "data": {
    "cart": {
      "id": "cart_123",
      "total_price": 150.0,
      "discounted_total": 135.0,
      "total_discount": 15.0,
      "has_discount": true,
      "voucher_code": "LEGACY10",
      "discount_amount": 15.0,
      "discount_summary": {
        "type": "legacy",
        "code": "LEGACY10",
        "amount": 15.0
      }
    },
    "discount": {
      "type": "legacy",
      "code": "LEGACY10",
      "discount_amount": 15.0,
      "final_amount": 135.0
    }
  }
}
```

**Error Response (Product Not Eligible):**
```json
{
  "success": false,
  "error": "Invalid discount code: Discount code 'ELECTRONICS20' does not apply to any items in your cart"
}
```

### Remove Voucher from Cart
`DELETE /api/cart/remove_voucher`

**Response:**
```json
{
  "success": true,
  "data": {
    "total_price": 150.0,
    "voucher_code": null,
    "discount_amount": 0.0,
    "has_voucher": false
  }
}
```

### Apply Voucher to Buy Now Session
`POST /api/buy_now/apply_voucher`

**Enhanced Request (Recommended - supports product-specific discounts):**
```json
{
  "voucher_code": "SAVE10",
  "order_amount": 89.99,
  "variant_id": "variant_123",
  "product_id": "product_456",
  "quantity": 1,
  "price": 89.99,
  "product_name": "Premium Travel Backpack"
}
```

**Legacy Request (Limited - no product-specific validation):**
```json
{
  "voucher_code": "SAVE10",
  "order_amount": 89.99
}
```

**Request (Preview Mode - order_amount can be 0 or omitted):**
```json
{
  "voucher_code": "SAVE10",
  "order_amount": 0.0,
  "variant_id": "variant_123",
  "product_id": "product_456",
  "quantity": 1,
  "price": 89.99,
  "product_name": "Premium Travel Backpack"
}
```

**Response (Aligned with Cart Apply Voucher):**
```json
{
  "success": true,
  "data": {
    "buy_now_session": {
      "id": "buy_now_session",
      "session_type": "buy_now",
      "original_amount": 89.99,
      "final_amount": 80.99,
      "currency": "USD",
      "voucher_code": "SAVE10",
      "discount_amount": 9.0,
      "discounted_total": 80.99,
      "has_voucher": true,
      "applied_at": "2025-07-30T12:08:22.531Z"
    },
    "voucher": {
      "code": "SAVE10",
      "name": "10% Off",
      "discount_type": "percentage",
      "discount_value": 10.0
    },
    "discount_amount": 9.0,
    "final_amount": 80.99,
    "preview_mode": false
  }
}
```

**Response (Preview Mode with order_amount: 0):**
```json
{
  "success": true,
  "data": {
    "buy_now_session": {
      "id": "buy_now_session",
      "session_type": "buy_now",
      "original_amount": 0,
      "final_amount": 0,
      "currency": "USD",
      "voucher_code": "SAVE10",
      "discount_amount": 1.0,
      "discounted_total": 0,
      "has_voucher": true,
      "applied_at": "2025-07-30T12:40:47.955Z"
    },
    "voucher": {
      "code": "SAVE10",
      "name": "10% Off",
      "discount_type": "percentage",
      "discount_value": 10.0
    },
    "discount_amount": 1.0,
    "final_amount": 0,
    "preview_mode": true
  }
}
```

### Remove Voucher from Buy Now Session
`DELETE /api/buy_now/remove_voucher`

**Response (Aligned with Cart Remove Voucher):**
```json
{
  "success": true,
  "data": {
    "id": "buy_now_session",
    "session_type": "buy_now",
    "original_amount": 0,
    "final_amount": 0,
    "currency": "USD",
    "voucher_code": null,
    "discount_amount": 0.0,
    "discounted_total": 0,
    "has_voucher": false,
    "applied_at": null
  }
}
```

### Buy Now (with Session Voucher)
`POST /api/buy_now`

**Request:**
```json
{
  "variant_id": "*********",
  "quantity": 1,
  "price": 89.99,
  "product_name": "Global eSIM"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "payment_intent_id": "pi_*********0",
    "amount": 80.99,
    "voucher_applied": true,
    "discount_amount": 9.0,
    "original_amount": 89.99
  }
}
```

## Error Messages

| Error | Solution |
|-------|----------|
| `"Voucher code is required"` | Provide voucher code |
| `"Voucher code not found"` | Check spelling |
| `"Voucher has expired"` | Use different voucher |
| `"You have already used this voucher"` | Use different voucher |
| `"Order amount must be at least $X"` | Add more items |
| `"Order not found"` | Check order ID |
| `"Invalid token"` | Re-authenticate |

## Example Usage

### JavaScript
```javascript
// Get orders - ✅ Correct URL
const orders = await fetch('https://api.travelgator.com/api/orders', {
  headers: { 'Authorization': `Bearer ${token}` }
}).then(r => r.json());

// ❌ Wrong - Don't use double /api/
// const orders = await fetch('https://api.travelgator.com/api/api/orders', ...)

// Validate voucher - ✅ Correct URL
const validation = await fetch('https://api.travelgator.com/api/vouchers/validate_code', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ code: 'SAVE10', order_amount: 100 })
}).then(r => r.json());

// Apply voucher to cart - ✅ Correct URL
const result = await fetch('https://api.travelgator.com/api/cart/apply_voucher', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ voucher_code: 'SAVE10' })
}).then(r => r.json());
```

### Flutter
```dart
// Get orders
final response = await http.get(
  Uri.parse('$baseUrl/api/orders'),
  headers: {'Authorization': 'Bearer $token'},
);

// Validate voucher
final response = await http.post(
  Uri.parse('$baseUrl/api/vouchers/validate_code'),
  headers: {
    'Authorization': 'Bearer $token',
    'Content-Type': 'application/json',
  },
  body: jsonEncode({'code': 'SAVE10', 'order_amount': 100}),
);
```



