import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/network/api_config.dart';
import '../models/stripe_config_model.dart';
import '../models/payment_result_model.dart';
import '../../domain/exceptions/payment_exceptions.dart';

/// Abstract data source for Stripe operations
abstract class StripeDataSource {
  Future<StripeConfigModel> getStripeConfig(String authToken);
  Future<PaymentResultModel> verifyPayment({
    required String paymentIntentId,
    required String authToken,
  });
}

/// Implementation of Stripe data source using Dio HTTP client
class StripeDataSourceImpl implements StripeDataSource {
  final Dio _dio;

  StripeDataSourceImpl({required Dio dio}) : _dio = dio;

  @override
  Future<StripeConfigModel> getStripeConfig(String authToken) async {
    try {
      debugPrint('[StripeDataSource] Fetching Stripe configuration...');
      
      final response = await _dio.get(
        ApiConfig.stripeConfigEndpoint,
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
          },
        ),
      );

      debugPrint('[StripeDataSource] Stripe config response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;
        debugPrint('[StripeDataSource] Response status: ${response.statusCode}');
        debugPrint('[StripeDataSource] Response data type: ${data.runtimeType}');
        debugPrint('[StripeDataSource] Response data: $data');
        debugPrint('[StripeDataSource] Raw response data: $data');
        debugPrint('[StripeDataSource] Data type: ${data.runtimeType}');
        debugPrint('[StripeDataSource] Data keys: ${data is Map ? data.keys.toList() : 'Not a Map'}');

        // Handle both formats: with/without success wrapper
        Map<String, dynamic> configData;

        if (data['data'] != null) {
          // Backend format: {"data": {"publishable_key": "...", "currency": "..."}}
          debugPrint('[StripeDataSource] Using backend format with data wrapper');
          debugPrint('[StripeDataSource] data[\'data\'] = ${data['data']}');
          debugPrint('[StripeDataSource] data[\'data\'] type = ${data['data'].runtimeType}');
          configData = data['data'] as Map<String, dynamic>;
        } else if (data['success'] == true && data['data'] != null) {
          // Alternative format: {"success": true, "data": {...}}
          debugPrint('[StripeDataSource] Using success format');
          configData = data['data'] as Map<String, dynamic>;
        } else {
          // Direct format: {"publishable_key": "...", "currency": "..."}
          debugPrint('[StripeDataSource] Using direct format');
          configData = data as Map<String, dynamic>;
        }

        debugPrint('[StripeDataSource] Parsing config data: $configData');
        try {
          return StripeConfigModel.fromJson(configData);
        } catch (parseError) {
          debugPrint('[StripeDataSource] JSON parsing error: $parseError');
          debugPrint('[StripeDataSource] Config data keys: ${configData.keys.toList()}');
          debugPrint('[StripeDataSource] Config data values: ${configData.values.toList()}');
          rethrow;
        }
      } else {
        throw const PaymentNetworkException(
          message: 'Failed to fetch Stripe configuration',
        );
      }
    } on DioException catch (e) {
      debugPrint('[StripeDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[StripeDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  @override
  Future<PaymentResultModel> verifyPayment({
    required String paymentIntentId,
    required String authToken,
  }) async {
    try {
      debugPrint('[StripeDataSource] Verifying payment: $paymentIntentId');
      
      final response = await _dio.post(
        ApiConfig.verifyPaymentEndpoint,
        data: {'payment_intent_id': paymentIntentId},
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[StripeDataSource] Payment verification response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          return PaymentResultModel.fromJson(data['data'] as Map<String, dynamic>);
        } else {
          throw const PaymentFailedException(
            message: 'Failed to verify payment',
          );
        }
      } else {
        throw const PaymentNetworkException(
          message: 'Failed to verify payment',
        );
      }
    } on DioException catch (e) {
      debugPrint('[StripeDataSource] DioException: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      debugPrint('[StripeDataSource] Unexpected error: $e');
      throw PaymentNetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Handle Dio exceptions and convert to payment exceptions
  PaymentException _handleDioException(DioException e) {
    final statusCode = e.response?.statusCode;
    final responseData = e.response?.data;

    if (statusCode == 401) {
      return const PaymentAuthenticationException(
        message: 'Authentication required for payment operations',
      );
    } else if (statusCode == 403) {
      return const PaymentAuthenticationException(
        message: 'Insufficient permissions for payment operations',
      );
    } else if (statusCode == 404) {
      return const PaymentConfigurationException(
        message: 'Payment endpoint not found',
      );
    } else if (statusCode != null && statusCode >= 500) {
      return const PaymentNetworkException(
        message: 'Server error - please try again later',
      );
    }

    // Try to extract error message from response
    String errorMessage = 'Network error occurred';
    if (responseData is Map<String, dynamic>) {
      errorMessage = responseData['message'] ?? 
                    responseData['error'] ?? 
                    errorMessage;
    }

    return PaymentNetworkException(message: errorMessage);
  }
}
