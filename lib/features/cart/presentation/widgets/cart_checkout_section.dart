import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../domain/entities/cart.dart';
import '../../../../l10n/app_localizations.dart';

class CartCheckoutSection extends StatelessWidget {
  final Cart cart;
  final VoidCallback onCheckout;

  const CartCheckoutSection({
    super.key,
    required this.cart,
    required this.onCheckout,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Order Summary
              _buildOrderSummary(),
              
              const SizedBox(height: 16),
              
              // Checkout Button
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: onCheckout,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFF6982),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    AppLocalizations.of(context).proceedToCheckout,
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderSummary() {
    final subtotal = _calculateSubtotal();
    final tax = _calculateTax(subtotal);
    final total = subtotal + tax;

    return Column(
      children: [
        // Subtotal
        _buildSummaryRow(
          'Subtotal',
          '\$${subtotal.toStringAsFixed(2)}',
          isRegular: true,
        ),
        
        const SizedBox(height: 8),
        
        // Tax
        _buildSummaryRow(
          'Tax',
          '\$${tax.toStringAsFixed(2)}',
          isRegular: true,
        ),
        
        const SizedBox(height: 8),
        
        // Divider
        Container(
          height: 1,
          color: const Color(0xFFE5E7EB),
        ),
        
        const SizedBox(height: 8),
        
        // Total
        _buildSummaryRow(
          'Total',
          '\$${total.toStringAsFixed(2)}',
          isTotal: true,
        ),
      ],
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isRegular = false, bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.inter(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            color: isTotal ? const Color(0xFF1F2937) : const Color(0xFF6B7280),
          ),
        ),
        Text(
          value,
          style: GoogleFonts.inter(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.w700 : FontWeight.w600,
            color: isTotal ? const Color(0xFFFF6982) : const Color(0xFF1F2937),
          ),
        ),
      ],
    );
  }

  double _calculateSubtotal() {
    return cart.items.fold(0.0, (sum, item) => sum + (item.price * item.quantity));
  }

  double _calculateTax(double subtotal) {
    // Assuming 8.5% tax rate
    return subtotal * 0.085;
  }
}
