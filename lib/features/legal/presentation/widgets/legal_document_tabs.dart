import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../domain/entities/legal_document_type.dart';
import '../providers/legal_documents_providers.dart';

/// Tab navigation widget for switching between legal documents
class LegalDocumentTabs extends ConsumerWidget {
  const LegalDocumentTabs({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedType = ref.watch(selectedLegalDocumentTypeProvider);

    return Semantics(
      label: 'Legal document tabs',
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 24),
        decoration: BoxDecoration(
          color: const Color(0xFFF3F4F6),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: LegalDocumentType.values.map((type) {
            final isSelected = selectedType == type;
            return Expanded(
              child: _buildTab(
                context: context,
                ref: ref,
                type: type,
                isSelected: isSelected,
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildTab({
    required BuildContext context,
    required WidgetRef ref,
    required LegalDocumentType type,
    required bool isSelected,
  }) {
    return Semantics(
      label: '${type.getDisplayName(context)} tab',
      selected: isSelected,
      button: true,
      onTap: () {
        ref.read(selectedLegalDocumentTypeProvider.notifier).state = type;
      },
      child: GestureDetector(
        onTap: () {
          ref.read(selectedLegalDocumentTypeProvider.notifier).state = type;
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          margin: const EdgeInsets.all(4),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: isSelected ? Colors.white : Colors.transparent,
            borderRadius: BorderRadius.circular(6),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Text(
            type.getDisplayName(context),
            textAlign: TextAlign.center,
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              color: isSelected ? const Color(0xFF111827) : const Color(0xFF6B7280),
            ),
          ),
        ),
      ),
    );
  }
}
