import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/discount_stats.dart';

part 'discount_stats_model.g.dart';

/// Discount statistics model for API responses
@JsonSerializable()
class DiscountStatsModel {
  @Json<PERSON>ey(name: 'total_orders_with_discounts', defaultValue: 0)
  final int totalOrdersWithDiscounts;
  @JsonKey(name: 'total_savings', defaultValue: 0.0)
  final double totalSavings;
  @<PERSON>son<PERSON>ey(name: 'total_discount_codes_used', defaultValue: 0)
  final int totalDiscountCodesUsed;
  @<PERSON>sonKey(name: 'most_used_discount_code', defaultValue: '')
  final String mostUsedDiscountCode;
  @<PERSON><PERSON><PERSON>ey(name: 'average_discount_amount', defaultValue: 0.0)
  final double averageDiscountAmount;
  @<PERSON>sonKey(name: 'recent_discounts', defaultValue: <DiscountUsageModel>[])
  final List<DiscountUsageModel> recentDiscounts;

  const DiscountStatsModel({
    required this.totalOrdersWithDiscounts,
    required this.totalSavings,
    required this.totalDiscountCodesUsed,
    required this.mostUsedDiscountCode,
    required this.averageDiscountAmount,
    required this.recentDiscounts,
  });

  factory DiscountStatsModel.fromJson(Map<String, dynamic> json) =>
      _$DiscountStatsModelFromJson(json);

  Map<String, dynamic> toJson() => _$DiscountStatsModelToJson(this);

  /// Convert to domain entity
  DiscountStats toDomain() {
    return DiscountStats(
      totalOrdersWithDiscounts: totalOrdersWithDiscounts,
      totalSavings: totalSavings,
      totalDiscountCodesUsed: totalDiscountCodesUsed,
      mostUsedDiscountCode: mostUsedDiscountCode,
      averageDiscountAmount: averageDiscountAmount,
      recentDiscounts: recentDiscounts.map((model) => model.toDomain()).toList(),
    );
  }
}

/// Discount usage model for API responses
@JsonSerializable()
class DiscountUsageModel {
  final String code;
  final String? title;
  final double amount;
  @JsonKey(name: 'used_at')
  final String usedAt;
  @JsonKey(name: 'order_id')
  final String orderId;
  @JsonKey(defaultValue: 'legacy')
  final String type;

  const DiscountUsageModel({
    required this.code,
    this.title,
    required this.amount,
    required this.usedAt,
    required this.orderId,
    required this.type,
  });

  factory DiscountUsageModel.fromJson(Map<String, dynamic> json) =>
      _$DiscountUsageModelFromJson(json);

  Map<String, dynamic> toJson() => _$DiscountUsageModelToJson(this);

  /// Convert to domain entity
  DiscountUsage toDomain() {
    return DiscountUsage(
      code: code,
      title: title,
      amount: amount,
      usedAt: DateTime.parse(usedAt),
      orderId: orderId,
      type: type,
    );
  }
}
