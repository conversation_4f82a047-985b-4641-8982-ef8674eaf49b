import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../../../auth/data/models/enhanced_user_model.dart';
import '../../data/services/buy_now_voucher_service.dart';
import '../../../../core/network/api_config.dart';

/// Provider for buy now voucher service
final buyNowVoucherServiceProvider = Provider<BuyNowVoucherService>((ref) {
  return BuyNowVoucherService(dio: ApiConfig.createDioClient());
});

/// Provider for buy now voucher state
final buyNowVoucherProvider = StateNotifierProvider<BuyNowVoucherNotifier, BuyNowVoucherState>((ref) {
  return BuyNowVoucherNotifier(ref);
});

/// State for buy now voucher operations
class BuyNowVoucherState {
  final bool isLoading;
  final String? appliedVoucherCode;
  final double discountAmount;
  final double finalAmount;
  final String? errorMessage;
  final Map<String, dynamic>? buyNowSession;
  final Map<String, dynamic>? voucherInfo;

  const BuyNowVoucherState({
    this.isLoading = false,
    this.appliedVoucherCode,
    this.discountAmount = 0.0,
    this.finalAmount = 0.0,
    this.errorMessage,
    this.buyNowSession,
    this.voucherInfo,
  });

  BuyNowVoucherState copyWith({
    bool? isLoading,
    String? appliedVoucherCode,
    double? discountAmount,
    double? finalAmount,
    String? errorMessage,
    Map<String, dynamic>? buyNowSession,
    Map<String, dynamic>? voucherInfo,
    bool clearError = false,
    bool clearVoucher = false,
  }) {
    return BuyNowVoucherState(
      isLoading: isLoading ?? this.isLoading,
      appliedVoucherCode: clearVoucher ? null : (appliedVoucherCode ?? this.appliedVoucherCode),
      discountAmount: clearVoucher ? 0.0 : (discountAmount ?? this.discountAmount),
      finalAmount: clearVoucher ? 0.0 : (finalAmount ?? this.finalAmount),
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
      buyNowSession: clearVoucher ? null : (buyNowSession ?? this.buyNowSession),
      voucherInfo: clearVoucher ? null : (voucherInfo ?? this.voucherInfo),
    );
  }

  bool get hasVoucher => appliedVoucherCode != null && appliedVoucherCode!.isNotEmpty;
  bool get hasDiscount => discountAmount > 0;
}

/// Notifier for buy now voucher operations
class BuyNowVoucherNotifier extends StateNotifier<BuyNowVoucherState> {
  final Ref _ref;

  BuyNowVoucherNotifier(this._ref) : super(const BuyNowVoucherState());

  /// Get current backend token
  String? _getBackendToken() {
    final authState = _ref.read(authNotifierProvider);
    return authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth ? enhancedUser.backendToken : null;
        }
        return null;
      },
      orElse: () => null,
    );
  }

  /// Apply voucher to buy now session
  /// Enhanced version with product-specific validation support
  Future<void> applyVoucher({
    required String voucherCode,
    required double orderAmount,
    // Enhanced product-specific validation fields
    String? variantId,
    String? productId,
    int? quantity,
    double? price,
    String? productName,
  }) async {
    debugPrint('[BuyNowVoucherNotifier] Applying voucher: $voucherCode with order amount: \$${orderAmount.toStringAsFixed(2)}');
    if (variantId != null) {
      debugPrint('[BuyNowVoucherNotifier] Enhanced request with product validation: variant=$variantId, product=$productId');
    }

    final token = _getBackendToken();
    if (token == null) {
      debugPrint('[BuyNowVoucherNotifier] No backend token available');
      state = state.copyWith(
        errorMessage: 'Authentication required',
        clearError: false,
      );
      return;
    }

    if (orderAmount <= 0) {
      debugPrint('[BuyNowVoucherNotifier] Invalid order amount: $orderAmount');
      state = state.copyWith(
        errorMessage: 'Invalid order amount',
        clearError: false,
      );
      return;
    }

    state = state.copyWith(isLoading: true, clearError: true);

    try {
      final service = _ref.read(buyNowVoucherServiceProvider);
      final result = await service.applyVoucherToBuyNow(
        voucherCode: voucherCode,
        orderAmount: orderAmount,
        authToken: token,
        // Enhanced product-specific validation fields
        variantId: variantId,
        productId: productId,
        quantity: quantity,
        price: price,
        productName: productName,
      );

      if (result.success) {
        debugPrint('[BuyNowVoucherNotifier] Voucher applied successfully: $voucherCode');
        state = state.copyWith(
          isLoading: false,
          appliedVoucherCode: voucherCode,
          discountAmount: result.discountAmount,
          finalAmount: result.finalAmount,
          buyNowSession: result.buyNowSession,
          voucherInfo: result.voucherInfo,
          clearError: true,
        );
      } else {
        debugPrint('[BuyNowVoucherNotifier] Failed to apply voucher: ${result.errorMessage}');
        state = state.copyWith(
          isLoading: false,
          errorMessage: result.errorMessage ?? 'Failed to apply voucher',
        );
      }
    } catch (e) {
      debugPrint('[BuyNowVoucherNotifier] Error applying voucher: $e');
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Error applying voucher: $e',
      );
    }
  }

  /// Remove voucher from buy now session
  Future<void> removeVoucher() async {
    final token = _getBackendToken();
    if (token == null) {
      state = state.copyWith(
        errorMessage: 'Authentication required',
        clearError: false,
      );
      return;
    }

    state = state.copyWith(isLoading: true, clearError: true);

    try {
      final service = _ref.read(buyNowVoucherServiceProvider);
      final result = await service.removeVoucherFromBuyNow(authToken: token);

      if (result.success) {
        debugPrint('[BuyNowVoucherNotifier] Voucher removed successfully');
        state = state.copyWith(
          isLoading: false,
          clearVoucher: true,
          clearError: true,
        );
      } else {
        debugPrint('[BuyNowVoucherNotifier] Failed to remove voucher: ${result.errorMessage}');
        state = state.copyWith(
          isLoading: false,
          errorMessage: result.errorMessage ?? 'Failed to remove voucher',
        );
      }
    } catch (e) {
      debugPrint('[BuyNowVoucherNotifier] Error removing voucher: $e');
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Error removing voucher: $e',
      );
    }
  }

  /// Reset voucher state
  void reset() {
    state = const BuyNowVoucherState();
  }

  /// Clear error message
  void clearError() {
    state = state.copyWith(clearError: true);
  }
}
