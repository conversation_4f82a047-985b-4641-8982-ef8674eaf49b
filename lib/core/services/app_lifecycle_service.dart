import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../features/auth/presentation/providers/auth_providers.dart';
import '../../features/auth/data/models/enhanced_user_model.dart';

/// Service to handle app lifecycle events and token verification
class AppLifecycleService extends WidgetsBindingObserver {
  final Ref _ref;
  bool _isVerifyingToken = false;
  DateTime? _lastVerificationTime;
  
  // Minimum time between verifications to avoid spam
  static const Duration _minVerificationInterval = Duration(minutes: 2);

  AppLifecycleService(this._ref);

  /// Initialize the lifecycle observer
  void initialize() {
    WidgetsBinding.instance.addObserver(this);
    debugPrint('[AppLifecycle] Lifecycle observer initialized');
  }

  /// Dispose the lifecycle observer
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    debugPrint('[AppLifecycle] Lifecycle observer disposed');
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    debugPrint('[AppLifecycle] App state changed to: $state');
    
    switch (state) {
      case AppLifecycleState.resumed:
        _onAppResumed();
        break;
      case AppLifecycleState.paused:
        _onAppPaused();
        break;
      case AppLifecycleState.inactive:
        _onAppInactive();
        break;
      case AppLifecycleState.detached:
        _onAppDetached();
        break;
      case AppLifecycleState.hidden:
        _onAppHidden();
        break;
    }
  }

  /// Called when app becomes active/foreground
  void _onAppResumed() {
    debugPrint('[AppLifecycle] App resumed - checking if token verification needed');
    
    // Check if we should verify token
    if (_shouldVerifyToken()) {
      _verifyTokenInBackground();
    }
  }

  /// Called when app goes to background
  void _onAppPaused() {
    debugPrint('[AppLifecycle] App paused');
  }

  /// Called when app becomes inactive
  void _onAppInactive() {
    debugPrint('[AppLifecycle] App inactive');
  }

  /// Called when app is detached
  void _onAppDetached() {
    debugPrint('[AppLifecycle] App detached');
  }

  /// Called when app is hidden
  void _onAppHidden() {
    debugPrint('[AppLifecycle] App hidden');
  }

  /// Check if we should verify the token
  bool _shouldVerifyToken() {
    // Don't verify if already in progress
    if (_isVerifyingToken) {
      debugPrint('[AppLifecycle] Token verification already in progress');
      return false;
    }

    // Check if enough time has passed since last verification
    if (_lastVerificationTime != null) {
      final timeSinceLastVerification = DateTime.now().difference(_lastVerificationTime!);
      if (timeSinceLastVerification < _minVerificationInterval) {
        debugPrint('[AppLifecycle] Too soon since last verification (${timeSinceLastVerification.inMinutes} minutes ago)');
        return false;
      }
    }

    // Check if user is authenticated and has backend token
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && 
                 enhancedUser.backendToken != null && 
                 enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      debugPrint('[AppLifecycle] No backend authentication to verify');
      return false;
    }

    debugPrint('[AppLifecycle] Token verification needed');
    return true;
  }

  /// Verify token in background without blocking UI
  void _verifyTokenInBackground() {
    if (_isVerifyingToken) return;

    _isVerifyingToken = true;
    _lastVerificationTime = DateTime.now();

    debugPrint('[AppLifecycle] Starting background token verification...');

    // Run verification in background
    Future.microtask(() async {
      try {
        final authNotifier = _ref.read(authNotifierProvider.notifier);
        final isValid = await authNotifier.verifyBackendToken();
        
        debugPrint('[AppLifecycle] Background token verification completed: $isValid');
        
        if (isValid) {
          debugPrint('[AppLifecycle] Token is valid or was successfully refreshed');
        } else {
          debugPrint('[AppLifecycle] Token verification failed - user may need to re-authenticate');
        }
      } catch (e) {
        debugPrint('[AppLifecycle] Error during background token verification: $e');
      } finally {
        _isVerifyingToken = false;
      }
    });
  }

  /// Force token verification (for manual triggers)
  Future<bool> forceTokenVerification() async {
    if (_isVerifyingToken) {
      debugPrint('[AppLifecycle] Token verification already in progress');
      return false;
    }

    debugPrint('[AppLifecycle] Force token verification requested');
    
    try {
      _isVerifyingToken = true;
      _lastVerificationTime = DateTime.now();

      final authNotifier = _ref.read(authNotifierProvider.notifier);
      final isValid = await authNotifier.verifyBackendToken();
      
      debugPrint('[AppLifecycle] Force token verification completed: $isValid');
      return isValid;
    } catch (e) {
      debugPrint('[AppLifecycle] Error during force token verification: $e');
      return false;
    } finally {
      _isVerifyingToken = false;
    }
  }

  /// Check if token verification is currently in progress
  bool get isVerifyingToken => _isVerifyingToken;

  /// Get last verification time
  DateTime? get lastVerificationTime => _lastVerificationTime;
}

/// Provider for app lifecycle service
final appLifecycleServiceProvider = Provider<AppLifecycleService>((ref) {
  final service = AppLifecycleService(ref);
  
  // Initialize when provider is created
  service.initialize();
  
  // Dispose when provider is disposed
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});
