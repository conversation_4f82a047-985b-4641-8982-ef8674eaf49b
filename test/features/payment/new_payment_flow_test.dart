import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_travelgator/features/payment/domain/entities/payment_status.dart';

void main() {
  group('New Payment Flow Tests', () {

    group('Payment Status Entity', () {
      test('should correctly identify payment states', () {
        // Test successful payment
        const succeededStatus = PaymentStatus(
          status: 'succeeded',
          orderId: 'order_123',
          amountReceived: 2999,
          charges: [],
        );
        expect(succeededStatus.isSuccessful, true);
        expect(succeededStatus.isFinal, true);

        // Test processing payment
        const processingStatus = PaymentStatus(
          status: 'processing',
          orderId: 'order_123',
          amountReceived: 0,
          charges: [],
        );
        expect(processingStatus.isProcessing, true);
        expect(processingStatus.isFinal, false);

        // Test failed payment
        const failedStatus = PaymentStatus(
          status: 'failed',
          orderId: 'order_123',
          amountReceived: 0,
          charges: [],
        );
        expect(failedStatus.isFailed, true);
        expect(failedStatus.isFinal, true);

        // Test canceled payment
        const canceledStatus = PaymentStatus(
          status: 'canceled',
          orderId: 'order_123',
          amountReceived: 0,
          charges: [],
        );
        expect(canceledStatus.isCanceled, true);
        expect(canceledStatus.isFinal, true);

        // Test requires action payment
        const requiresActionStatus = PaymentStatus(
          status: 'requires_action',
          orderId: 'order_123',
          amountReceived: 0,
          charges: [],
        );
        expect(requiresActionStatus.requiresAction, true);
        expect(requiresActionStatus.isFinal, false);
      });
    });
  });
}
