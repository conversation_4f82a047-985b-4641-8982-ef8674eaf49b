import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart' as stripe;
import '../../domain/entities/payment_intent.dart';
import '../../domain/entities/payment_status.dart';
import '../../../cart/domain/entities/cart.dart';
import 'payment_providers.dart';

/// Provider for new checkout operations following documented API
final newCheckoutProvider = StateNotifierProvider<NewCheckoutNotifier, NewCheckoutState>((ref) {
  return NewCheckoutNotifier(ref);
});

/// State for new checkout operations
class NewCheckoutState {
  final bool isLoading;
  final String? error;
  final PaymentIntent? paymentIntent;
  final PaymentStatus? paymentStatus;
  final NewCheckoutStep currentStep;
  final bool isCartPayment;

  const NewCheckoutState({
    this.isLoading = false,
    this.error,
    this.paymentIntent,
    this.paymentStatus,
    this.currentStep = NewCheckoutStep.initial,
    this.isCartPayment = true,
  });

  NewCheckoutState copyWith({
    bool? isLoading,
    String? error,
    PaymentIntent? paymentIntent,
    PaymentStatus? paymentStatus,
    NewCheckoutStep? currentStep,
    bool? isCartPayment,
    bool clearError = false,
    bool clearPaymentIntent = false,
    bool clearPaymentStatus = false,
  }) {
    return NewCheckoutState(
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : (error ?? this.error),
      paymentIntent: clearPaymentIntent ? null : (paymentIntent ?? this.paymentIntent),
      paymentStatus: clearPaymentStatus ? null : (paymentStatus ?? this.paymentStatus),
      currentStep: currentStep ?? this.currentStep,
      isCartPayment: isCartPayment ?? this.isCartPayment,
    );
  }

  /// Check if payment is successful
  bool get isPaymentSuccessful => paymentStatus?.isSuccessful ?? false;

  /// Check if payment is still processing
  bool get isPaymentProcessing => paymentStatus?.isProcessing ?? false;

  /// Check if payment requires action
  bool get requiresAction => paymentStatus?.requiresAction ?? false;
}

/// New checkout steps enumeration
enum NewCheckoutStep {
  initial,
  creatingPaymentIntent,
  paymentIntentCreated,
  presentingPaymentSheet,
  paymentSheetCompleted,
  checkingPaymentStatus,
  paymentCompleted,
  paymentFailed,
}

/// Notifier for new checkout operations
class NewCheckoutNotifier extends StateNotifier<NewCheckoutState> {
  final Ref _ref;

  NewCheckoutNotifier(this._ref) : super(const NewCheckoutState());

  /// Create payment intent for cart checkout
  Future<void> createCartPaymentIntent(Cart cart) async {
    // Clear any existing payment intent to ensure fresh checkout
    final oldPaymentIntentId = state.paymentIntent?.paymentIntentId;
    if (oldPaymentIntentId != null) {
      debugPrint('[NewCheckout] Clearing old payment intent: $oldPaymentIntentId');
    }

    state = state.copyWith(
      isLoading: true,
      currentStep: NewCheckoutStep.creatingPaymentIntent,
      clearError: true,
      clearPaymentIntent: true,
      clearPaymentStatus: true,
    );

    try {
      debugPrint('[NewCheckout] Creating NEW cart payment intent for cart with ${cart.items.length} items');
      debugPrint('[NewCheckout] Cart total: \$${cart.totalPrice}');
      debugPrint('[NewCheckout] Cart ID: ${cart.id}');

      // Validate cart before proceeding
      if (cart.items.isEmpty) {
        throw Exception('Cart is empty. Please add items before checkout.');
      }

      if (cart.totalPrice <= 0) {
        throw Exception('Invalid cart total. Please refresh your cart.');
      }

      // eSIM products don't require shipping - this is a digital product
      debugPrint('[NewCheckout] eSIM digital product - no shipping required');

      // Convert cart items to the format expected by the backend
      final cartItems = cart.items.map((item) => {
        'variant_id': item.variantId,
        'product_id': item.productId,
        'quantity': item.quantity,
        'price': item.price,
        'title': item.title,
        'currency': cart.currency ?? 'USD',
        'image_url': item.imageUrl,
      }).toList();

      debugPrint('[NewCheckout] Sending cart items to backend: $cartItems');

      final repository = _ref.read(paymentRepositoryProvider);
      final result = await repository.createCartPaymentIntentWithItems(
        cartItems: cartItems,
      );

      result.fold(
        (failure) {
          state = state.copyWith(
            isLoading: false,
            error: failure.message,
            currentStep: NewCheckoutStep.paymentFailed,
            clearPaymentIntent: true,
            clearPaymentStatus: true,
          );
        },
        (paymentIntent) {
          debugPrint('[NewCheckout] ✅ NEW cart payment intent created successfully: ${paymentIntent.paymentIntentId}');
          debugPrint('[NewCheckout] Client secret: ${paymentIntent.clientSecret.substring(0, 20)}...');
          state = state.copyWith(
            isLoading: false,
            paymentIntent: paymentIntent,
            currentStep: NewCheckoutStep.paymentIntentCreated,
            isCartPayment: true,
            clearError: true,
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to create payment intent: $e',
        currentStep: NewCheckoutStep.paymentFailed,
        clearPaymentIntent: true,
        clearPaymentStatus: true,
      );
    }
  }

  /// Create payment intent for buy now
  /// Note: Voucher should be applied to buy now session before calling this
  Future<void> createBuyNowPaymentIntent({
    required String variantId,
    required int quantity,
    required double price,
    required String productName,
  }) async {
    // Clear any existing payment intent to ensure fresh checkout
    final oldPaymentIntentId = state.paymentIntent?.paymentIntentId;
    if (oldPaymentIntentId != null) {
      debugPrint('[NewCheckout] Clearing old payment intent: $oldPaymentIntentId');
    }

    state = state.copyWith(
      isLoading: true,
      currentStep: NewCheckoutStep.creatingPaymentIntent,
      clearError: true,
      clearPaymentIntent: true,
      clearPaymentStatus: true,
    );

    try {
      debugPrint('[NewCheckout] Creating NEW buy now payment intent (voucher should be pre-applied to session)');

      final repository = _ref.read(paymentRepositoryProvider);
      final result = await repository.createBuyNowPaymentIntent(
        variantId: variantId,
        quantity: quantity,
        price: price,
        productName: productName,
        voucherCode: null, // Voucher is handled by session, not passed directly
      );

      result.fold(
        (failure) {
          state = state.copyWith(
            isLoading: false,
            error: failure.message,
            currentStep: NewCheckoutStep.paymentFailed,
            clearPaymentIntent: true,
            clearPaymentStatus: true,
          );
        },
        (paymentIntent) {
          debugPrint('[NewCheckout] ✅ NEW buy now payment intent created successfully: ${paymentIntent.paymentIntentId}');
          debugPrint('[NewCheckout] Client secret: ${paymentIntent.clientSecret.substring(0, 20)}...');
          state = state.copyWith(
            isLoading: false,
            paymentIntent: paymentIntent,
            currentStep: NewCheckoutStep.paymentIntentCreated,
            isCartPayment: false,
            clearError: true,
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to create buy now payment intent: $e',
        currentStep: NewCheckoutStep.paymentFailed,
        clearPaymentIntent: true,
        clearPaymentStatus: true,
      );
    }
  }

  /// Confirm payment using Payment Element (CardField)
  Future<void> confirmPaymentWithElement(String clientSecret) async {
    if (state.paymentIntent == null) {
      state = state.copyWith(
        error: 'No payment intent available',
        currentStep: NewCheckoutStep.paymentFailed,
      );
      return;
    }

    state = state.copyWith(
      isLoading: true,
      error: null,
      currentStep: NewCheckoutStep.presentingPaymentSheet,
    );

    try {
      debugPrint('[NewCheckout] 🔧 Confirming payment with Payment Element...');
      debugPrint('[NewCheckout] Client secret: ${clientSecret.substring(0, 20)}...');
      debugPrint('[NewCheckout] Payment intent ID: ${state.paymentIntent!.paymentIntentId}');

      // Create payment method from CardField and confirm payment
      final paymentIntent = await stripe.Stripe.instance.confirmPayment(
        paymentIntentClientSecret: clientSecret,
        data: const stripe.PaymentMethodParams.card(
          paymentMethodData: stripe.PaymentMethodData(),
        ),
      );

      debugPrint('[NewCheckout] ✅ Payment confirmed with Payment Element!');
      debugPrint('[NewCheckout] 🎉 Payment status: ${paymentIntent.status}');

      state = state.copyWith(
        isLoading: false,
        currentStep: NewCheckoutStep.paymentSheetCompleted, // Reusing existing step
      );

      // Start polling for payment status
      await pollPaymentStatus();

    } on stripe.StripeException catch (e) {
      debugPrint('[NewCheckout] ❌ Stripe exception: ${e.error}');
      debugPrint('[NewCheckout] Error code: ${e.error.code}');
      debugPrint('[NewCheckout] Error message: ${e.error.message}');
      debugPrint('[NewCheckout] Error type: ${e.error.type}');

      String errorMessage;
      if (e.error.code == stripe.FailureCode.Canceled) {
        debugPrint('[NewCheckout] User canceled payment');
        errorMessage = 'Thanh toán đã bị hủy';
      } else if (e.error.code == stripe.FailureCode.Failed) {
        debugPrint('[NewCheckout] Payment failed: ${e.error.message}');
        // Use localized message for better user experience
        errorMessage = e.error.localizedMessage ?? e.error.message ?? 'Thanh toán thất bại';
      } else {
        debugPrint('[NewCheckout] Other Stripe error: ${e.error.message}');
        errorMessage = e.error.localizedMessage ?? e.error.message ?? 'Lỗi thanh toán không xác định';
      }

      state = state.copyWith(
        isLoading: false,
        error: errorMessage,
        currentStep: NewCheckoutStep.paymentFailed,
      );
    } catch (e, stackTrace) {
      debugPrint('[NewCheckout] ❌ Unexpected error during payment confirmation: $e');
      debugPrint('[NewCheckout] Stack trace: $stackTrace');
      state = state.copyWith(
        isLoading: false,
        error: 'Payment processing failed: $e',
        currentStep: NewCheckoutStep.paymentFailed,
      );
    }
  }

  /// Poll payment status from backend  
  Future<void> pollPaymentStatus() async {
    if (state.paymentIntent == null) return;

    state = state.copyWith(
      isLoading: true,
      currentStep: NewCheckoutStep.checkingPaymentStatus,
    );

    try {
      final pollingService = _ref.read(paymentPollingServiceProvider);
      final paymentStatus = await pollingService.pollPaymentStatus(
        state.paymentIntent!.paymentIntentId,
        isCartPayment: state.isCartPayment,
      );

      state = state.copyWith(
        isLoading: false,
        paymentStatus: paymentStatus,
        currentStep: paymentStatus.isSuccessful 
            ? NewCheckoutStep.paymentCompleted 
            : NewCheckoutStep.paymentFailed,
      );

      if (!paymentStatus.isSuccessful) {
        state = state.copyWith(
          error: 'Payment ${paymentStatus.status}',
        );
      }

    } catch (e) {
      debugPrint('[NewCheckout] Error polling payment status: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to verify payment status: $e',
        currentStep: NewCheckoutStep.paymentFailed,
      );
    }
  }

  /// Set existing payment intent to avoid duplicate API calls
  void setExistingPaymentIntent({
    required String paymentIntentId,
    required String clientSecret,
    required String orderId,
  }) {
    debugPrint('[NewCheckout] Setting existing payment intent: $paymentIntentId');
    debugPrint('[NewCheckout] Client secret: ${clientSecret.substring(0, 20)}...');
    debugPrint('[NewCheckout] Order ID: $orderId');

    final paymentIntent = PaymentIntent(
      paymentIntentId: paymentIntentId,
      clientSecret: clientSecret,
      orderId: orderId,
      amount: 0.0, // Amount not needed for existing payment intent
      currency: 'USD', // Default currency
    );

    state = state.copyWith(
      isLoading: false,
      paymentIntent: paymentIntent,
      currentStep: NewCheckoutStep.paymentIntentCreated,
      isCartPayment: false, // This is a buy now payment
      clearError: true,
    );
  }

  /// Reset checkout state - clears all previous data including payment intent
  void reset() {
    debugPrint('[NewCheckout] Resetting checkout state - clearing all previous data');
    state = state.copyWith(
      isLoading: false,
      currentStep: NewCheckoutStep.initial,
      clearError: true,
      clearPaymentIntent: true,
      clearPaymentStatus: true,
    );
  }
}
