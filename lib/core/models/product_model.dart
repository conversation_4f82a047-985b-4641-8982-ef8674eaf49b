class ProductsResponse {
  final ProductsConnection products;

  ProductsResponse({required this.products});

  factory ProductsResponse.fromJson(Map<String, dynamic> json) {
    return ProductsResponse(
      products: ProductsConnection.fromJson(json['products']),
    );
  }
}

class ProductsConnection {
  final PageInfo pageInfo;
  final List<ProductEdge> edges;

  ProductsConnection({required this.pageInfo, required this.edges});

  factory ProductsConnection.fromJson(Map<String, dynamic> json) {
    return ProductsConnection(
      pageInfo: PageInfo.fromJson(json['pageInfo']),
      edges:
          (json['edges'] as List)
              .map((edge) => ProductEdge.fromJson(edge))
              .toList(),
    );
  }
}

class PageInfo {
  final bool hasNextPage;
  final String? endCursor;

  PageInfo({required this.hasNextPage, this.endCursor});

  factory PageInfo.fromJson(Map<String, dynamic> json) {
    return PageInfo(
      hasNextPage: json['hasNextPage'],
      endCursor: json['endCursor'],
    );
  }
}

class ProductEdge {
  final Product node;

  ProductEdge({required this.node});

  factory ProductEdge.fromJson(Map<String, dynamic> json) {
    return ProductEdge(node: Product.fromJson(json['node']));
  }
}

class Product {
  final String id;
  final String title;
  final String handle;
  final String? description;
  final String? descriptionHtml;
  final FeaturedImage? featuredImage;
  final VariantsConnection variants;
  final String tags;
  final List<Metafield> metafields;

  Product({
    required this.id,
    required this.title,
    required this.handle,
    this.description,
    this.descriptionHtml,
    this.featuredImage,
    required this.variants,
    this.tags = '',
    this.metafields = const [],
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    // Handle metafields as direct array (new structure) or as connection (old structure)
    List<Metafield> metafieldsList = [];
    if (json['metafields'] != null) {
      if (json['metafields'] is List) {
        // New structure: direct array of metafields
        final metafieldsArray = json['metafields'] as List;
        // debugPrint('🔍 Product ${json['title']}: Processing ${metafieldsArray.length} metafields');

        // Filter out null values and only process valid metafield objects
        final validMetafields = metafieldsArray
            .where((metafield) => metafield != null && metafield is Map<String, dynamic>)
            .toList();

        // debugPrint('🔍 Product ${json['title']}: Found ${validMetafields.length} valid metafields out of ${metafieldsArray.length}');

        // Process metafields and convert metaobject references to simple key-value pairs
        for (final metafieldData in validMetafields) {
          final metafield = metafieldData as Map<String, dynamic>;

          if (metafield['type'] == 'metaobject_reference' && metafield['reference'] != null) {
            // Handle metaobject reference
            final reference = metafield['reference'] as Map<String, dynamic>;
            final fields = reference['fields'] as List?;

            // Extract code from fields
            String? code;
            if (fields != null) {
              for (final field in fields) {
                if (field['key'] == 'code') {
                  code = field['value'] as String?;
                  break;
                }
              }
            }

            if (code != null) {
              // Convert to simple metafield format
              if (metafield['key'] == 'travel_region') {
                metafieldsList.add(Metafield(
                  namespace: 'travel',
                  key: 'region',
                  value: code,
                ));
                // debugPrint('🔍 Product ${json['title']}: Added region metafield: $code');
              } else if (metafield['key'] == 'travel_country') {
                metafieldsList.add(Metafield(
                  namespace: 'travel',
                  key: 'country',
                  value: code,
                ));
                // debugPrint('🔍 Product ${json['title']}: Added country metafield: $code');
              }
            }
          } else {
            // Handle regular metafields
            try {
              metafieldsList.add(Metafield.fromJson(metafield));
            } catch (e) {
              // debugPrint('🔍 Product ${json['title']}: Error parsing metafield: $e');
            }
          }
        }
      } else if (json['metafields'] is Map && json['metafields']['edges'] != null) {
        // Old structure: MetafieldsConnection with edges
        final connection = MetafieldsConnection.fromJson(json['metafields']);
        metafieldsList = connection.edges.map((edge) => edge.node).toList();
      }
    }

    // Handle tags - Shopify returns tags as List<String>, but we store as comma-separated string
    String tagsString = '';
    if (json['tags'] != null) {
      if (json['tags'] is List) {
        // New GraphQL API returns tags as List<String>
        final tagsList = json['tags'] as List;
        tagsString = tagsList.map((tag) => tag.toString()).join(', ');
      } else if (json['tags'] is String) {
        // Fallback for string format
        tagsString = json['tags'] as String;
      }
    }

    // Debug logging for description fields
    // debugPrint('🔍 Product ${json['title']}: description = "${json['description']}"');
    // debugPrint('🔍 Product ${json['title']}: descriptionHtml = "${json['descriptionHtml']}"');

    return Product(
      id: json['id'],
      title: json['title'],
      handle: json['handle'],
      description: json['description'],
      descriptionHtml: json['descriptionHtml'],
      featuredImage:
          json['featuredImage'] != null
              ? FeaturedImage.fromJson(json['featuredImage'])
              : null,
      variants: VariantsConnection.fromJson(json['variants']),
      tags: tagsString,
      metafields: metafieldsList,
    );
  }

  // Helper methods to get region and country from metafields (prioritized) or tags (fallback)
  String? get region {
    // First check metafields (new approach)
    for (final metafield in metafields) {
      if (metafield.namespace == 'travel' && metafield.key == 'region') {
        return metafield.value;
      }
    }

    // Fallback to tags (legacy approach)
    if (tags.isNotEmpty) {
      final tagList = tags.split(',').map((tag) => tag.trim()).toList();
      for (final tag in tagList) {
        if (tag.startsWith('region-')) {
          return tag.substring(7);
        }
      }
    }

    return null;
  }

  String? get country {
    // First check metafields (new approach)
    for (final metafield in metafields) {
      if (metafield.namespace == 'travel' && metafield.key == 'country') {
        return metafield.value;
      }
    }

    // Fallback to tags (legacy approach)
    if (tags.isNotEmpty) {
      final tagList = tags.split(',').map((tag) => tag.trim()).toList();
      for (final tag in tagList) {
        if (tag.startsWith('country-')) {
          return tag.substring(8);
        }
      }
    }

    return null;
  }
}

class FeaturedImage {
  final String url;
  final String? altText;

  FeaturedImage({required this.url, this.altText});

  factory FeaturedImage.fromJson(Map<String, dynamic> json) {
    return FeaturedImage(url: json['url'], altText: json['altText']);
  }
}

class VariantsConnection {
  final List<VariantEdge> edges;

  VariantsConnection({required this.edges});

  factory VariantsConnection.fromJson(Map<String, dynamic> json) {
    return VariantsConnection(
      edges:
          (json['edges'] as List)
              .map((edge) => VariantEdge.fromJson(edge))
              .toList(),
    );
  }
}

class VariantEdge {
  final Variant node;

  VariantEdge({required this.node});

  factory VariantEdge.fromJson(Map<String, dynamic> json) {
    return VariantEdge(node: Variant.fromJson(json['node']));
  }
}

class Variant {
  final String id;
  final String title;
  final MoneyV2 price;
  final MoneyV2? compareAtPrice;
  final List<SelectedOption> selectedOptions;
  final bool availableForSale;

  Variant({
    required this.id,
    required this.title,
    required this.price,
    this.compareAtPrice,
    required this.selectedOptions,
    required this.availableForSale,
  });

  factory Variant.fromJson(Map<String, dynamic> json) {
    return Variant(
      id: json['id'],
      title: json['title'] ?? '',
      price: MoneyV2.fromJson(json['price']),
      compareAtPrice: json['compareAtPrice'] != null
          ? MoneyV2.fromJson(json['compareAtPrice'])
          : null,
      selectedOptions: json['selectedOptions'] != null
          ? (json['selectedOptions'] as List)
              .map((option) => SelectedOption.fromJson(option))
              .toList()
          : [],
      availableForSale: json['availableForSale'] ?? true,
    );
  }

  /// Get a display name for this variant based on selected options
  String get displayName {
    if (selectedOptions.isEmpty) return title;

    final optionValues = selectedOptions.map((option) => option.value).join(' / ');
    return optionValues.isNotEmpty ? optionValues : title;
  }

  /// Get a specific option value by name
  String? getOptionValue(String optionName) {
    for (final option in selectedOptions) {
      if (option.name.toLowerCase() == optionName.toLowerCase()) {
        return option.value;
      }
    }
    return null;
  }
}



class SelectedOption {
  final String name;
  final String value;

  SelectedOption({required this.name, required this.value});

  factory SelectedOption.fromJson(Map<String, dynamic> json) {
    return SelectedOption(
      name: json['name'] ?? '',
      value: json['value'] ?? '',
    );
  }
}

class MoneyV2 {
  final String amount;
  final String currencyCode;

  MoneyV2({required this.amount, required this.currencyCode});

  factory MoneyV2.fromJson(Map<String, dynamic> json) {
    return MoneyV2(amount: json['amount'], currencyCode: json['currencyCode']);
  }
}

// Helper methods to easily access price information
extension ProductPriceExtension on Product {
  MoneyV2? get price {
    if (variants.edges.isEmpty) return null;
    final variant = variants.edges.first.node;
    return variant.price;
  }

  MoneyV2? get compareAtPrice {
    if (variants.edges.isEmpty) return null;
    final variant = variants.edges.first.node;
    return variant.compareAtPrice;
  }
}

class MetafieldsConnection {
  final List<MetafieldEdge> edges;

  MetafieldsConnection({required this.edges});

  factory MetafieldsConnection.fromJson(Map<String, dynamic> json) {
    return MetafieldsConnection(
      edges:
          (json['edges'] as List)
              .map((edge) => MetafieldEdge.fromJson(edge))
              .toList(),
    );
  }
}

class MetafieldEdge {
  final Metafield node;

  MetafieldEdge({required this.node});

  factory MetafieldEdge.fromJson(Map<String, dynamic> json) {
    return MetafieldEdge(node: Metafield.fromJson(json['node']));
  }
}

class Metafield {
  final String key;
  final String value;
  final String namespace;

  Metafield({required this.key, required this.value, required this.namespace});

  factory Metafield.fromJson(Map<String, dynamic> json) {
    return Metafield(
      key: json['key'],
      value: json['value'],
      namespace: json['namespace'],
    );
  }
}
