# TravelGator Client Helper Library

A lightweight client helper library that simplifies interactions with the TravelGator backend API, providing clean abstractions for cart operations with built-in debouncing, error handling, and performance optimizations.

## Features

- ✅ **Modern API Integration** - Uses the new PUT /api/cart endpoint exclusively
- ✅ **Automatic Authentication** - JWT token management with auto-refresh
- ✅ **Performance Optimized** - Debouncing, caching, and bulk operations
- ✅ **Type Safe** - Comprehensive TypeScript-like models and error handling
- ✅ **Error Recovery** - Intelligent retry logic and specific error codes
- ✅ **Backward Compatible** - Seamless integration with existing cart system

## Quick Start

### Basic Setup

```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:your_app/core/client/performance/cart_manager.dart';

class CartScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cartManager = ref.watch(cartManagerProvider);
    
    return Scaffold(
      body: FutureBuilder(
        future: cartManager.getCart(),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            return CartView(cart: snapshot.data!);
          }
          return CircularProgressIndicator();
        },
      ),
    );
  }
}
```

### Cart Operations

```dart
final cartManager = ref.read(cartManagerProvider);

// Get cart
final cart = await cartManager.getCart();

// Add item
await cartManager.scheduleUpdate(items: [
  CartItemUpdate(variantId: 'item_123', quantity: 2),
]);

// Apply voucher
await cartManager.applyVoucher('SAVE20');

// Set payment method
await cartManager.setPaymentMethod('stripe');

// Bulk operations
await cartManager.batchItemOperations(
  itemsToRemove: ['item_1'],
  itemsToSaveForLater: ['item_2'],
  quantityUpdates: {'item_3': 5},
);
```

## Architecture

### Core Components

1. **TravelGatorClient** - Main HTTP client with authentication and retry logic
2. **CartOperations** - Simplified cart operation methods
3. **CartManager** - High-performance manager with debouncing and caching
4. **AuthProvider** - JWT token management and refresh
5. **ErrorHandler** - Comprehensive error handling with specific error codes

### Data Flow

```
UI Layer → CartManager → CartOperations → TravelGatorClient → API
                ↓
            Debouncing & Caching
```

## Usage Examples

### Simple Cart Updates

```dart
// Update item quantity
await cartManager.updateQuantity('item_123', 3);

// Remove item
await cartManager.updateQuantity('item_123', 0);

// Save for later
await cartManager.scheduleUpdate(items: [
  CartItemUpdate(variantId: 'item_123', quantity: 1, selected: false),
]);
```

### Advanced Operations

```dart
// Comprehensive update
await cartManager.scheduleUpdate(
  items: [
    CartItemUpdate(variantId: 'item_1', quantity: 2),
    CartItemUpdate(variantId: 'item_2', quantity: 0), // Remove
    CartItemUpdate(variantId: 'item_3', quantity: 1, selected: false), // Save for later
  ],
  voucherCode: 'WELCOME10',
  paymentMethod: 'stripe',
);

// Batch operations
await cartManager.batchItemOperations(
  itemsToRemove: ['old_item_1', 'old_item_2'],
  itemsToMoveToCart: ['saved_item_1'],
  quantityUpdates: {
    'item_1': 3,
    'item_2': 1,
  },
);
```

### Error Handling

```dart
try {
  await cartManager.applyVoucher('INVALID_CODE');
} on ClientException catch (e) {
  switch (e.errorCode) {
    case 'VOUCHER_INVALID':
      showError('Invalid voucher code');
      break;
    case 'VOUCHER_EXPIRED':
      showError('This voucher has expired');
      break;
    default:
      showError('Failed to apply voucher: ${e.message}');
  }
}
```

### Performance Features

```dart
// Debounced updates (multiple calls within 300ms are batched)
cartManager.updateQuantity('item_1', 2);
cartManager.updateQuantity('item_2', 3);
cartManager.applyVoucher('SAVE20');
// Only one API call is made with all updates

// Cached responses
final cart1 = await cartManager.getCart(); // API call
final cart2 = await cartManager.getCart(); // Returns cached result

// Force refresh
final freshCart = await cartManager.getCart(forceRefresh: true);
```

## Integration with Existing Code

### Repository Integration

```dart
// Replace existing cart repository
final cartRepositoryProvider = Provider<CartRepository>((ref) {
  return ref.watch(enhancedCartRepositoryProvider);
});
```

### Provider Updates

```dart
class CartNotifier extends StateNotifier<CartState> {
  final CartManager _cartManager;
  
  CartNotifier(this._cartManager) : super(CartState.initial()) {
    // Set up callbacks
    _cartManager.onCartUpdated = (cart) {
      state = state.copyWith(cart: _convertToEntity(cart));
    };
    
    _cartManager.onError = (error) {
      state = state.copyWith(error: error.message);
    };
  }
  
  Future<void> loadCart() async {
    state = state.copyWith(isLoading: true);
    try {
      final cart = await _cartManager.getCart();
      // Cart is automatically updated via callback
    } catch (e) {
      // Error is automatically handled via callback
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }
}
```

## Configuration

### Client Configuration

```dart
final customClientProvider = Provider<TravelGatorClient>((ref) {
  final authInterceptor = ref.watch(authInterceptorProvider);
  
  return TravelGatorClient(
    authInterceptor: authInterceptor,
    config: ClientConfig(
      baseUrl: 'https://api.travelgator.com/api',
      debounceDelay: Duration(milliseconds: 500), // Custom debounce
      maxRetries: 5, // Custom retry count
      cacheExpiry: Duration(minutes: 5), // Custom cache duration
      enableLogging: kDebugMode,
    ),
  );
});
```

### Manager Configuration

```dart
final customCartManagerProvider = Provider<CartManager>((ref) {
  final client = ref.watch(customClientProvider);
  final cartOps = CartOperations(client);
  
  return CartManager(
    cartOperations: cartOps,
    debounceDelay: Duration(milliseconds: 500),
    cacheExpiry: Duration(minutes: 2),
  );
});
```

## Error Codes

| Code | Description | User Action |
|------|-------------|-------------|
| `VOUCHER_INVALID` | Invalid voucher code | Check code and try again |
| `VOUCHER_EXPIRED` | Voucher has expired | Use a different voucher |
| `INVALID_PAYMENT_METHOD` | Invalid payment method | Select valid payment method |
| `INVALID_QUANTITY` | Invalid quantity value | Enter positive number |
| `ITEM_NOT_FOUND` | Item not in cart | Refresh cart |
| `INSUFFICIENT_STOCK` | Not enough stock | Reduce quantity |

## Best Practices

1. **Use CartManager** for all cart operations instead of direct client calls
2. **Handle errors gracefully** with specific error codes
3. **Leverage debouncing** for rapid user interactions
4. **Use bulk operations** for multiple item updates
5. **Cache responses** to improve performance
6. **Set up callbacks** for automatic UI updates

## Migration Guide

### From Legacy Cart Repository

```dart
// Old way
await cartRepository.addItem(
  variantId: 'item_123',
  productId: 'product_456',
  quantity: 2,
  price: 29.99,
  title: 'Product Title',
  currency: 'USD',
);

// New way
await cartManager.scheduleUpdate(items: [
  CartItemUpdate(
    variantId: 'item_123',
    productId: 'product_456',
    quantity: 2,
  ),
]);
```

### From Multiple API Calls

```dart
// Old way (multiple API calls)
await cartRepository.updateItem(variantId: 'item_1', quantity: 2);
await cartRepository.updateItem(variantId: 'item_2', quantity: 3);
await cartRepository.applyVoucher(voucherCode: 'SAVE20');

// New way (single API call)
await cartManager.scheduleUpdate(
  items: [
    CartItemUpdate(variantId: 'item_1', quantity: 2),
    CartItemUpdate(variantId: 'item_2', quantity: 3),
  ],
  voucherCode: 'SAVE20',
);
```

## Testing

```dart
// Mock cart manager for testing
final mockCartManager = MockCartManager();
when(mockCartManager.getCart()).thenAnswer((_) async => mockCartResponse);

// Test with provider override
testWidgets('Cart screen test', (tester) async {
  await tester.pumpWidget(
    ProviderScope(
      overrides: [
        cartManagerProvider.overrideWithValue(mockCartManager),
      ],
      child: CartScreen(),
    ),
  );
});
```
