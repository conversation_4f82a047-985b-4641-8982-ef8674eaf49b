import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/order.dart';

part 'order_model.g.dart';

/// Order model for API responses
@JsonSerializable()
class OrderModel {
  final String id;
  @Json<PERSON>ey(name: 'total_price', defaultValue: 0.0)
  final double totalPrice;
  @Json<PERSON><PERSON>(name: 'original_total')
  final double? originalTotal;
  @Json<PERSON>ey(defaultValue: 'USD')
  final String currency;
  @JsonKey(defaultValue: 'pending')
  final String status;
  @JsonKey(name: 'payment_status', defaultValue: 'pending')
  final String paymentStatus;
  @<PERSON>son<PERSON><PERSON>(name: 'voucher_code')
  final String? voucherCode;
  @JsonKey(name: 'discount_amount', defaultValue: 0.0)
  final double discountAmount;
  @JsonKey(defaultValue: <OrderItemModel>[])
  final List<OrderItemModel> items;
  @JsonKey(name: 'created_at')
  final String createdAt;

  const OrderModel({
    required this.id,
    required this.totalPrice,
    this.originalTotal,
    required this.currency,
    required this.status,
    required this.paymentStatus,
    this.voucherCode,
    required this.discountAmount,
    required this.items,
    required this.createdAt,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) =>
      _$OrderModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrderModelToJson(this);

  /// Convert to domain entity
  Order toDomain() {
    return Order(
      id: id,
      totalPrice: totalPrice,
      originalTotal: originalTotal,
      currency: currency,
      status: _mapToOrderStatus(),
      voucherCode: voucherCode,
      discountAmount: discountAmount,
      items: items.map((item) => item.toDomain()).toList(),
      createdAt: DateTime.parse(createdAt),
    );
  }

  /// Map API status and payment_status to OrderStatus
  OrderStatus _mapToOrderStatus() {
    // Check payment status first for more accurate mapping
    switch (paymentStatus.toLowerCase()) {
      case 'succeeded':
        return OrderStatus.confirmed;
      case 'failed':
        return OrderStatus.failed;
      case 'canceled':
      case 'cancelled':
        return OrderStatus.cancelled;
      case 'pending':
      case 'requires_payment_method':
      case 'requires_action':
        return OrderStatus.pending;
      default:
        // Fallback to order status
        switch (status.toLowerCase()) {
          case 'completed':
          case 'confirmed':
            return OrderStatus.confirmed;
          case 'failed':
            return OrderStatus.failed;
          case 'cancelled':
          case 'canceled':
            return OrderStatus.cancelled;
          case 'pending':
          default:
            return OrderStatus.pending;
        }
    }
  }
}

/// Order item model for API responses
@JsonSerializable()
class OrderItemModel {
  @JsonKey(name: 'variant_id', defaultValue: '')
  final String variantId;
  @JsonKey(defaultValue: 1)
  final int quantity;
  @JsonKey(defaultValue: 0.0)
  final double price;
  @JsonKey(defaultValue: 'Unknown Product')
  final String title;
  @JsonKey(name: 'image_url')
  final String? imageUrl;

  const OrderItemModel({
    required this.variantId,
    required this.quantity,
    required this.price,
    required this.title,
    this.imageUrl,
  });

  factory OrderItemModel.fromJson(Map<String, dynamic> json) =>
      _$OrderItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrderItemModelToJson(this);

  /// Convert to domain entity
  OrderItem toDomain() {
    return OrderItem(
      variantId: variantId,
      quantity: quantity,
      price: price,
      title: title,
      imageUrl: imageUrl,
    );
  }
}

/// API response wrapper for orders
@JsonSerializable()
class OrdersResponse {
  @JsonKey(defaultValue: true)
  final bool success;
  final List<OrderModel> data;

  const OrdersResponse({
    required this.success,
    required this.data,
  });

  factory OrdersResponse.fromJson(Map<String, dynamic> json) {
    try {
      return _$OrdersResponseFromJson(json);
    } catch (e) {
      // If the response doesn't have the expected structure,
      // try to parse it as a direct array
      if (json['data'] is List) {
        return OrdersResponse(
          success: json['success'] ?? true,
          data: (json['data'] as List)
              .map((item) => OrderModel.fromJson(item as Map<String, dynamic>))
              .toList(),
        );
      } else if (json is List) {
        // If the response is directly an array
        return OrdersResponse(
          success: true,
          data: (json as List)
              .map((item) => OrderModel.fromJson(item as Map<String, dynamic>))
              .toList(),
        );
      }
      rethrow;
    }
  }

  Map<String, dynamic> toJson() => _$OrdersResponseToJson(this);

  /// Convert to domain entities
  List<Order> toDomain() {
    return data.map((order) => order.toDomain()).toList();
  }
}

/// API response wrapper for single order
@JsonSerializable()
class OrderResponse {
  @JsonKey(defaultValue: true)
  final bool success;
  final OrderModel data;

  const OrderResponse({
    required this.success,
    required this.data,
  });

  factory OrderResponse.fromJson(Map<String, dynamic> json) {
    try {
      return _$OrderResponseFromJson(json);
    } catch (e) {
      // If the response doesn't have the expected structure,
      // try to parse it as a direct object
      if (json['data'] is Map<String, dynamic>) {
        return OrderResponse(
          success: json['success'] ?? true,
          data: OrderModel.fromJson(json['data'] as Map<String, dynamic>),
        );
      } else if (json.containsKey('id')) {
        // If the response is directly an order object
        return OrderResponse(
          success: true,
          data: OrderModel.fromJson(json),
        );
      }
      rethrow;
    }
  }

  Map<String, dynamic> toJson() => _$OrderResponseToJson(this);

  /// Convert to domain entity
  Order toDomain() {
    return data.toDomain();
  }
}
