# Unified Checkout Implementation Verification

## Overview
This document outlines test scenarios to verify that the Flutter client has been successfully updated to use the unified `/cart/checkout` endpoint.

## Changes Made

### 1. Repository Updates ✅
- **File**: `lib/features/cart/data/repositories/cart_repository_impl.dart`
- **Changes**:
  - `checkout()` method now sends empty body to `/cart/checkout`
  - `checkoutSelected()` method now sends `selected_variant_ids` to `/cart/checkout`
  - `checkoutSelectedWithQuantities()` method now sends `items` array to `/cart/checkout`
  - All methods use unified endpoint with proper priority logic

### 2. Entity Updates ✅
- **File**: `lib/features/cart/domain/entities/cart.dart`
- **Changes**:
  - Added `selected` field to `CartItem` entity
  - Added selection metadata fields to `Cart` entity:
    - `selectedItemsCount`
    - `selectedTotalPrice`
    - `allItemsSelected`
    - `anyItemsSelected`
  - Updated `copyWith` methods to include new fields
  - Regenerated JSON serialization code

### 3. New Service Created ✅
- **File**: `lib/features/cart/presentation/services/unified_cart_service.dart`
- **Features**:
  - Smart checkout with automatic priority detection
  - Validation methods
  - Selection state helpers
  - Modern API following client implementation guide

### 4. Documentation Updated ✅
- **File**: `docs/client_implementation_guide.md`
- **Updates**:
  - Added migration section
  - Updated status to "SYNCED WITH BACKEND"
  - Added examples of new unified approach
  - Included backward compatibility notes

## Test Scenarios

### Scenario 1: Checkout All Items
**Purpose**: Verify empty body triggers "checkout all" logic

**Steps**:
1. Add multiple items to cart
2. Call `cartService.checkoutAll()`
3. Verify request goes to `POST /cart/checkout` with empty body `{}`
4. Verify response contains checkout URL

**Expected API Call**:
```
POST /cart/checkout
Authorization: Bearer <token>
Content-Type: application/json

{}
```

### Scenario 2: Checkout Selected Variants
**Purpose**: Verify selected variant IDs are sent correctly

**Steps**:
1. Add multiple items to cart
2. Select specific items (variant IDs: ["12345", "67890"])
3. Call `cartService.checkoutSelectedVariants(["12345", "67890"])`
4. Verify request contains `selected_variant_ids` array

**Expected API Call**:
```
POST /cart/checkout
Authorization: Bearer <token>
Content-Type: application/json

{
  "selected_variant_ids": ["12345", "67890"]
}
```

### Scenario 3: Checkout Custom Quantities
**Purpose**: Verify items array with custom quantities

**Steps**:
1. Add multiple items to cart
2. Create custom checkout items with specific quantities
3. Call `cartService.checkoutWithCustomQuantities(items)`
4. Verify request contains `items` array with variant_id and quantity

**Expected API Call**:
```
POST /cart/checkout
Authorization: Bearer <token>
Content-Type: application/json

{
  "items": [
    {"variant_id": "12345", "quantity": 1},
    {"variant_id": "67890", "quantity": 2}
  ]
}
```

### Scenario 4: Smart Checkout Priority Logic
**Purpose**: Verify automatic priority selection works

**Test Cases**:

#### Case A: Custom Items (Priority 1)
```dart
final result = await cartService.smartCheckout(
  customItems: [CheckoutItem(variantId: "12345", quantity: 1)],
  selectedVariantIds: ["67890"], // Should be ignored
);
// Should use items array (Priority 1)
```

#### Case B: Selected Variants (Priority 2)
```dart
final result = await cartService.smartCheckout(
  selectedVariantIds: ["12345", "67890"],
);
// Should use selected_variant_ids (Priority 2)
```

#### Case C: Fallback to All (Priority 5)
```dart
final result = await cartService.smartCheckout();
// Should use empty body (Priority 5)
```

### Scenario 5: Enhanced Response Handling
**Purpose**: Verify new response format is handled correctly

**Steps**:
1. Make any checkout request
2. Verify response parsing handles new fields:
   - `stripe_session_id`
   - `expires_at`
   - `selected_items` array
3. Verify backward compatibility with old response format

**Expected Response**:
```json
{
  "success": true,
  "data": {
    "checkout_url": "https://checkout.stripe.com/pay/cs_xxx",
    "order_id": "order_123",
    "stripe_session_id": "cs_xxx",
    "total_amount": 89.97,
    "currency": "USD",
    "expires_at": "2024-01-15T10:30:00Z",
    "selected_items": [...]
  }
}
```

### Scenario 6: Selection Metadata
**Purpose**: Verify cart response includes selection data

**Steps**:
1. Get cart with `GET /cart`
2. Verify response includes selection metadata
3. Test UnifiedCartService helper methods:
   - `getSelectedVariantIds()`
   - `hasSelectedItems()`
   - `getSelectedTotalPrice()`
   - `areAllItemsSelected()`

**Expected Cart Response**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "variant_id": "12345",
        "selected": true,
        ...
      }
    ],
    "selected_items_count": 2,
    "selected_total_price": 59.98,
    "all_items_selected": false,
    "any_items_selected": true,
    ...
  }
}
```

## Manual Testing Steps

### 1. Network Inspection
1. Open Flutter app with network debugging enabled
2. Add items to cart
3. Attempt checkout with different methods
4. Verify all requests go to `/cart/checkout` endpoint
5. Verify request payloads match expected format

### 2. UI Testing
1. Test existing cart screen functionality
2. Verify checkout buttons still work
3. Test item selection/deselection
4. Verify totals update correctly
5. Test error handling for validation failures

### 3. Integration Testing
1. Complete full checkout flow
2. Verify Stripe checkout URL is received
3. Test payment completion
4. Verify cart updates after successful payment

## Debugging Commands

### Check Current Implementation
```bash
# Verify repository methods
grep -n "POST.*cart" lib/features/cart/data/repositories/cart_repository_impl.dart

# Check entity fields
grep -n "selected" lib/features/cart/domain/entities/cart.dart

# Verify service exists
ls -la lib/features/cart/presentation/services/unified_cart_service.dart
```

### Test API Calls
```bash
# Test checkout all
curl -X POST "https://your-api.com/api/cart/checkout" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{}'

# Test selected variants
curl -X POST "https://your-api.com/api/cart/checkout" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"selected_variant_ids": ["12345"]}'

# Test custom quantities
curl -X POST "https://your-api.com/api/cart/checkout" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"items": [{"variant_id": "12345", "quantity": 1}]}'
```

## Success Criteria

- [ ] All checkout methods use `/cart/checkout` endpoint
- [ ] Request payloads match unified API specification
- [ ] Response parsing handles enhanced format
- [ ] Existing UI functionality remains intact
- [ ] New UnifiedCartService works correctly
- [ ] Selection metadata is properly handled
- [ ] Backward compatibility is maintained
- [ ] Error handling works for all scenarios

## Next Steps

1. **Test the implementation** using the scenarios above
2. **Update cart UI** to use UnifiedCartService for better features
3. **Add selection management** endpoints if backend supports them
4. **Optimize performance** by leveraging selection metadata
5. **Add unit tests** for the new unified service

This verification ensures the client is properly synced with the backend's unified checkout implementation.
