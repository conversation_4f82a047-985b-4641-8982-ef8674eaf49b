import 'package:flutter/material.dart';
import '../../domain/entities/order.dart' as entities;

/// Widget for displaying pricing breakdown in transaction detail screen
class PricingBreakdown extends StatelessWidget {
  final entities.Order order;

  const PricingBreakdown({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity, // Full width - responsive
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // Use minimum space needed
        children: [
          _buildPricingContent(),
        ],
      ),
    );
  }

  Widget _buildPricingContent() {
    final subtotal = _calculateSubtotal();
    final credits = _calculateCredits();
    final promoDiscount = order.discountAmount;
    final finalAmount = order.totalPrice;

    return Container(
      width: double.infinity, // Make responsive instead of fixed width
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        mainAxisSize: MainAxisSize.min, // Use minimum space needed
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: double.infinity, // Make responsive instead of fixed width
            child: Text(
              _buildPricingText(subtotal, credits, promoDiscount, finalAmount),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1F2937),
                height: 1.21,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  String _buildPricingText(double subtotal, double credits, double promoDiscount, double finalAmount) {
    final buffer = StringBuffer();

    // Subtotal - match Figma format exactly
    buffer.writeln('\$${subtotal.toStringAsFixed(2)} (${order.currency})');

    // Credits (if any) - with proper spacing to match Figma
    if (credits > 0) {
      buffer.writeln('       Credits  (-\$${credits.toStringAsFixed(2)})');
    }

    // Promo discount (if any)
    if (promoDiscount > 0) {
      final promoCode = order.voucherCode ?? 'PROMO2';
      buffer.writeln('$promoCode(-\$${promoDiscount.toStringAsFixed(2)})');
    }

    // Final amount
    buffer.write('\$${finalAmount.toStringAsFixed(2)}');

    return buffer.toString();
  }

  double _calculateSubtotal() {
    // Calculate subtotal from order items
    double subtotal = 0;
    for (final item in order.items) {
      subtotal += item.totalPrice;
    }
    return subtotal;
  }

  double _calculateCredits() {
    // Get credits from order data if available
    // TODO: In real implementation, this should come from the order data
    // For now, return 0 to not show fake credits
    return 0.0;
  }
}
