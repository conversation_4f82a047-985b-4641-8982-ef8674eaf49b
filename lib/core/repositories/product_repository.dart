import 'package:flutter/foundation.dart';
import 'package:flutter_travelgator/features/home/<USER>/models/region.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import '../graphql/queries.dart';
import '../models/product_model.dart';
import '../../features/home/<USER>/models/country.dart';

class ProductRepository {
  final GraphQLClient client;

  ProductRepository({required this.client});

  Future<List<Product>> getProducts({
    int first = 10,
    String? query,
    String? after,
  }) async {
    try {
      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getProducts),
        variables: {'first': first, 'query': query, 'after': after},
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        // Handle different types of GraphQL exceptions gracefully
        final exception = result.exception!;
        if (exception.linkException != null) {
          final linkException = exception.linkException!;
          if (linkException.toString().contains('Failed host lookup') ||
              linkException.toString().contains('Operation timed out') ||
              linkException.toString().contains('No stream event')) {
            debugPrint('🔍 Repository: Network connectivity issue detected');
            throw Exception('Network connection error. Please check your internet connection.');
          }
        }
        throw Exception('Failed to load products. Please try again later.');
      }

      // Check if products data exists and is not null
      final productsData = result.data?['products'];
      if (productsData == null) {
        debugPrint('No products data returned for query: $query');
        return [];
      }

      final ProductsConnection productsConnection = ProductsConnection.fromJson(productsData);

      return productsConnection.edges.map((edge) => edge.node).toList();
    } on Exception catch (e) {
      // Catch any Exception (including ServerException)
      debugPrint('🔍 Repository: Caught exception: $e');
      if (e.toString().contains('Network connection error')) {
        rethrow; // Keep our custom network error message
      }
      // Return user-friendly message for any other exception
      throw Exception('Failed to load products. Please try again later.');
    } catch (e) {
      // Catch any other errors (non-Exception types)
      debugPrint('🔍 Repository: Caught error: $e');
      throw Exception('Failed to load products. Please try again later.');
    }
  }

  Future<ProductsConnection> getProductsWithPagination({
    int first = 10,
    String? query,
    String? after,
    FetchPolicy? fetchPolicy,
  }) async {
    try {
      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getProducts),
        variables: {'first': first, 'query': query, 'after': after},
        fetchPolicy: fetchPolicy ?? FetchPolicy.cacheFirst,
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        throw Exception(result.exception.toString());
      }

      // Check if products data exists and is not null
      final productsData = result.data?['products'];
      if (productsData == null) {
        debugPrint('No products data returned for query: $query');
        // Return empty ProductsConnection
        return ProductsConnection(
          pageInfo: PageInfo(hasNextPage: false, endCursor: null),
          edges: [],
        );
      }

      final productsConnection = ProductsConnection.fromJson(productsData);

      // debugPrint('🔍 Repository: Found ${productsConnection.edges.length} total products in getProductsWithPagination');

      // Log all product details for debugging
      // for (final product in allProducts) {
      //   debugPrint('🔍 Product: ${product.title}');
      //   debugPrint('🔍   Tags: "${product.tags}"');
      //   debugPrint('🔍   Metafields: ${product.metafields.length}');
      //   for (final metafield in product.metafields) {
      //     debugPrint('🔍     ${metafield.namespace}.${metafield.key} = "${metafield.value}"');
      //   }

      //   // Log all variants for this product
      //   debugPrint('🔍   Variants: ${product.variants.edges.length}');
      //   for (int i = 0; i < product.variants.edges.length; i++) {
      //     final variant = product.variants.edges[i].node;
      //     debugPrint('🔍     Variant $i: ${variant.title} - \$${variant.price.amount}');
      //     debugPrint('🔍       ID: ${variant.id}');
      //     debugPrint('🔍       Available: ${variant.availableForSale}');
      //     debugPrint('🔍       SelectedOptions: ${variant.selectedOptions.length}');
      //     for (int j = 0; j < variant.selectedOptions.length; j++) {
      //       final option = variant.selectedOptions[j];
      //       debugPrint('🔍         Option $j: ${option.name} = "${option.value}"');
      //     }
      //   }
      // }

      return productsConnection;
    } catch (e) {
      throw Exception('Failed to fetch products: $e');
    }
  }

  /// Fetch products filtered by region and/or country using tag-based filtering
  ///
  /// This method uses tag-based filtering which is currently working.
  /// Will be migrated to metafields once they are properly configured.
  Future<List<Product>> getProductsByRegionAndCountry({
    int first = 10,
    String? after,
    String? region,
    String? country,
  }) async {
    try {
      // Construct tag-based query string based on region and country
      List<String> queryParts = [];

      if (region != null && region.isNotEmpty) {
        queryParts.add("tag:region-$region");
      }

      if (country != null && country.isNotEmpty) {
        queryParts.add("tag:country-$country");
      }

      String queryString = queryParts.join(" AND ");

      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getProductsByRegionAndCountry),
        variables: {
          'first': first,
          'after': after,
          'query': queryString.isNotEmpty ? queryString : null,
        },
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        throw Exception(result.exception.toString());
      }

      // Check if products data exists and is not null
      final productsData = result.data?['products'];
      if (productsData == null) {
        debugPrint('No products data returned for query: $queryString');
        return [];
      }

      final ProductsConnection productsConnection = ProductsConnection.fromJson(productsData);

      return productsConnection.edges.map((edge) => edge.node).toList();
    } catch (e) {
      throw Exception('Failed to fetch products by region and country: $e');
    }
  }

  /// Fetch products with pagination, filtered by region and/or country using tag-based filtering
  Future<ProductsConnection> getProductsByRegionAndCountryWithPagination({
    int first = 10,
    String? after,
    String? region,
    String? country,
  }) async {
    try {
      // Get all products with metafields and tags, then filter client-side
      debugPrint('🔍 Repository: Fetching all products with metafields for filtering...');
      debugPrint('🔍 Repository: Filtering by region: $region, country: $country');

      QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getProductsByRegionAndCountry),
        variables: {
          'first': 50, // Get more products to inspect
          'after': after,
          'query': null, // Get all products to filter client-side
        },
      );

      QueryResult result = await client.query(options);

      if (result.hasException) {
        throw Exception(result.exception.toString());
      }

      // Check if products data exists and is not null
      final productsData = result.data?['products'];
      if (productsData == null) {
        debugPrint('🔍 Repository: No products data returned');
        return ProductsConnection(
          pageInfo: PageInfo(hasNextPage: false, endCursor: null),
          edges: [],
        );
      }

      final allProductsConnection = ProductsConnection.fromJson(productsData);
      final allProducts = allProductsConnection.edges.map((edge) => edge.node).toList();

      // debugPrint('🔍 Repository: Found ${allProducts.length} total products');

      // Log all product details for debugging
      // for (final product in allProducts) {
      //   debugPrint('🔍 Product: ${product.title}');
      //   debugPrint('🔍   Tags: "${product.tags}"');
      //   debugPrint('🔍   Metafields: ${product.metafields.length}');
      //   for (final metafield in product.metafields) {
      //     debugPrint('🔍     ${metafield.namespace}.${metafield.key} = "${metafield.value}"');
      //   }

      //   // Log all variants for this product
      //   debugPrint('🔍   Variants: ${product.variants.edges.length}');
      //   for (int i = 0; i < product.variants.edges.length; i++) {
      //     final variant = product.variants.edges[i].node;
      //     debugPrint('🔍     Variant $i: ${variant.title} - \$${variant.price.amount}');
      //     debugPrint('🔍       ID: ${variant.id}');
      //     debugPrint('🔍       Available: ${variant.availableForSale}');
      //     debugPrint('🔍       SelectedOptions: ${variant.selectedOptions.length}');
      //     for (int j = 0; j < variant.selectedOptions.length; j++) {
      //       final option = variant.selectedOptions[j];
      //       debugPrint('🔍         Option $j: ${option.name} = "${option.value}"');
      //     }
      //   }
      // }

      // Filter products based on region and/or country
      List<Product> filteredProducts = _filterProductsByRegionAndCountry(
        allProducts,
        region: region,
        country: country
      );

      // debugPrint('🔍 Repository: Found ${filteredProducts.length} products after filtering');

      // Create filtered connection with pagination
      final filteredEdges = filteredProducts.take(first).map((product) =>
        ProductEdge(node: product)
      ).toList();

      return ProductsConnection(
        pageInfo: PageInfo(
          hasNextPage: filteredProducts.length > first,
          endCursor: filteredEdges.isNotEmpty ? 'filtered_${filteredEdges.length}' : null,
        ),
        edges: filteredEdges,
      );
    } catch (e) {
      throw Exception('Failed to fetch products by region and country: $e');
    }
  }

  /// Fetch all available regions
  Future<List<Region>> getRegions() async {
    try {
      // debugPrint('🌏 Repository: Starting to fetch regions...');
      // debugPrint('🌏 Repository: Query string: ${GraphQLQueries.getRegions}');

      // Test direct query first
      await testDirectRegionsQuery();

      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getRegions),
        fetchPolicy: FetchPolicy.networkOnly, // Force network request, skip cache
      );

      final QueryResult result = await client.query(options);
      // debugPrint('🌏 Repository: GraphQL result received');
      // debugPrint('🌏 Repository: Has exception: ${result.hasException}');
      // debugPrint('🌏 Repository: Raw data: ${result.data}');

      if (result.hasException) {
        debugPrint('🌏 Repository: Exception: ${result.exception}');
        // Handle different types of GraphQL exceptions gracefully
        final exception = result.exception!;
        if (exception.linkException != null) {
          final linkException = exception.linkException!;
          if (linkException.toString().contains('Failed host lookup') ||
              linkException.toString().contains('Operation timed out') ||
              linkException.toString().contains('No stream event')) {
            debugPrint('🌏 Repository: Network connectivity issue detected');
            throw Exception('Network connection error. Please check your internet connection.');
          }
        }
        throw Exception('Failed to load regions. Please try again later.');
      }

      final metaobjectsData = result.data?['metaobjects'];
      // debugPrint('🌏 Repository: Metaobjects data: $metaobjectsData');

      final regions = metaobjectsData?['nodes'] as List? ?? [];
      // debugPrint('🌏 Repository: Found ${regions.length} region nodes');

      final parsedRegions = regions.map<Region>((node) {
        final nodeData = node as Map<String, dynamic>;
        final region = Region.fromShopifyMetaobject(nodeData);
        return region;
      }).toList();

      // debugPrint('🌏 Repository: Returning ${parsedRegions.length} regions');
      return parsedRegions;
    } on Exception catch (e) {
      // Catch any Exception (including ServerException)
      debugPrint('🌏 Repository: Caught exception: $e');
      if (e.toString().contains('Network connection error')) {
        rethrow; // Keep our custom network error message
      }
      // Return user-friendly message for any other exception
      throw Exception('Failed to load regions. Please try again later.');
    } catch (e) {
      // Catch any other errors (non-Exception types)
      debugPrint('🌏 Repository: Caught error: $e');
      throw Exception('Failed to load regions. Please try again later.');
    }
  }

  /// Fetch all available countries and convert to Country model
  Future<List<Country>> getCountries() async {
    try {
      // debugPrint('🌍 Repository: Starting to fetch countries...');
      // debugPrint('🌍 Repository: Query string: ${GraphQLQueries.getCountries}');
      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getCountries),
        fetchPolicy: FetchPolicy.networkOnly, // Force network request, skip cache
      );

      final QueryResult result = await client.query(options);
      // debugPrint('🌍 Repository: GraphQL result received');
      // debugPrint('🌍 Repository: Has exception: ${result.hasException}');
      // debugPrint('🌍 Repository: Raw data: ${result.data}');

      if (result.hasException) {
        debugPrint('🌍 Repository: Exception: ${result.exception}');
        // Handle different types of GraphQL exceptions gracefully
        final exception = result.exception!;
        if (exception.linkException != null) {
          final linkException = exception.linkException!;
          if (linkException.toString().contains('Failed host lookup') ||
              linkException.toString().contains('Operation timed out') ||
              linkException.toString().contains('No stream event')) {
            debugPrint('🌍 Repository: Network connectivity issue detected');
            throw Exception('Network connection error. Please check your internet connection.');
          }
        }
        throw Exception('Failed to load countries. Please try again later.');
      }

      // debugPrint('🌍 Repository: Metaobjects data: ${result.data?['metaobjects']}');

      // debugPrint('🌍 Repository: Found ${result.data?['metaobjects']?['nodes']?.length ?? 0} country nodes');

      // Use the static fromShopifyList method to parse the response
      final parsedCountries = Country.fromShopifyList(result.data!);
      // debugPrint('🌍 Repository: Returning ${parsedCountries.length} countries');
      return parsedCountries;
    } on Exception catch (e) {
      // Catch any Exception (including ServerException)
      debugPrint('🌍 Repository: Caught exception: $e');
      if (e.toString().contains('Network connection error')) {
        rethrow; // Keep our custom network error message
      }
      // Return user-friendly message for any other exception
      throw Exception('Failed to load countries. Please try again later.');
    } catch (e) {
      // Catch any other errors (non-Exception types)
      debugPrint('🌍 Repository: Caught error: $e');
      throw Exception('Failed to load countries. Please try again later.');
    }
  }

  /// Fetch products with price filter and pagination using client-side filtering
  Future<ProductsConnection> getProductsByPriceWithPagination({
    int first = 10,
    String? after,
    double? minPrice,
    double? maxPrice,
    String? region,
    String? country,
  }) async {
    try {
      debugPrint('🔍 Repository: Fetching products with price filter - min: $minPrice, max: $maxPrice, region: $region, country: $country');

      // Use client-side filtering since Shopify's price query syntax is unreliable
      if (region != null && region.isNotEmpty) {
        debugPrint('🔍 Repository: Will filter by region client-side: $region');
      }

      if (country != null && country.isNotEmpty) {
        debugPrint('🔍 Repository: Will filter by country client-side: $country');
      }

      // Get all products first, then filter client-side
      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getProductsByRegionAndCountry),
        variables: {
          'first': 50, // Get more products to filter from
          'after': after,
          'query': null, // Get all products
        },
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        throw Exception(result.exception.toString());
      }

      // Check if products data exists and is not null
      final productsData = result.data?['products'];
      if (productsData == null) {
        debugPrint('🔍 Repository: No products data returned');
        return ProductsConnection(
          pageInfo: PageInfo(hasNextPage: false, endCursor: null),
          edges: [],
        );
      }

      final productsConnection = ProductsConnection.fromJson(productsData);
      final allProducts = productsConnection.edges.map((edge) => edge.node).toList();

      debugPrint('🔍 Repository: Retrieved ${allProducts.length} products, now filtering...');

      // Apply client-side filtering
      List<Product> filteredProducts = allProducts;

      // Filter by region/country
      filteredProducts = _filterProductsByRegionAndCountry(
        filteredProducts,
        region: region,
        country: country,
      );

      // Filter by price range
      if (minPrice != null || maxPrice != null) {
        filteredProducts = filteredProducts.where((product) {
          if (product.price == null) return false;

          final price = double.tryParse(product.price!.amount);
          if (price == null) return false;

          bool matchesMin = minPrice == null || price >= minPrice;
          bool matchesMax = maxPrice == null || price <= maxPrice;

          final matches = matchesMin && matchesMax;
          // if (matches) {
          //   debugPrint('🔍 ✅ Product "${product.title}" matches price filter (\$${price.toStringAsFixed(2)})');
          // } else {
          //   debugPrint('🔍 ❌ Product "${product.title}" does not match price filter (\$${price.toStringAsFixed(2)}) - min: $minPrice, max: $maxPrice');
          // }

          return matches;
        }).toList();
      }

      debugPrint('🔍 Repository: Found ${filteredProducts.length} products after all filtering');

      // Convert back to ProductsConnection format
      final filteredEdges = filteredProducts.take(first).map((product) =>
        ProductEdge(node: product)
      ).toList();

      return ProductsConnection(
        pageInfo: PageInfo(
          hasNextPage: filteredProducts.length > first,
          endCursor: filteredProducts.length > first ? filteredEdges.last.node.id : null,
        ),
        edges: filteredEdges,
      );
    } catch (e) {
      debugPrint('🔍 Repository: Error in getProductsByPriceWithPagination: $e');
      throw Exception('Failed to fetch products by price: $e');
    }
  }

  /// Get minimum price for products by country using metafields with tag fallback
  Future<double?> getMinPriceByCountry(String countryCode) async {
    try {
      // debugPrint('🔍 Starting min price fetch for country: $countryCode');

      // Since metafields aren't properly configured yet, let's use tag-based filtering directly
      // and also try to get all products to filter them client-side

      // debugPrint('🔍 Trying tag-based filtering for country $countryCode');
      QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getProductsByRegionAndCountry),
        variables: {
          'first': 50,
          'query': "tag:country-$countryCode",
        },
      );

      QueryResult result = await client.query(options);
      // debugPrint('🔍 Tag query result for $countryCode: hasException=${result.hasException}');

      List<Product> products = [];

      if (!result.hasException && result.data?['products'] != null) {
        try {
          final productsConnection = ProductsConnection.fromJson(result.data!['products']);
          products = productsConnection.edges.map((edge) => edge.node).toList();
          // debugPrint('🔍 Found ${products.length} products using tag filtering for country $countryCode');
        } catch (parseError) {
          // debugPrint('🔍 Error parsing tag data: $parseError');
        }
      }

      // If no products found with tags, try getting all products and filter client-side
      if (products.isEmpty) {
        // debugPrint('🔍 No products found with tags, trying to get all products for client-side filtering');
        options = QueryOptions(
          document: gql(GraphQLQueries.getProductsByRegionAndCountry),
          variables: {
            'first': 50,
            'query': null, // Get all products
          },
        );

        result = await client.query(options);

        if (!result.hasException && result.data?['products'] != null) {
          try {
            final productsConnection = ProductsConnection.fromJson(result.data!['products']);
            final allProducts = productsConnection.edges.map((edge) => edge.node).toList();

            // debugPrint('🔍 All products found: ${allProducts.length}');
            // for (final product in allProducts) {
            //   debugPrint('🔍 Product: ${product.title}');
            //   debugPrint('🔍   Tags: "${product.tags}"');
            //   debugPrint('🔍   Metafields: ${product.metafields.length}');
            //   for (final metafield in product.metafields) {
            //     debugPrint('🔍     ${metafield.namespace}.${metafield.key} = ${metafield.value}');
            //   }
            // }

            // Filter client-side by checking tags or metafields
            products = allProducts.where((product) {
              // Check if product has the country in tags
              if (product.tags.contains('country-$countryCode')) {
                // debugPrint('🔍 Product ${product.title} matches country tag: country-$countryCode');
                return true;
              }

              // Check if product has the country in metafields
              for (final metafield in product.metafields) {
                if (metafield.namespace == 'travel' &&
                    metafield.key == 'country' &&
                    metafield.value == countryCode) {
                  // debugPrint('🔍 Product ${product.title} matches country metafield: $countryCode');
                  return true;
                }
              }

              // debugPrint('🔍 Product ${product.title} does not match country $countryCode');
              return false;
            }).toList();

            // debugPrint('🔍 Found ${products.length} products after client-side filtering for country $countryCode');
          } catch (parseError) {
            // debugPrint('🔍 Error parsing all products data: $parseError');
          }
        }
      }

      if (products.isEmpty) {
        // debugPrint('No products found for country $countryCode with either method');
        return null;
      }

      // Find minimum price from all products
      double? minPrice;
      for (final product in products) {
        if (product.price != null) {
          final price = double.tryParse(product.price!.amount);
          if (price != null) {
            if (minPrice == null || price < minPrice) {
              minPrice = price;
            }
          }
        }
      }

      // debugPrint('Min price for country $countryCode: \$${minPrice?.toStringAsFixed(2)}');
      return minPrice;
    } catch (e) {
      // debugPrint('Failed to fetch min price for country $countryCode: $e');
      return null;
    }
  }

  /// Get minimum price for products by region using tag-based filtering and client-side filtering
  Future<double?> getMinPriceByRegion(String regionCode) async {
    try {
      debugPrint('🔍 Starting min price fetch for region: $regionCode');

      // Get all products and filter client-side since metafield/tag queries aren't working correctly
      debugPrint('🔍 Getting all products for client-side filtering for region $regionCode');
      QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getProductsByRegionAndCountry),
        variables: {
          'first': 50,
          'query': null, // Get all products
        },
      );

      QueryResult result = await client.query(options);

      List<Product> products = [];

      if (!result.hasException && result.data?['products'] != null) {
        try {
          final productsConnection = ProductsConnection.fromJson(result.data!['products']);
          final allProducts = productsConnection.edges.map((edge) => edge.node).toList();

          // debugPrint('🔍 All products found: ${allProducts.length}');
          // for (final product in allProducts) {
          //   debugPrint('🔍 Product: ${product.title}');
          //   debugPrint('🔍   Tags: "${product.tags}"');
          //   debugPrint('🔍   Metafields: ${product.metafields.length}');
          //   for (final metafield in product.metafields) {
          //     debugPrint('🔍     ${metafield.namespace}.${metafield.key} = ${metafield.value}');
          //   }
          // }

          // Filter client-side by checking tags or metafields
          products = allProducts.where((product) {
            // Check if product has the region in tags
            final tags = product.tags.toLowerCase();
            final regionLower = regionCode.toLowerCase();

            if (tags.contains('region-$regionLower') ||
                tags.contains('region_$regionLower') ||
                tags.contains(regionLower) ||
                tags.contains('$regionLower-region')) {
              // debugPrint('🔍 Product ${product.title} matches region tag: $regionCode');
              return true;
            }

            // Check if product has the region in metafields
            for (final metafield in product.metafields) {
              if (metafield.namespace == 'travel' &&
                  metafield.key == 'region' &&
                  metafield.value.toLowerCase() == regionLower) {
                // debugPrint('🔍 Product ${product.title} matches region metafield: $regionCode');
                return true;
              }
            }

            // debugPrint('🔍 Product ${product.title} does not match region $regionCode');
            return false;
          }).toList();

          // debugPrint('🔍 Found ${products.length} products after client-side filtering for region $regionCode');
        } catch (parseError) {
          // debugPrint('🔍 Error parsing all products data: $parseError');
        }
      }

      if (products.isEmpty) {
        // debugPrint('No products found for region $regionCode with client-side filtering');
        return null;
      }

      // Find minimum price from all products
      double? minPrice;
      for (final product in products) {
        if (product.price != null) {
          final price = double.tryParse(product.price!.amount);
          if (price != null) {
            if (minPrice == null || price < minPrice) {
              minPrice = price;
            }
          }
        }
      }

      // debugPrint('Min price for region $regionCode: \$${minPrice?.toStringAsFixed(2)}');
      return minPrice;
    } catch (e) {
      // debugPrint('Failed to fetch min price for region $regionCode: $e');
      return null;
    }
  }

  /// Clear product cache to force fresh data fetch
  Future<void> clearProductCache() async {
    try {
      await client.resetStore();
    } catch (e) {
      throw Exception('Failed to clear product cache: $e');
    }
  }

  /// Helper method to filter products by region and/or country using metafields and tags
  List<Product> _filterProductsByRegionAndCountry(
    List<Product> products, {
    String? region,
    String? country,
  }) {
    return products.where((product) {
      bool matchesRegion = region == null || region.isEmpty;
      bool matchesCountry = country == null || country.isEmpty;

      // Check region matching
      if (region != null && region.isNotEmpty) {
        matchesRegion = _productMatchesRegion(product, region);
      }

      // Check country matching
      if (country != null && country.isNotEmpty) {
        matchesCountry = _productMatchesCountry(product, country);
      }

      final matches = matchesRegion && matchesCountry;
      // if (matches) {
      //   debugPrint('🔍 ✅ Product "${product.title}" matches filters (region: $region, country: $country)');
      // } else {
      //   debugPrint('🔍 ❌ Product "${product.title}" does not match filters (region: $region, country: $country)');
      // }

      return matches;
    }).toList();
  }

  /// Check if a product matches a specific region
  bool _productMatchesRegion(Product product, String region) {
    final regionLower = region.toLowerCase();

    // 1. Check metafields first (preferred method)
    for (final metafield in product.metafields) {
      if (metafield.namespace == 'travel' && metafield.key == 'region') {
        final metafieldValue = metafield.value.toLowerCase();
        if (metafieldValue == regionLower) {
          debugPrint('🔍   ✅ Region match via metafield: ${metafield.value}');
          return true;
        }
      }
    }

    // 2. Check tags as fallback
    final tags = product.tags.toLowerCase();
    final tagPatterns = [
      'region-$regionLower',
      'region_$regionLower',
      regionLower,
      '$regionLower-region'
    ];

    for (final pattern in tagPatterns) {
      if (tags.contains(pattern)) {
        debugPrint('🔍   ✅ Region match via tag: $pattern');
        return true;
      }
    }

    debugPrint('🔍   ❌ No region match for: $region');
    return false;
  }

  /// Check if a product matches a specific country
  bool _productMatchesCountry(Product product, String country) {
    final countryLower = country.toLowerCase();

    // 1. Check metafields first (preferred method)
    for (final metafield in product.metafields) {
      if (metafield.namespace == 'travel' && metafield.key == 'country') {
        final metafieldValue = metafield.value.toLowerCase();
        if (metafieldValue == countryLower) {
          // debugPrint('🔍   ✅ Country match via metafield: ${metafield.value}');
          return true;
        }
      }
    }

    // 2. Check tags as fallback
    final tags = product.tags.toLowerCase();
    final tagPatterns = [
      'country-$countryLower',
      'country_$countryLower',
      countryLower,
      '$countryLower-country'
    ];

    for (final pattern in tagPatterns) {
      if (tags.contains(pattern)) {
        debugPrint('🔍   ✅ Country match via tag: $pattern');
        return true;
      }
    }

    debugPrint('🔍   ❌ No country match for: $country');
    return false;
  }

  /// Test direct regions query (temporary debug method)
  Future<void> testDirectRegionsQuery() async {
    try {
      // debugPrint('🧪 Testing direct regions query...');
      final String directQuery = '''
        query Regions {
          metaobjects(type: "travel_region", first: 20) {
            nodes {
              id
              handle
              fields {
                key
                value
              }
            }
          }
        }
      ''';

      final QueryOptions options = QueryOptions(
        document: gql(directQuery),
        fetchPolicy: FetchPolicy.networkOnly,
      );

      await client.query(options);
      // debugPrint('🧪 Direct query result: ${result.data}');
      // debugPrint('🧪 Direct query exception: ${result.hasException}');
      // if (result.hasException) {
      //   debugPrint('🧪 Direct query error: ${result.exception}');
      // }
    } catch (e) {
      // debugPrint('🧪 Direct query failed: $e');
    }
  }
}
