// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'client_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CartItemUpdate _$CartItemUpdateFromJson(Map<String, dynamic> json) =>
    CartItemUpdate(
      variantId: json['variant_id'] as String,
      productId: json['product_id'] as String?,
      quantity: (json['quantity'] as num).toInt(),
      selected: json['selected'] as bool?,
    );

Map<String, dynamic> _$CartItemUpdateToJson(CartItemUpdate instance) =>
    <String, dynamic>{
      'variant_id': instance.variantId,
      'product_id': instance.productId,
      'quantity': instance.quantity,
      'selected': instance.selected,
    };

CartUpdateRequest _$CartUpdateRequestFromJson(Map<String, dynamic> json) =>
    CartUpdateRequest(
      items:
          (json['items'] as List<dynamic>?)
              ?.map((e) => CartItemUpdate.fromJson(e as Map<String, dynamic>))
              .toList(),
      preferredPaymentMethod: json['preferred_payment_method'] as String?,
    );

Map<String, dynamic> _$CartUpdateRequestToJson(CartUpdateRequest instance) =>
    <String, dynamic>{
      'items': instance.items,
      'preferred_payment_method': instance.preferredPaymentMethod,
    };

CartItemResponse _$CartItemResponseFromJson(Map<String, dynamic> json) =>
    CartItemResponse(
      variantId: json['variant_id'] as String,
      productId: json['product_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      quantity: (json['quantity'] as num).toInt(),
      totalPrice: (json['total_price'] as num).toDouble(),
      selected: json['selected'] as bool,
    );

Map<String, dynamic> _$CartItemResponseToJson(CartItemResponse instance) =>
    <String, dynamic>{
      'variant_id': instance.variantId,
      'product_id': instance.productId,
      'title': instance.title,
      'description': instance.description,
      'price': instance.price,
      'quantity': instance.quantity,
      'total_price': instance.totalPrice,
      'selected': instance.selected,
    };

CartResponse _$CartResponseFromJson(Map<String, dynamic> json) => CartResponse(
  id: json['id'] as String,
  userId: json['user_id'] as String,
  items:
      (json['items'] as List<dynamic>)
          .map((e) => CartItemResponse.fromJson(e as Map<String, dynamic>))
          .toList(),
  itemsCount: (json['items_count'] as num).toInt(),
  subtotal: (json['subtotal'] as num).toDouble(),
  currency: json['currency'] as String,
  hasDiscount: json['has_discount'] as bool,
  voucherCode: json['voucher_code'] as String?,
  discountAmount: (json['discount_amount'] as num).toDouble(),
  totalPrice: (json['total_price'] as num).toDouble(),
  preferredPaymentMethod: json['preferred_payment_method'] as String?,
  checkoutReady: json['checkout_ready'] as bool,
  createdAt: json['created_at'] as String,
  updatedAt: json['updated_at'] as String,
);

Map<String, dynamic> _$CartResponseToJson(CartResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'items': instance.items,
      'items_count': instance.itemsCount,
      'subtotal': instance.subtotal,
      'currency': instance.currency,
      'has_discount': instance.hasDiscount,
      'voucher_code': instance.voucherCode,
      'discount_amount': instance.discountAmount,
      'total_price': instance.totalPrice,
      'preferred_payment_method': instance.preferredPaymentMethod,
      'checkout_ready': instance.checkoutReady,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

ApiResponse<T> _$ApiResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => ApiResponse<T>(
  success: json['success'] as bool,
  data: _$nullableGenericFromJson(json['data'], fromJsonT),
  error: json['error'] as String?,
  errorCode: json['error_code'] as String?,
  details: json['details'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$ApiResponseToJson<T>(
  ApiResponse<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'success': instance.success,
  'data': _$nullableGenericToJson(instance.data, toJsonT),
  'error': instance.error,
  'error_code': instance.errorCode,
  'details': instance.details,
};

T? _$nullableGenericFromJson<T>(
  Object? input,
  T Function(Object? json) fromJson,
) => input == null ? null : fromJson(input);

Object? _$nullableGenericToJson<T>(
  T? input,
  Object? Function(T value) toJson,
) => input == null ? null : toJson(input);
