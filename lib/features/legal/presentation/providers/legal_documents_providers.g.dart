// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'legal_documents_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$legalDocumentsRepositoryHash() =>
    r'a839342d384188081eaa11ae75c22dee8034b564';

/// Provider for the legal documents repository
///
/// Copied from [legalDocumentsRepository].
@ProviderFor(legalDocumentsRepository)
final legalDocumentsRepositoryProvider =
    AutoDisposeProvider<LegalDocumentsRepository>.internal(
      legalDocumentsRepository,
      name: r'legalDocumentsRepositoryProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$legalDocumentsRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LegalDocumentsRepositoryRef =
    AutoDisposeProviderRef<LegalDocumentsRepository>;
String _$legalDocumentsHash() => r'f41aa27ea94057552b7d8ac8dd40a7b4b12c71af';

/// Provider for all legal documents
///
/// Copied from [legalDocuments].
@ProviderFor(legalDocuments)
final legalDocumentsProvider =
    AutoDisposeProvider<List<LegalDocument>>.internal(
      legalDocuments,
      name: r'legalDocumentsProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$legalDocumentsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LegalDocumentsRef = AutoDisposeProviderRef<List<LegalDocument>>;
String _$legalDocumentByTypeHash() =>
    r'dce429bf07cd06281d15a86b891e32c67ce45c4b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for a specific legal document by type
///
/// Copied from [legalDocumentByType].
@ProviderFor(legalDocumentByType)
const legalDocumentByTypeProvider = LegalDocumentByTypeFamily();

/// Provider for a specific legal document by type
///
/// Copied from [legalDocumentByType].
class LegalDocumentByTypeFamily extends Family<LegalDocument?> {
  /// Provider for a specific legal document by type
  ///
  /// Copied from [legalDocumentByType].
  const LegalDocumentByTypeFamily();

  /// Provider for a specific legal document by type
  ///
  /// Copied from [legalDocumentByType].
  LegalDocumentByTypeProvider call(LegalDocumentType type) {
    return LegalDocumentByTypeProvider(type);
  }

  @override
  LegalDocumentByTypeProvider getProviderOverride(
    covariant LegalDocumentByTypeProvider provider,
  ) {
    return call(provider.type);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'legalDocumentByTypeProvider';
}

/// Provider for a specific legal document by type
///
/// Copied from [legalDocumentByType].
class LegalDocumentByTypeProvider extends AutoDisposeProvider<LegalDocument?> {
  /// Provider for a specific legal document by type
  ///
  /// Copied from [legalDocumentByType].
  LegalDocumentByTypeProvider(LegalDocumentType type)
    : this._internal(
        (ref) => legalDocumentByType(ref as LegalDocumentByTypeRef, type),
        from: legalDocumentByTypeProvider,
        name: r'legalDocumentByTypeProvider',
        debugGetCreateSourceHash:
            const bool.fromEnvironment('dart.vm.product')
                ? null
                : _$legalDocumentByTypeHash,
        dependencies: LegalDocumentByTypeFamily._dependencies,
        allTransitiveDependencies:
            LegalDocumentByTypeFamily._allTransitiveDependencies,
        type: type,
      );

  LegalDocumentByTypeProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.type,
  }) : super.internal();

  final LegalDocumentType type;

  @override
  Override overrideWith(
    LegalDocument? Function(LegalDocumentByTypeRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: LegalDocumentByTypeProvider._internal(
        (ref) => create(ref as LegalDocumentByTypeRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        type: type,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<LegalDocument?> createElement() {
    return _LegalDocumentByTypeProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is LegalDocumentByTypeProvider && other.type == type;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, type.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin LegalDocumentByTypeRef on AutoDisposeProviderRef<LegalDocument?> {
  /// The parameter `type` of this provider.
  LegalDocumentType get type;
}

class _LegalDocumentByTypeProviderElement
    extends AutoDisposeProviderElement<LegalDocument?>
    with LegalDocumentByTypeRef {
  _LegalDocumentByTypeProviderElement(super.provider);

  @override
  LegalDocumentType get type => (origin as LegalDocumentByTypeProvider).type;
}

String _$currentLegalDocumentHash() =>
    r'a7d01783d99b026566b8a79a796593903dfa963e';

/// Provider for the current document based on selected type
///
/// Copied from [currentLegalDocument].
@ProviderFor(currentLegalDocument)
final currentLegalDocumentProvider =
    AutoDisposeProvider<LegalDocument?>.internal(
      currentLegalDocument,
      name: r'currentLegalDocumentProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$currentLegalDocumentHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentLegalDocumentRef = AutoDisposeProviderRef<LegalDocument?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
