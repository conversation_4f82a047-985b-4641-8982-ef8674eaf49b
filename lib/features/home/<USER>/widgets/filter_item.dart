import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../l10n/app_localizations.dart';

class FilterItem extends StatelessWidget {
  final VoidCallback onCountriesPressed;
  final VoidCallback onRegionsPressed;
  final bool isCountriesSelected;

  const FilterItem({
    super.key,
    required this.onCountriesPressed,
    required this.onRegionsPressed,
    this.isCountriesSelected = true,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        ElevatedButton(
          onPressed: onCountriesPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor:
                isCountriesSelected
                    ? const Color(0xFFFF6982)
                    : Colors.transparent,
            foregroundColor:
                isCountriesSelected ? Colors.white : const Color(0xFFFF6982),
            elevation: isCountriesSelected ? 1 : 0,
            side: BorderSide(
              color: const Color(0xFFFF6982),
              width: isCountriesSelected ? 0 : 1,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(100),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          child: Text(
            AppLocalizations.of(context).countries,
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight:
                  isCountriesSelected ? FontWeight.bold : FontWeight.w600,
            ),
          ),
        ),
        const SizedBox(width: 16),
        ElevatedButton(
          onPressed: onRegionsPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor:
                !isCountriesSelected
                    ? const Color(0xFFFF6982)
                    : Colors.transparent,
            foregroundColor:
                !isCountriesSelected ? Colors.white : const Color(0xFFFF6982),
            elevation: !isCountriesSelected ? 1 : 0,
            side: BorderSide(
              color: const Color(0xFFFF6982),
              width: !isCountriesSelected ? 0 : 1,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(100),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          child: Text(
            AppLocalizations.of(context).regions,
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight:
                  !isCountriesSelected ? FontWeight.bold : FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
