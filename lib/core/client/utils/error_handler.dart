import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/client_models.dart';

/// Error handler for TravelGator client operations
class ClientErrorHandler {
  /// Convert DioException to ClientException
  static ClientException handleDioError(DioException error) {
    if (kDebugMode) {
      debugPrint('[ClientErrorHandler] DioException: ${error.type}');
      debugPrint('[ClientErrorHandler] Message: ${error.message}');
      debugPrint('[ClientErrorHandler] Response: ${error.response?.data}');
    }

    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ClientException(
          message: 'Network timeout. Please check your connection.',
          type: ClientErrorType.network,
          statusCode: statusCode,
          originalError: error,
        );

      case DioExceptionType.connectionError:
        return ClientException(
          message: 'Connection failed. Please check your internet connection.',
          type: ClientErrorType.network,
          statusCode: statusCode,
          originalError: error,
        );

      case DioExceptionType.badResponse:
        return _handleBadResponse(statusCode, responseData, error);

      case DioExceptionType.cancel:
        return ClientException(
          message: 'Request was cancelled.',
          type: ClientErrorType.network,
          statusCode: statusCode,
          originalError: error,
        );

      case DioExceptionType.unknown:
      default:
        return ClientException(
          message: 'An unexpected error occurred: ${error.message}',
          type: ClientErrorType.unknown,
          statusCode: statusCode,
          originalError: error,
        );
    }
  }

  /// Handle bad response errors with specific status codes
  static ClientException _handleBadResponse(
    int? statusCode,
    dynamic responseData,
    DioException originalError,
  ) {
    String message;
    ClientErrorType type;
    String? errorCode;
    Map<String, dynamic>? details;

    // Extract error information from response
    if (responseData is Map<String, dynamic>) {
      message = responseData['error'] ?? 'Server error occurred';
      errorCode = responseData['error_code'];
      details = responseData['details'];
    } else {
      message = 'Server error occurred';
    }

    switch (statusCode) {
      case 400:
        type = ClientErrorType.validation;
        message = _getValidationErrorMessage(errorCode, details) ?? message;
        break;
      case 401:
        type = ClientErrorType.authentication;
        message = 'Authentication failed. Please sign in again.';
        break;
      case 403:
        type = ClientErrorType.authentication;
        message = 'Access denied. You don\'t have permission for this action.';
        break;
      case 404:
        type = ClientErrorType.validation;
        message = 'Resource not found.';
        break;
      case 422:
        type = ClientErrorType.validation;
        message = _getValidationErrorMessage(errorCode, details) ?? message;
        break;
      case 429:
        type = ClientErrorType.network;
        message = 'Too many requests. Please try again later.';
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        type = ClientErrorType.server;
        message = 'Server error. Please try again later.';
        break;
      default:
        type = ClientErrorType.server;
        break;
    }

    return ClientException(
      message: message,
      type: type,
      errorCode: errorCode,
      statusCode: statusCode,
      details: details,
      originalError: originalError,
    );
  }

  /// Get user-friendly validation error messages
  static String? _getValidationErrorMessage(
    String? errorCode,
    Map<String, dynamic>? details,
  ) {
    switch (errorCode) {
      case 'INVALID_PAYMENT_METHOD':
        final validMethods = details?['valid_methods'] as List?;
        if (validMethods != null) {
          return 'Invalid payment method. Valid options: ${validMethods.join(', ')}';
        }
        return 'Invalid payment method selected.';
      case 'INVALID_QUANTITY':
        return 'Invalid quantity. Must be a positive number.';
      case 'ITEM_NOT_FOUND':
        return 'Item not found in cart.';
      case 'CART_EMPTY':
        return 'Cart is empty.';
      case 'INSUFFICIENT_STOCK':
        return 'Insufficient stock for requested quantity.';
      default:
        return null;
    }
  }

  /// Handle general exceptions
  static ClientException handleGeneralError(dynamic error) {
    if (error is ClientException) {
      return error;
    }

    if (error is DioException) {
      return handleDioError(error);
    }

    if (kDebugMode) {
      debugPrint('[ClientErrorHandler] General error: $error');
    }

    return ClientException(
      message: 'An unexpected error occurred: $error',
      type: ClientErrorType.unknown,
      originalError: error,
    );
  }

  /// Check if error is retryable
  static bool isRetryableError(ClientException error) {
    switch (error.type) {
      case ClientErrorType.network:
        return error.statusCode != 429; // Don't retry rate limits
      case ClientErrorType.server:
        return error.statusCode == null || error.statusCode! >= 500;
      case ClientErrorType.authentication:
      case ClientErrorType.validation:
      case ClientErrorType.unknown:
        return false;
    }
  }

  /// Get retry delay based on attempt number
  static Duration getRetryDelay(int attempt) {
    // Exponential backoff: 1s, 2s, 4s, 8s...
    return Duration(seconds: 1 << (attempt - 1));
  }
}

/// Response processor for API responses
class ResponseProcessor {
  /// Process API response and extract data
  static T processResponse<T>(
    Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    if (kDebugMode) {
      debugPrint('[ResponseProcessor] Processing response: ${response.statusCode}');
    }

    final responseData = response.data;
    if (responseData is! Map<String, dynamic>) {
      throw ClientException(
        message: 'Invalid response format',
        type: ClientErrorType.server,
        statusCode: response.statusCode,
      );
    }

    final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
      responseData,
      (json) => json as Map<String, dynamic>,
    );

    if (!apiResponse.success) {
      throw ClientException(
        message: apiResponse.error ?? 'API request failed',
        type: ClientErrorType.server,
        errorCode: apiResponse.errorCode,
        statusCode: response.statusCode,
        details: apiResponse.details,
      );
    }

    if (apiResponse.data == null) {
      throw ClientException(
        message: 'No data in response',
        type: ClientErrorType.server,
        statusCode: response.statusCode,
      );
    }

    return fromJson(apiResponse.data!);
  }

  /// Process simple success response
  static void processSuccessResponse(Response response) {
    if (kDebugMode) {
      debugPrint('[ResponseProcessor] Processing success response: ${response.statusCode}');
    }

    final responseData = response.data;
    if (responseData is Map<String, dynamic>) {
      final success = responseData['success'] as bool?;
      if (success == false) {
        throw ClientException(
          message: responseData['error'] ?? 'Request failed',
          type: ClientErrorType.server,
          errorCode: responseData['error_code'],
          statusCode: response.statusCode,
          details: responseData['details'],
        );
      }
    }
  }
}
