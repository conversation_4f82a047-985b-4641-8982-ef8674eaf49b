import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/providers/product_provider.dart';
import '../../../../core/utils/modal_manager.dart';
import '../../../../l10n/app_localizations.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../../../auth/presentation/providers/auth_state.dart';
import '../widgets/product_grid_item.dart';
import '../widgets/price_filter_widget.dart';

// Separate provider for filtered products to avoid affecting home screen
final filteredProductsProvider = StateNotifierProvider<ProductsNotifier, ProductsState>(
  (ref) {
    final repository = ref.watch(productRepositoryProvider);
    return ProductsNotifier(repository);
  },
);

class FilteredProductsScreen extends ConsumerStatefulWidget {
  final String? countryCode;
  final String? regionCode;
  final String filterTitle;

  const FilteredProductsScreen({
    super.key,
    this.countryCode,
    this.regionCode,
    required this.filterTitle,
  });

  @override
  ConsumerState<FilteredProductsScreen> createState() => _FilteredProductsScreenState();
}

class _FilteredProductsScreenState extends ConsumerState<FilteredProductsScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _hasSetupAuthListener = false;

  @override
  void initState() {
    super.initState();
    
    // Load filtered products when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      debugPrint('🔍 FilteredProductsScreen: Starting to load filtered products');
      if (widget.countryCode != null) {
        debugPrint('🔍 Loading products for country: ${widget.countryCode}');
        ref.read(filteredProductsProvider.notifier).setCountryFilter(widget.countryCode!);
      } else if (widget.regionCode != null) {
        debugPrint('🔍 Loading products for region: ${widget.regionCode}');
        ref.read(filteredProductsProvider.notifier).setRegionFilter(widget.regionCode!);
      }
    });

    // Setup pagination
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= 
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreProducts();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadMoreProducts() {
    final productsState = ref.read(filteredProductsProvider);
    if (!productsState.isLoading && productsState.hasNextPage) {
      if (widget.countryCode != null) {
        ref.read(filteredProductsProvider.notifier).loadMoreProductsByCountry(
          widget.countryCode!,
        );
      } else if (widget.regionCode != null) {
        ref.read(filteredProductsProvider.notifier).loadMoreProductsByRegion(
          widget.regionCode!,
        );
      }
    }
  }

  /// Sets up a listener for authentication state changes
  void _setupAuthRedirect() {
    if (_hasSetupAuthListener) return;

    ref.listen<AuthState>(authNotifierProvider, (previous, current) {
      current.maybeMap(
        authenticated: (state) {
          // Close all open modals when authentication succeeds
          ModalManager().closeAllModals();

          // Don't redirect - let user continue their shopping flow
          debugPrint('[FilteredProductsScreen] Authentication successful, staying on filtered products screen for seamless shopping experience');
        },
        orElse: () {},
      );
    });

    _hasSetupAuthListener = true;
  }

  @override
  Widget build(BuildContext context) {
    // Setup authentication redirect listener
    _setupAuthRedirect();

    final productsState = ref.watch(filteredProductsProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFF8F9FA),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFFE9ECEF)),
            ),
            child: const Icon(
              Icons.arrow_back_ios_new,
              color: Color(0xFF1F2937),
              size: 18,
            ),
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          widget.filterTitle,
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF1F2937),
            letterSpacing: -0.02,
          ),
        ),
        centerTitle: false,
        actions: [
          // Container(
          //   margin: const EdgeInsets.only(right: 16),
          //   child: Container(
          //     padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          //     decoration: BoxDecoration(
          //       color: const Color(0xFFFF6982),
          //       borderRadius: BorderRadius.circular(20),
          //       boxShadow: [
          //         BoxShadow(
          //           color: const Color(0xFFFF6982).withValues(alpha: 0.3),
          //           blurRadius: 4,
          //           offset: const Offset(0, 2),
          //         ),
          //       ],
          //     ),
          //     child: Text(
          //       '${productsState.products.length}',
          //       style: GoogleFonts.inter(
          //         fontSize: 14,
          //         fontWeight: FontWeight.w700,
          //         color: Colors.white,
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
      body: GestureDetector(
        onTap: () {
          // Dismiss keyboard when tapping outside input fields
          FocusScope.of(context).unfocus();
        },
        child: Column(
          children: [
            // Filter info header
            

          // Price filter
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 16),
            child: PriceFilterWidget(
              onPriceChanged: (minPrice, maxPrice) {
                ref.read(filteredProductsProvider.notifier).setPriceFilter(
                  minPrice: minPrice,
                  maxPrice: maxPrice,
                );
              },
            ),
          ),

          // Products grid
          Expanded(
            child: _buildProductsGrid(productsState),
          ),
        ],
        ),
      ),
    );
  }

  Widget _buildProductsGrid(ProductsState productsState) {
    if (productsState.isLoading && productsState.products.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFFFF6982)),
      );
    }

    if (productsState.error != null && productsState.products.isEmpty) {
      return Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.error_outline,
                size: 40,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 8),
              Text(
                AppLocalizations.of(context).errorLoadingProducts,
                style: GoogleFonts.inter(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  if (widget.countryCode != null) {
                    ref.read(filteredProductsProvider.notifier).setCountryFilter(widget.countryCode!);
                  } else if (widget.regionCode != null) {
                    ref.read(filteredProductsProvider.notifier).setRegionFilter(widget.regionCode!);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF6982),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                ),
                child: Text(
                  AppLocalizations.of(context).retry,
                  style: GoogleFonts.inter(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (productsState.products.isEmpty) {
      return Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.inventory_2_outlined,
                size: 40,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 8),
              Text(
                AppLocalizations.of(context).noProductsFound,
                style: GoogleFonts.inter(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 4),
              Text(
                AppLocalizations.of(context).tryAdjustingFilter,
                textAlign: TextAlign.center,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        if (widget.countryCode != null) {
          await ref.read(filteredProductsProvider.notifier).setCountryFilter(widget.countryCode!);
        } else if (widget.regionCode != null) {
          await ref.read(filteredProductsProvider.notifier).setRegionFilter(widget.regionCode!);
        }
      },
      child: GridView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.85, // Increased to 0.85 to give more height
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: productsState.products.length + (productsState.hasNextPage ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= productsState.products.length) {
            // Loading indicator for pagination
            return const Center(
              child: CircularProgressIndicator(color: Color(0xFFFF6982)),
            );
          }

          final product = productsState.products[index];
          return ProductGridItem(product: product);
        },
      ),
    );
  }
}
