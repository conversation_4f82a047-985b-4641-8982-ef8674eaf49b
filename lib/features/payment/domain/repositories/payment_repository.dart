import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/payment_intent.dart';
import '../entities/payment_status.dart';
import '../entities/shipping_address.dart';

/// Repository interface for payment operations following documented API
abstract class PaymentRepository {
  /// Create payment intent for cart checkout
  Future<Either<Failure, PaymentIntent>> createCartPaymentIntent({
    required ShippingAddress shippingAddress,
  });

  /// Create payment intent for digital product cart checkout (no shipping required)
  Future<Either<Failure, PaymentIntent>> createCartPaymentIntentDigital();

  /// Create payment intent for specific cart items (selected items checkout)
  Future<Either<Failure, PaymentIntent>> createCartPaymentIntentWithItems({
    required List<Map<String, dynamic>> cartItems,
  });

  /// Create payment intent for buy now
  Future<Either<Failure, PaymentIntent>> createBuyNowPaymentIntent({
    required String variantId,
    required int quantity,
    required double price,
    required String productName,
    String? voucherCode,
  });

  /// Get cart payment status
  Future<Either<Failure, PaymentStatus>> getCartPaymentStatus({
    required String paymentIntentId,
  });

  /// Get buy now payment status
  Future<Either<Failure, PaymentStatus>> getBuyNowPaymentStatus({
    required String paymentIntentId,
  });

  /// Confirm cart payment with payment method
  Future<Either<Failure, PaymentStatus>> confirmCartPayment({
    required String paymentIntentId,
    required String paymentMethodId,
  });

  /// Confirm payment with card details via backend (PCI compliant)
  Future<Either<Failure, PaymentStatus>> confirmPaymentWithCardDetails({
    required String paymentIntentId,
    required String clientSecret,
    required Map<String, dynamic> cardDetails,
  });

  /// Confirm buy now payment with payment method
  Future<Either<Failure, PaymentStatus>> confirmBuyNowPayment({
    required String paymentIntentId,
    required String paymentMethodId,
  });

  /// Cancel cart payment intent
  Future<Either<Failure, void>> cancelCartPayment({
    required String paymentIntentId,
  });
}
