import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/stripe_config.dart';

part 'stripe_config_model.g.dart';

/// Data model for Stripe configuration from backend API
@JsonSerializable()
class StripeConfigModel {
  @Json<PERSON>ey(name: 'publishable_key', defaultValue: '')
  final String publishableKey;
  @JsonKey(name: 'merchant_id')
  final String merchantId;
  @JsonKey(name: 'merchant_display_name')
  final String merchantDisplayName;
  @JsonKey(name: 'country_code')
  final String countryCode;
  final String currency;
  @JsonKey(name: 'test_mode')
  final bool testMode;
  @JsonKey(name: 'additional_settings')
  final Map<String, dynamic>? additionalSettings;

  const StripeConfigModel({
    required this.publishableKey,
    this.merchantId = '',
    this.merchantDisplayName = 'TravelGator',
    this.countryCode = 'US',
    this.currency = 'USD',
    this.testMode = true,
    this.additionalSettings,
  });

  factory StripeConfigModel.fromJson(Map<String, dynamic> json) {
    // Handle missing currency field from backend
    final modifiedJson = Map<String, dynamic>.from(json);
    if (!modifiedJson.containsKey('currency')) {
      modifiedJson['currency'] = 'USD';
    }
    return _$StripeConfigModelFromJson(modifiedJson);
  }

  Map<String, dynamic> toJson() => _$StripeConfigModelToJson(this);

  /// Convert to domain entity
  StripeConfig toDomain() {
    return StripeConfig(
      publishableKey: publishableKey,
      merchantId: merchantId,
      merchantDisplayName: merchantDisplayName,
      countryCode: countryCode,
      currency: currency,
      testMode: testMode,
      additionalSettings: additionalSettings,
    );
  }

  /// Create from domain entity
  factory StripeConfigModel.fromDomain(StripeConfig config) {
    return StripeConfigModel(
      publishableKey: config.publishableKey,
      merchantId: config.merchantId,
      merchantDisplayName: config.merchantDisplayName,
      countryCode: config.countryCode,
      currency: config.currency,
      testMode: config.testMode,
      additionalSettings: config.additionalSettings,
    );
  }
}
