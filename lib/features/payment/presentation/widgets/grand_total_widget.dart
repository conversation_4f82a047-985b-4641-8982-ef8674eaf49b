import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Grand Total widget that includes discount code, total, and pay button
/// Following Figma design node-id=203-3563
class GrandTotalWidget extends StatelessWidget {
  final double grandTotal;
  final bool isLoading;
  final Function(String)? onDiscountCodeChanged;
  final VoidCallback? onPayPressed;

  const GrandTotalWidget({
    super.key,
    required this.grandTotal,
    this.isLoading = false,
    this.onDiscountCodeChanged,
    this.onPayPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 375,
      height: 163,
      decoration: const BoxDecoration(
        color: Color(0xFFFFFFFF),
        border: Border(
          top: BorderSide(
            color: Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
        child: Column(
          children: [
            // Discount Code section
            Row(
              children: [
                // Discount code label
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(8, 8, 16, 8),
                    child: Text(
                      'Discount code',
                      style: GoogleFonts.inter(
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF1F2937),
                      ),
                    ),
                  ),
                ),
                
                // Discount code input
                Expanded(
                  child: Container(
                    height: 46,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFFFFF),
                      border: Border.all(color: const Color(0xFFE5E7EB)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextField(
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF1F2937),
                      ),
                      decoration: const InputDecoration(
                        hintText: 'Enter code',
                        hintStyle: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: Color(0xFF9CA3AF),
                        ),
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        errorBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                        filled: false,
                        isDense: true,
                      ),
                      textAlign: TextAlign.center,
                      onChanged: onDiscountCodeChanged,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Grand Total section
            Container(
              padding: const EdgeInsets.fromLTRB(8, 8, 16, 8),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Grand Total:',
                        style: GoogleFonts.inter(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1F2937),
                          height: 1.21,
                        ),
                      ),
                      Text(
                        '\$${grandTotal.toStringAsFixed(2)}',
                        style: GoogleFonts.inter(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1F2937),
                          height: 1.21,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Divider line
                  Container(
                    height: 1,
                    color: const Color(0xFFE5E7EB),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Pay Now button
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: isLoading ? null : onPayPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF6982),
                  foregroundColor: Colors.white,
                  elevation: 4,
                  shadowColor: Colors.black.withValues(alpha: 0.25),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  padding: const EdgeInsets.all(12),
                ),
                child: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Pay Now',
                        style: GoogleFonts.inter(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
