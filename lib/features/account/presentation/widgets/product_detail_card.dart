import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/order.dart' as entities;
import '../../../../core/providers/product_provider.dart';
import '../../../../core/models/product_model.dart';

/// Widget for displaying product details in transaction detail screen
class ProductDetailCard extends ConsumerWidget {
  final entities.OrderItem orderItem;
  final bool isLast;

  const ProductDetailCard({
    super.key,
    required this.orderItem,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch products provider to get variant information and images
    final productsState = ref.watch(productsProvider);

    // If products are not loaded, trigger loading (needed for variant info and images)
    if (productsState.products.isEmpty && !productsState.isLoading) {
      // debugPrint('[ProductDetailCard] No products loaded, triggering product fetch...');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(productsProvider.notifier).fetchProducts(first: 10);
      });
    }

    // Try to get image URL from order item first, then from products provider
    String? productImageUrl = orderItem.imageUrl;

    // debugPrint('[ProductDetailCard] Order item ${orderItem.title}: imageUrl from order = $productImageUrl');

    // If no image URL from order, try to find it in the products provider
    if (productImageUrl == null || productImageUrl.isEmpty) {
      // Try to find matching product by variant ID
      final orderVariantId = orderItem.variantId;
      final matchingProduct = productsState.products.where((product) {
        // Check if any variant matches the order's variant ID
        return product.variants.edges.any((variantEdge) =>
          _isMatchingVariantId(variantEdge.node.id, orderVariantId));
      }).firstOrNull;

      if (matchingProduct?.featuredImage?.url != null) {
        productImageUrl = matchingProduct!.featuredImage!.url;
        // debugPrint('[ProductDetailCard] Found product image for order item ${orderItem.title}: $productImageUrl');
      } else {
        // debugPrint('[ProductDetailCard] No product image found for order item ${orderItem.title} (variantId: $orderVariantId)');
        // if (productsState.products.isNotEmpty) {
        //   debugPrint('[ProductDetailCard] Available products: ${productsState.products.map((p) => '${p.title} (variants: ${p.variants.edges.map((v) => v.node.id).take(2).join(', ')})').take(3).join(', ')}...');
        // }
      }
    }

    return Container(
      padding: const EdgeInsets.all(8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product image
          Container(
            width: 108,
            height: 83,
            decoration: BoxDecoration(
              color: const Color(0xFFF3F4F6),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: productImageUrl != null && productImageUrl.isNotEmpty
                  ? Image.network(
                      productImageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        // debugPrint('[ProductDetailCard] Image load error for $productImageUrl: $error');
                        return _buildPlaceholderImage();
                      },
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) {
                          // debugPrint('[ProductDetailCard] Image loaded successfully: $productImageUrl');
                          return child;
                        }
                        return Container(
                          color: const Color(0xFFF3F4F6),
                          child: const Center(
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6982)),
                            ),
                          ),
                        );
                      },
                    )
                  : _buildPlaceholderImage(),
            ),
          ),

          const SizedBox(width: 8),

          // Product details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product title
                Text(
                  orderItem.title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF1F2937),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),

                // Product specifications
                Row(
                  children: [
                    Expanded(
                      child: _buildSpecItem('GB', _extractGB(ref)),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildSpecItem('Day', _extractDays(ref)),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Price
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      orderItem.formattedTotalPrice,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF1F2937),
                      ),
                    ),
                    const Text(
                      ' (USD)', // Changed from SGD to USD to match Stripe config
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF1F2937),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecItem(String label, String value) {
    return Row(
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: Color(0xFF9CA3AF),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: Color(0xFF1F2937),
          ),
        ),
      ],
    );
  }

  /// Get variant from products provider using variant ID
  Variant? _getVariantFromId(WidgetRef ref) {
    // Get all products from the provider
    final productsState = ref.read(productsProvider);

    // debugPrint('🔍 ProductDetailCard: Looking for variant with ID: ${orderItem.variantId}');
    // debugPrint('🔍 ProductDetailCard: Available products: ${productsState.products.length}');

    // Find the product that contains this variant
    for (final product in productsState.products) {
      for (final variantEdge in product.variants.edges) {
        final variant = variantEdge.node;

        // Check multiple ID format possibilities:
        // 1. Exact match
        if (variant.id == orderItem.variantId) {
          // debugPrint('🔍 ProductDetailCard: Found exact match variant: ${variant.id}');
          return variant;
        }

        // 2. GID format vs numeric format
        // Extract numeric part from GID (e.g., "gid://shopify/ProductVariant/123" -> "123")
        final variantNumericId = variant.id.split('/').last;
        final orderItemNumericId = orderItem.variantId.split('/').last;

        if (variantNumericId == orderItemNumericId) {
          // debugPrint('🔍 ProductDetailCard: Found numeric match variant: ${variant.id} ($variantNumericId)');
          return variant;
        }

        // 3. Check if one contains the other
        if (variant.id.contains(orderItem.variantId) ||
            orderItem.variantId.contains(variant.id)) {
          // debugPrint('🔍 ProductDetailCard: Found partial match variant: ${variant.id}');
          return variant;
        }
      }
    }

    // debugPrint('🔍 ProductDetailCard: No variant found for ID: ${orderItem.variantId}');
    return null;
  }

  String _extractGB(WidgetRef ref) {
    // Get variant data from product provider using variant_id
    final variant = _getVariantFromId(ref);
    if (variant != null) {
      // debugPrint('🔍 ProductDetailCard: Found variant for data extraction: ${variant.id}');
      // debugPrint('🔍 ProductDetailCard: Variant selectedOptions: ${variant.selectedOptions.map((o) => '${o.name}=${o.value}').join(', ')}');

      // Look for GB option in selectedOptions
      final gbOption = variant.getOptionValue('gb') ?? variant.getOptionValue('data');
      if (gbOption != null) {
        // debugPrint('🔍 ProductDetailCard: Found GB option: $gbOption');
        // Extract numeric value from option (e.g., "1GB" -> "1", "5" -> "5")
        final regex = RegExp(r'(\d+)');
        final match = regex.firstMatch(gbOption);
        final result = match?.group(1) ?? '1';
        // debugPrint('🔍 ProductDetailCard: Extracted data amount: $result');
        return '${result}GB';
      } else {
        // debugPrint('🔍 ProductDetailCard: No GB/data option found in variant');
      }
    } else {
      // debugPrint('🔍 ProductDetailCard: No variant found, using title fallback');
    }

    // Fallback to title parsing if variant not found
    final title = orderItem.title.toLowerCase();
    if (title.contains('1gb')) return '1GB';
    if (title.contains('2gb')) return '2GB';
    if (title.contains('3gb')) return '3GB';
    if (title.contains('5gb')) return '5GB';
    if (title.contains('10gb')) return '10GB';
    if (title.contains('12gb')) return '12GB';
    if (title.contains('15gb')) return '15GB';
    if (title.contains('20gb')) return '20GB';
    return '1GB'; // Default
  }

  String _extractDays(WidgetRef ref) {
    // Get variant data from product provider using variant_id
    final variant = _getVariantFromId(ref);
    if (variant != null) {
      // Look for days option in selectedOptions
      final daysOption = variant.getOptionValue('days') ?? variant.getOptionValue('validity');
      if (daysOption != null) {
        // debugPrint('🔍 ProductDetailCard: Found days option: $daysOption');
        // Extract numeric value from option (e.g., "1 day" -> "1", "7 Days" -> "7")
        final regex = RegExp(r'(\d+)');
        final match = regex.firstMatch(daysOption);
        final duration = match?.group(1) ?? '1';
        // debugPrint('🔍 ProductDetailCard: Extracted duration: $duration');
        return '$duration Days';
      }
    }

    // Fallback to title parsing if variant not found
    final title = orderItem.title.toLowerCase();
    if (title.contains('1 day') || title.contains('1-day')) return '1 Days';
    if (title.contains('2 day') || title.contains('2-day')) return '2 Days';
    if (title.contains('3 day') || title.contains('3-day')) return '3 Days';
    if (title.contains('4 day') || title.contains('4-day')) return '4 Days';
    if (title.contains('5 day') || title.contains('5-day')) return '5 Days';
    if (title.contains('7 day') || title.contains('7-day')) return '7 Days';
    if (title.contains('10 day') || title.contains('10-day')) return '10 Days';
    if (title.contains('15 day') || title.contains('15-day')) return '15 Days';
    if (title.contains('30 day') || title.contains('30-day')) return '30 Days';
    return '2 Days'; // Default
  }

  /// Build placeholder image when no image URL is available
  Widget _buildPlaceholderImage() {
    // debugPrint('[ProductDetailCard] No image URL available for: ${orderItem.title}');
    return Container(
      color: const Color(0xFFF3F4F6),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.sim_card,
            color: Color(0xFF9CA3AF),
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            'eSIM',
            style: TextStyle(
              fontSize: 10,
              color: Color(0xFF9CA3AF),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// Check if variant IDs match, handling both numeric and GID formats
  bool _isMatchingVariantId(String productVariantId, String orderVariantId) {
    // Direct match
    if (productVariantId == orderVariantId) return true;

    // Extract numeric ID from GID format (gid://shopify/ProductVariant/123456)
    final productNumericId = _extractNumericId(productVariantId);
    final orderNumericId = _extractNumericId(orderVariantId);

    // Compare numeric IDs
    if (productNumericId != null && orderNumericId != null) {
      return productNumericId == orderNumericId;
    }

    // Compare with order ID as numeric string
    if (productNumericId != null && productNumericId == orderVariantId) {
      return true;
    }

    // Compare with product ID as numeric string
    if (orderNumericId != null && orderNumericId == productVariantId) {
      return true;
    }

    return false;
  }

  /// Extract numeric ID from GID format
  String? _extractNumericId(String id) {
    if (id.startsWith('gid://shopify/')) {
      final parts = id.split('/');
      return parts.isNotEmpty ? parts.last : null;
    }
    return id; // Return as-is if not GID format
  }


}
