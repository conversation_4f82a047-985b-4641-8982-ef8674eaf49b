import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// Service for handling buy now voucher-related API operations
class BuyNowVoucherService {
  final Dio _dio;

  BuyNowVoucherService({required Dio dio}) : _dio = dio;

  /// Apply voucher to buy now session
  /// Enhanced version with product-specific validation support
  Future<BuyNowVoucherApplicationResult> applyVoucherToBuyNow({
    required String voucherCode,
    required double orderAmount,
    required String authToken,
    // Enhanced product-specific validation fields
    String? variantId,
    String? productId,
    int? quantity,
    double? price,
    String? productName,
  }) async {
    try {
      debugPrint('[BuyNowVoucherService] Applying voucher to buy now: $voucherCode for amount: \$${orderAmount.toStringAsFixed(2)}');
      if (variantId != null) {
        debugPrint('[BuyNowVoucherService] Enhanced request with product validation: variant=$variantId, product=$productId');
      }

      // Build request data with enhanced product-specific validation
      final requestData = <String, dynamic>{
        'voucher_code': voucherCode,
        'order_amount': orderAmount,
      };

      // Add enhanced product-specific validation fields if provided
      if (variantId != null) requestData['variant_id'] = variantId;
      if (productId != null) requestData['product_id'] = productId;
      if (quantity != null) requestData['quantity'] = quantity;
      if (price != null) requestData['price'] = price;
      if (productName != null) requestData['product_name'] = productName;

      final response = await _dio.post(
        '/buy_now/apply_voucher',
        data: requestData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[BuyNowVoucherService] Apply voucher response: ${response.statusCode}');
      debugPrint('[BuyNowVoucherService] Response data: ${response.data}');

      if (response.statusCode == 200 && response.data['data'] != null) {
        final responseData = response.data['data'] as Map<String, dynamic>;
        final buyNowSession = responseData['buy_now_session'] as Map<String, dynamic>?;
        
        return BuyNowVoucherApplicationResult(
          success: true,
          discountAmount: (responseData['discount_amount'] as num?)?.toDouble() ?? 0.0,
          finalAmount: (responseData['final_amount'] as num?)?.toDouble() ?? orderAmount,
          buyNowSession: buyNowSession,
          voucherInfo: responseData['voucher'] as Map<String, dynamic>?,
          errorMessage: null,
        );
      } else {
        return BuyNowVoucherApplicationResult(
          success: false,
          discountAmount: 0.0,
          finalAmount: orderAmount,
          buyNowSession: null,
          voucherInfo: null,
          errorMessage: response.data['message'] ?? 'Failed to apply voucher',
        );
      }
    } on DioException catch (e) {
      debugPrint('[BuyNowVoucherService] DioException applying voucher: ${e.message}');
      debugPrint('[BuyNowVoucherService] Response data: ${e.response?.data}');

      String errorMessage = 'Failed to apply voucher';

      // Handle specific status codes for enhanced error messages
      if (e.response?.statusCode == 400) {
        // Handle product-specific discount validation errors
        final responseData = e.response?.data;
        if (responseData is Map<String, dynamic>) {
          // Check for specific error messages from Shopify integration
          if (responseData['error'] != null) {
            // Handle both String and Map error types
            if (responseData['error'] is String) {
              errorMessage = responseData['error'];
            } else if (responseData['error'] is Map<String, dynamic>) {
              final errorInfo = responseData['error'] as Map<String, dynamic>;
              errorMessage = errorInfo['message'] ?? 'Invalid voucher code or product not eligible';
            } else {
              errorMessage = 'Invalid voucher code or product not eligible';
            }
          } else if (responseData['message'] != null) {
            errorMessage = responseData['message'];
          } else {
            errorMessage = 'Invalid voucher code or product not eligible';
          }
        } else {
          errorMessage = 'Invalid voucher code';
        }
      } else if (e.response?.statusCode == 404) {
        errorMessage = 'Voucher code not found';
      } else if (e.response?.statusCode == 422) {
        // Handle validation errors (like minimum order amount or product eligibility)
        final responseData = e.response?.data;
        if (responseData is Map<String, dynamic>) {
          errorMessage = responseData['error'] ?? responseData['message'] ?? 'Product not eligible for this discount';
        } else {
          errorMessage = 'Product validation failed';
        }
      } else if (e.response?.statusCode == 500) {
        // Handle server errors with detailed error information
        final responseData = e.response?.data;
        if (responseData is Map<String, dynamic>) {
          if (responseData['error'] is Map<String, dynamic>) {
            final errorInfo = responseData['error'] as Map<String, dynamic>;
            errorMessage = errorInfo['message'] ?? 'Server error occurred';
          } else if (responseData['error'] is String) {
            errorMessage = responseData['error'];
          } else if (responseData['message'] is String) {
            errorMessage = responseData['message'];
          } else {
            errorMessage = 'Server error occurred';
          }
        } else {
          errorMessage = 'Server error occurred';
        }
      } else if (e.response?.data != null) {
        // Try to extract error message from response for other status codes
        final responseData = e.response!.data;
        if (responseData['error'] != null) {
          errorMessage = responseData['error'];
        } else if (responseData['message'] != null) {
          errorMessage = responseData['message'];
        }
      }

      return BuyNowVoucherApplicationResult(
        success: false,
        discountAmount: 0.0,
        finalAmount: orderAmount,
        buyNowSession: null,
        voucherInfo: null,
        errorMessage: errorMessage,
      );
    } catch (e) {
      debugPrint('[BuyNowVoucherService] Unexpected error applying voucher: $e');
      return BuyNowVoucherApplicationResult(
        success: false,
        discountAmount: 0.0,
        finalAmount: orderAmount,
        buyNowSession: null,
        voucherInfo: null,
        errorMessage: 'Unexpected error: $e',
      );
    }
  }

  /// Remove voucher from buy now session
  Future<BuyNowVoucherRemovalResult> removeVoucherFromBuyNow({
    required String authToken,
  }) async {
    try {
      debugPrint('[BuyNowVoucherService] Removing voucher from buy now session');
      
      final response = await _dio.delete(
        '/buy_now/remove_voucher',
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('[BuyNowVoucherService] Remove voucher response: ${response.statusCode}');
      debugPrint('[BuyNowVoucherService] Response data: ${response.data}');

      if (response.statusCode == 200 && response.data['data'] != null) {
        final responseData = response.data['data'] as Map<String, dynamic>;
        
        return BuyNowVoucherRemovalResult(
          success: true,
          buyNowSession: responseData,
          errorMessage: null,
        );
      } else {
        return BuyNowVoucherRemovalResult(
          success: false,
          buyNowSession: null,
          errorMessage: response.data['message'] ?? 'Failed to remove voucher',
        );
      }
    } on DioException catch (e) {
      debugPrint('[BuyNowVoucherService] DioException removing voucher: ${e.message}');
      debugPrint('[BuyNowVoucherService] Response data: ${e.response?.data}');

      String errorMessage = 'Network error removing voucher';

      // Try to extract error message from response
      if (e.response?.data != null) {
        final responseData = e.response!.data;

        // Check for 'error' field first
        if (responseData['error'] != null) {
          errorMessage = responseData['error'];
        }
        // Check for 'message' field as fallback
        else if (responseData['message'] != null) {
          errorMessage = responseData['message'];
        }
      }

      return BuyNowVoucherRemovalResult(
        success: false,
        buyNowSession: null,
        errorMessage: errorMessage,
      );
    } catch (e) {
      debugPrint('[BuyNowVoucherService] Unexpected error removing voucher: $e');
      return BuyNowVoucherRemovalResult(
        success: false,
        buyNowSession: null,
        errorMessage: 'Unexpected error: $e',
      );
    }
  }
}

/// Result of applying voucher to buy now session
class BuyNowVoucherApplicationResult {
  final bool success;
  final double discountAmount;
  final double finalAmount;
  final Map<String, dynamic>? buyNowSession;
  final Map<String, dynamic>? voucherInfo;
  final String? errorMessage;

  const BuyNowVoucherApplicationResult({
    required this.success,
    required this.discountAmount,
    required this.finalAmount,
    this.buyNowSession,
    this.voucherInfo,
    this.errorMessage,
  });
}

/// Result of removing voucher from buy now session
class BuyNowVoucherRemovalResult {
  final bool success;
  final Map<String, dynamic>? buyNowSession;
  final String? errorMessage;

  const BuyNowVoucherRemovalResult({
    required this.success,
    this.buyNowSession,
    this.errorMessage,
  });
}
