import '../../domain/entities/cart.dart';

/// Cart status enum
enum CartStatus {
  initial,
  loading,
  loaded,
  error,
}

/// Cart state class
class CartState {
  final CartStatus status;
  final Cart? cart;
  final String? error;
  final bool isAuthenticated;

  const CartState({
    this.status = CartStatus.initial,
    this.cart,
    this.error,
    this.isAuthenticated = false,
  });

  CartState copyWith({
    CartStatus? status,
    Cart? cart,
    String? error,
    bool? isAuthenticated,
    bool clearError = false,
  }) {
    return CartState(
      status: status ?? this.status,
      cart: cart ?? this.cart,
      error: clearError ? null : (error ?? this.error),
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartState &&
        other.status == status &&
        other.cart == cart &&
        other.error == error &&
        other.isAuthenticated == isAuthenticated;
  }

  @override
  int get hashCode {
    return status.hashCode ^
        cart.hashCode ^
        error.hashCode ^
        isAuthenticated.hashCode;
  }

  /// Convenience methods for accessing discount information

  /// Check if cart has any discount applied
  bool get hasDiscount => cart?.hasAnyDiscount ?? false;

  /// Get the effective discount amount
  double get discountAmount => cart?.effectiveDiscountAmount ?? 0.0;

  /// Get the effective discount code
  String? get discountCode => cart?.effectiveDiscountCode;

  /// Get the discount title (only available for Shopify discounts)
  String? get discountTitle => cart?.discountTitle;

  /// Check if this is a Shopify discount
  bool get hasShopifyDiscount => cart?.hasShopifyDiscount ?? false;

  /// Check if this is a legacy discount
  bool get hasLegacyDiscount => cart?.hasLegacyDiscount ?? false;

  /// Get the original total (before discount)
  double? get originalTotal => cart?.originalTotal;

  /// Get the total discount amount
  double? get totalDiscount => cart?.totalDiscount;

  @override
  String toString() {
    return 'CartState(status: $status, cart: $cart, error: $error, isAuthenticated: $isAuthenticated, hasDiscount: $hasDiscount)';
  }
}