import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/stripe_config.dart';
import '../entities/payment_result.dart';

/// Repository interface for Stripe payment operations
abstract class StripeRepository {
  /// Initialize Stripe with configuration from backend
  Future<Either<Failure, StripeConfig>> initializeStripe();

  /// Process payment using Stripe Payment Sheet
  Future<Either<Failure, PaymentResult>> processPayment({
    required String clientSecret,
    required String paymentIntentId,
  });

  /// Verify payment status from backend
  Future<Either<Failure, PaymentResult>> verifyPayment({
    required String paymentIntentId,
  });

  /// Handle payment sheet completion
  Future<Either<Failure, PaymentResult>> handlePaymentSheetResult({
    required String paymentIntentId,
    required bool isCompleted,
    String? error,
  });

  /// Get Stripe configuration from backend
  Future<Either<Failure, StripeConfig>> getStripeConfig();

  /// Refresh Stripe configuration
  Future<Either<Failure, StripeConfig>> refreshStripeConfig();
}
