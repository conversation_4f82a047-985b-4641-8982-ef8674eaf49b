import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// Custom payment form that matches Figma design using separate Stripe components
class FigmaPaymentForm extends StatefulWidget {
  final Function(bool)? onValidationChanged;

  const FigmaPaymentForm({
    super.key,
    this.onValidationChanged,
  });

  @override
  State<FigmaPaymentForm> createState() => _FigmaPaymentFormState();
}

class _FigmaPaymentFormState extends State<FigmaPaymentForm> {
  bool _isCardValid = false;
  late CardFormEditController _controller;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _controller = CardFormEditController();
    _controller.addListener(_updateValidation);
  }

  @override
  void dispose() {
    _isDisposed = true;
    _controller.removeListener(_updateValidation);
    _controller.dispose();
    super.dispose();
  }

  void _updateValidation() {
    if (_isDisposed) return; // Prevent updates after disposal

    try {
      final isValid = _controller.details.complete;
      if (_isCardValid != isValid && !_isDisposed) {
        setState(() {
          _isCardValid = isValid;
        });
        widget.onValidationChanged?.call(isValid);

        // Debug logging
        // debugPrint('[FigmaPaymentForm] Card form changed:');
        // debugPrint('  - Complete: ${_controller.details.complete}');
        // debugPrint('  - Valid Number: ${_controller.details.validNumber}');
        // debugPrint('  - Valid CVC: ${_controller.details.validCVC}');
        // debugPrint('  - Valid Expiry: ${_controller.details.validExpiryDate}');
        // debugPrint('  - Overall Valid: $isValid');

        // Platform-specific debug info
        // if (defaultTargetPlatform == TargetPlatform.iOS) {
        //   debugPrint('  - iOS Platform: Styling limitations apply');
        // } else if (defaultTargetPlatform == TargetPlatform.android) {
        //   debugPrint('  - Android Platform: Full styling support');
        // }
      }
    } catch (e) {
      // debugPrint('[FigmaPaymentForm] Error in validation: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity, // Full screen width
      margin: const EdgeInsets.symmetric(horizontal: 8), // Margin from screen edges
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Credit card brands section
          _buildCardBrandsSection(),

          const SizedBox(height: 8),

          // Stripe CardField styled to match Figma design
          _buildFigmaStyledFields(),
        ],
      ),
    );
  }

  Widget _buildCardBrandsSection() {
    return Container(
      width: double.infinity,
      height: 32, // Fixed height to match Figma
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF7ED), // Figma background color
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildCardBrand('assets/icons/visa.svg'),
          const SizedBox(width: 4),
          _buildCardBrand('assets/icons/mastercard.svg'),
          const SizedBox(width: 4),
          _buildCardBrand('assets/icons/amex.svg'),
          const SizedBox(width: 4),
          _buildCardBrand('assets/icons/discover.svg'),
        ],
      ),
    );
  }

  Widget _buildCardBrand(String assetPath) {
    return Container(
      width: 24,
      height: 16,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(2),
        border: Border.all(
          color: Colors.black.withValues(alpha: 0.2),
          width: 0.5,
        ),
      ),
      child: Center(
        child: SvgPicture.asset(
          assetPath,
          width: 20,
          height: 12,
          placeholderBuilder: (context) {
            // Fallback if SVG doesn't exist
            return Container(
              width: 20,
              height: 12,
              color: Colors.grey[300],
            );
          },
        ),
      ),
    );
  }

  Widget _buildFigmaStyledFields() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
      ),
      child: CardFormField(
        key: const ValueKey('payment_card_form'), // Stable key to prevent recreation
        controller: _controller,
        style: _buildCardFormStyle(),
        enablePostalCode: true, // Enable postal code to get country support
        countryCode: 'US', // Set default country - Stripe will support all its countries
        onCardChanged: (card) {
          if (_isDisposed) return; // Prevent callbacks after disposal

          // debugPrint('[CardFormField] Card changed - Platform: ${defaultTargetPlatform.name}');
          // debugPrint('[CardFormField] Card details: ${card?.complete == true ? 'Complete' : 'Incomplete'}');

          // Log enhanced contrast styling approach
          // debugPrint('  - Styling: Light gray background with black text and visible dark placeholders');
          // debugPrint('  - Platform: ${defaultTargetPlatform.name} (enhanced contrast for better visibility)');
          // debugPrint('  - Readability: Optimized placeholder visibility for all devices');

        },
      ),
    );
  }

  /// Build platform-aware CardFormStyle with advanced iOS fixes
  CardFormStyle _buildCardFormStyle() {
    // IMPROVED CONTRAST APPROACH FOR BETTER PLACEHOLDER VISIBILITY
    return CardFormStyle(
      backgroundColor: const Color.fromARGB(255, 43, 43, 43), // Very light gray background for better contrast
      borderColor: const Color(0xFF000000), // Black border for contrast
      borderWidth: 2,
      borderRadius: 8,
      textColor: const Color(0xFF000000), // Black text
      fontSize: 16,
      placeholderColor: const Color(0xFF495057), // Dark gray placeholder (more visible)
      cursorColor: const Color(0xFF000000), // Black cursor
      textErrorColor: const Color(0xFFDC3545), // Red errors
    );
  }
}
