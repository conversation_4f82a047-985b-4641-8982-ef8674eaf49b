import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/network/api_config.dart';
import '../../../../core/usecases/usecase.dart';
import '../../data/datasources/order_data_source.dart';
import '../../data/repositories/order_repository_impl.dart';
import '../../domain/repositories/order_repository.dart';
import '../../domain/usecases/get_orders_usecase.dart';
import '../../domain/entities/order.dart' as entities;

/// Provider for order data source
final orderDataSourceProvider = Provider<OrderDataSource>((ref) {
  final dio = ApiConfig.createDioClient();
  return OrderDataSourceImpl(dio: dio, ref: ref);
});

/// Provider for order repository
final orderRepositoryProvider = Provider<OrderRepository>((ref) {
  final dataSource = ref.read(orderDataSourceProvider);
  return OrderRepositoryImpl(dataSource: dataSource);
});

/// Provider for get orders use case
final getOrdersUseCaseProvider = Provider<GetOrdersUseCase>((ref) {
  final repository = ref.read(orderRepositoryProvider);
  return GetOrdersUseCase(repository);
});

/// Provider for get order by ID use case
final getOrderByIdUseCaseProvider = Provider<GetOrderByIdUseCase>((ref) {
  final repository = ref.read(orderRepositoryProvider);
  return GetOrderByIdUseCase(repository);
});

/// State for orders list
class OrdersState {
  final List<entities.Order> orders;
  final bool isLoading;
  final String? error;

  const OrdersState({
    this.orders = const [],
    this.isLoading = false,
    this.error,
  });

  OrdersState copyWith({
    List<entities.Order>? orders,
    bool? isLoading,
    String? error,
  }) {
    return OrdersState(
      orders: orders ?? this.orders,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Provider for orders state
final ordersProvider = StateNotifierProvider<OrdersNotifier, OrdersState>((ref) {
  final getOrdersUseCase = ref.read(getOrdersUseCaseProvider);
  return OrdersNotifier(getOrdersUseCase);
});

/// Notifier for managing orders state
class OrdersNotifier extends StateNotifier<OrdersState> {
  final GetOrdersUseCase _getOrdersUseCase;

  OrdersNotifier(this._getOrdersUseCase) : super(const OrdersState());

  /// Fetch orders from the API
  Future<void> fetchOrders() async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await _getOrdersUseCase(NoParams());

    result.fold(
      (failure) => state = state.copyWith(
        isLoading: false,
        error: failure.message,
      ),
      (orders) => state = state.copyWith(
        isLoading: false,
        orders: orders,
        error: null,
      ),
    );
  }

  /// Refresh orders
  Future<void> refreshOrders() async {
    await fetchOrders();
  }

  /// Set loading state manually
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading, error: null);
  }
}

/// State for single order
class OrderState {
  final entities.Order? order;
  final bool isLoading;
  final String? error;

  const OrderState({
    this.order,
    this.isLoading = false,
    this.error,
  });

  OrderState copyWith({
    entities.Order? order,
    bool? isLoading,
    String? error,
  }) {
    return OrderState(
      order: order ?? this.order,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Provider for single order state
final orderProvider = StateNotifierProvider.family<OrderNotifier, OrderState, String>((ref, orderId) {
  final getOrderByIdUseCase = ref.read(getOrderByIdUseCaseProvider);
  return OrderNotifier(getOrderByIdUseCase, orderId);
});

/// Notifier for managing single order state
class OrderNotifier extends StateNotifier<OrderState> {
  final GetOrderByIdUseCase _getOrderByIdUseCase;
  final String _orderId;

  OrderNotifier(this._getOrderByIdUseCase, this._orderId) : super(const OrderState());

  /// Set loading state manually
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading, error: null);
  }

  /// Set error state manually
  void setError(String error) {
    state = state.copyWith(isLoading: false, error: error);
  }

  /// Fetch order by ID
  Future<void> fetchOrder() async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await _getOrderByIdUseCase(GetOrderByIdParams(orderId: _orderId));

    result.fold(
      (failure) => state = state.copyWith(
        isLoading: false,
        error: failure.message,
      ),
      (order) => state = state.copyWith(
        isLoading: false,
        order: order,
        error: null,
      ),
    );
  }
}
