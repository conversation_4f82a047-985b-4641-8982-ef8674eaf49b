import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../network/backend_auth_service.dart';
import '../../../features/auth/presentation/providers/auth_providers.dart';
import '../../../features/auth/data/models/enhanced_user_model.dart';
import '../../../features/auth/domain/entities/backend_auth_result.dart';
import '../models/client_models.dart';

/// Token provider implementation using existing auth system
class TravelGatorTokenProvider implements TokenProvider {
  final Ref ref;
  final BackendAuthService _authService;

  // Token caching to reduce redundant verification calls
  String? _cachedToken;
  DateTime? _cacheTimestamp;
  static const Duration _cacheExpiry = Duration(minutes: 5);

  TravelGatorTokenProvider({
    required this.ref,
    BackendAuthService? authService,
  }) : _authService = authService ?? BackendAuthService();

  @override
  Future<String?> getToken() async {
    try {
      final authState = ref.read(authNotifierProvider);
      final user = authState.maybeMap(
        authenticated: (state) => state.user as EnhancedUserModel?,
        orElse: () => null,
      );

      if (user == null) {
        if (kDebugMode) {
          debugPrint('[TokenProvider] No authenticated user found');
        }
        _clearCache();
        return null;
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        if (kDebugMode) {
          debugPrint('[TokenProvider] User does not have backend authentication');
        }
        _clearCache();
        return null;
      }

      // Check if we have a valid cached token
      if (_isCacheValid() && _cachedToken == user.backendToken) {
        if (kDebugMode) {
          debugPrint('[TokenProvider] Using cached token (valid for ${_getRemainingCacheTime().inSeconds}s)');
        }
        return user.backendToken;
      }

      // Check if token is still valid (only if cache is expired or token changed)
      final isValid = await isTokenValid();
      if (!isValid) {
        if (kDebugMode) {
          debugPrint('[TokenProvider] Token is invalid, attempting refresh');
        }
        _clearCache();
        await refreshToken();

        // Get updated token after refresh
        final updatedAuthState = ref.read(authNotifierProvider);
        final newToken = updatedAuthState.maybeMap(
          authenticated: (state) => (state.user as EnhancedUserModel?)?.backendToken,
          orElse: () => null,
        );

        // Cache the new token if valid
        if (newToken != null) {
          _updateCache(newToken);
        }

        return newToken;
      }

      // Cache the valid token
      _updateCache(user.backendToken!);
      return user.backendToken;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('[TokenProvider] Error getting token: $e');
      }
      _clearCache();
      return null;
    }
  }

  @override
  Future<void> refreshToken() async {
    try {
      // Clear cache when refreshing token
      _clearCache();

      final authState = ref.read(authNotifierProvider);
      final user = authState.maybeMap(
        authenticated: (state) => state.user as EnhancedUserModel?,
        orElse: () => null,
      );

      if (user == null) {
        throw ClientException(
          message: 'No authenticated user found',
          type: ClientErrorType.authentication,
        );
      }

      if (user.token == null || user.token!.isEmpty) {
        throw ClientException(
          message: 'No Firebase ID token available',
          type: ClientErrorType.authentication,
        );
      }

      if (kDebugMode) {
        debugPrint('[TokenProvider] Refreshing backend token');
      }

      // Use existing auth service to refresh token
      final response = await _authService.authenticate(user.token!);

      if (response.success && response.backendToken != null) {
        // Update user with new backend token
        final newBackendAuthResult = BackendAuthResult.success(
          backendToken: response.backendToken,
          userData: user.backendAuthResult?.userData,
          additionalData: user.backendAuthResult?.additionalData,
        );

        user.copyWithBackendAuthResult(newBackendAuthResult);

        // Update auth state - we need to implement a way to update the auth state
        // For now, we'll skip this update as the method doesn't exist

        if (kDebugMode) {
          debugPrint('[TokenProvider] Backend token refreshed successfully');
        }
      } else {
        throw ClientException(
          message: 'Failed to refresh backend token',
          type: ClientErrorType.authentication,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('[TokenProvider] Error refreshing token: $e');
      }
      
      if (e is ClientException) {
        rethrow;
      }
      
      throw ClientException(
        message: 'Token refresh failed: $e',
        type: ClientErrorType.authentication,
        originalError: e,
      );
    }
  }

  @override
  Future<bool> isTokenValid() async {
    try {
      final authState = ref.read(authNotifierProvider);
      final user = authState.maybeMap(
        authenticated: (state) => state.user as EnhancedUserModel?,
        orElse: () => null,
      );

      if (user == null || user.backendToken == null || user.backendToken!.isEmpty) {
        return false;
      }

      // Use existing auth service to verify token
      final response = await _authService.verifyToken(user.backendToken!);
      return response.success;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('[TokenProvider] Token validation failed: $e');
      }
      return false;
    }
  }

  /// Check if cached token is still valid
  bool _isCacheValid() {
    if (_cachedToken == null || _cacheTimestamp == null) {
      return false;
    }

    final now = DateTime.now();
    final cacheAge = now.difference(_cacheTimestamp!);
    return cacheAge < _cacheExpiry;
  }

  /// Get remaining cache time
  Duration _getRemainingCacheTime() {
    if (_cacheTimestamp == null) {
      return Duration.zero;
    }

    final now = DateTime.now();
    final cacheAge = now.difference(_cacheTimestamp!);
    final remaining = _cacheExpiry - cacheAge;
    return remaining.isNegative ? Duration.zero : remaining;
  }

  /// Update token cache
  void _updateCache(String token) {
    _cachedToken = token;
    _cacheTimestamp = DateTime.now();

    if (kDebugMode) {
      debugPrint('[TokenProvider] Token cached for ${_cacheExpiry.inMinutes} minutes');
    }
  }

  /// Clear token cache
  void _clearCache() {
    _cachedToken = null;
    _cacheTimestamp = null;

    if (kDebugMode) {
      debugPrint('[TokenProvider] Token cache cleared');
    }
  }

  /// Get current authenticated user
  EnhancedUserModel? getCurrentUser() {
    final authState = ref.read(authNotifierProvider);
    return authState.maybeMap(
      authenticated: (state) => state.user as EnhancedUserModel?,
      orElse: () => null,
    );
  }

  /// Check if user has valid backend authentication
  bool hasValidBackendAuth() {
    final user = getCurrentUser();
    return user != null && 
           user.hasBackendAuth && 
           user.backendToken != null && 
           user.backendToken!.isNotEmpty;
  }

  /// Get user ID for API calls
  String? getUserId() {
    final user = getCurrentUser();
    return user?.id;
  }

  /// Check if user is authenticated
  bool isAuthenticated() {
    final authState = ref.read(authNotifierProvider);
    return authState.maybeMap(
      authenticated: (_) => true,
      orElse: () => false,
    );
  }
}

/// Authentication interceptor for HTTP requests
class AuthInterceptor {
  final TokenProvider tokenProvider;

  AuthInterceptor({required this.tokenProvider});

  /// Add authentication header to request
  Future<Map<String, String>> getAuthHeaders() async {
    final token = await tokenProvider.getToken();
    
    if (token == null) {
      throw ClientException(
        message: 'Authentication required',
        type: ClientErrorType.authentication,
      );
    }

    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }

  /// Handle authentication errors and retry with token refresh
  Future<Map<String, String>> handleAuthError() async {
    if (kDebugMode) {
      debugPrint('[AuthInterceptor] Handling authentication error, refreshing token');
    }

    try {
      await tokenProvider.refreshToken();
      return await getAuthHeaders();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('[AuthInterceptor] Token refresh failed: $e');
      }
      
      throw ClientException(
        message: 'Authentication failed. Please sign in again.',
        type: ClientErrorType.authentication,
        originalError: e,
      );
    }
  }

  /// Check if error is authentication related
  bool isAuthError(int? statusCode) {
    return statusCode == 401 || statusCode == 403;
  }
}

/// Provider for token provider
final tokenProviderProvider = Provider<TravelGatorTokenProvider>((ref) {
  return TravelGatorTokenProvider(ref: ref);
});

/// Provider for auth interceptor
final authInterceptorProvider = Provider<AuthInterceptor>((ref) {
  final tokenProvider = ref.watch(tokenProviderProvider);
  return AuthInterceptor(tokenProvider: tokenProvider);
});
