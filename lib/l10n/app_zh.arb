{"appTitle": "TravelGator", "@appTitle": {"description": "The title of the application"}, "welcomeMessage": "欢迎使用 TravelGator", "@welcomeMessage": {"description": "Welcome message on the home screen"}, "signIn": "登录", "@signIn": {"description": "Text for sign in button"}, "signUp": "注册", "@signUp": {"description": "Text for sign up button"}, "email": "邮箱", "@email": {"description": "Label for email field"}, "password": "密码", "@password": {"description": "Label for password field"}, "name": "姓名", "@name": {"description": "Label for name field"}, "forgotPassword": "忘记密码？", "@forgotPassword": {"description": "Text for forgot password link"}, "orContinueWith": "或继续使用", "@orContinueWith": {"description": "Text for social login separator"}, "dontHaveAccount": "还没有账户？", "@dontHaveAccount": {"description": "Text for sign up prompt"}, "alreadyHaveAccount": "已有账户？", "@alreadyHaveAccount": {"description": "Text for sign in prompt"}, "createAccount": "创建账户", "@createAccount": {"description": "Title for account creation screen"}, "getStarted": "开始使用", "@getStarted": {"description": "Text for get started button"}, "legalDocuments": "法律文件", "@legalDocuments": {"description": "Title for legal documents modal"}, "userAgreement": "用户协议", "@userAgreement": {"description": "Title for user agreement document"}, "privacyPolicy": "隐私政策", "@privacyPolicy": {"description": "Title for privacy policy document"}, "termsAndConditions": "条款与条件", "@termsAndConditions": {"description": "Title for terms and conditions document"}, "lastUpdated": "最后更新：{date}", "@lastUpdated": {"description": "Text showing when document was last updated", "placeholders": {"date": {"type": "String", "description": "The date when the document was last updated"}}}, "close": "关闭", "@close": {"description": "Text for close button"}, "languages": "语言", "@languages": {"description": "Title for languages screen"}, "selectLanguage": "选择语言", "@selectLanguage": {"description": "Header text for language selection"}, "languageDescription": "为应用界面选择您偏好的语言。", "@languageDescription": {"description": "Description text for language selection"}, "languageChangeNote": "当您选择新语言时，应用界面将立即更新。", "@languageChangeNote": {"description": "Note about language change behavior"}, "languageChanged": "语言已更改为{language}", "@languageChanged": {"description": "Confirmation message when language is changed", "placeholders": {"language": {"type": "String", "description": "The name of the selected language"}}}, "accountInformation": "账户信息", "@accountInformation": {"description": "Title for account information section"}, "transactionHistory": "交易记录", "@transactionHistory": {"description": "Title for transaction history section"}, "paymentSettings": "支付设置", "@paymentSettings": {"description": "Title for payment settings section"}, "currency": "货币", "@currency": {"description": "Title for currency section"}, "helpCenter": "帮助中心", "@helpCenter": {"description": "Title for help center section"}, "logout": "退出登录", "@logout": {"description": "Text for logout button"}, "logoutConfirmTitle": "退出登录", "@logoutConfirmTitle": {"description": "Title for logout confirmation dialog"}, "logoutConfirmMessage": "您确定要退出登录吗？", "@logoutConfirmMessage": {"description": "Message for logout confirmation dialog"}, "cancel": "取消", "@cancel": {"description": "Text for cancel button"}, "appVersion": "TravelGator 版本 {version}", "@appVersion": {"description": "App version text", "placeholders": {"version": {"type": "String", "description": "The version number of the app"}}}, "cart": "购物车", "@cart": {"description": "Cart screen title"}, "cartIsEmpty": "购物车为空", "@cartIsEmpty": {"description": "Message when cart has no items"}, "addSomeProducts": "添加一些产品开始购物", "@addSomeProducts": {"description": "Subtitle for empty cart"}, "startShopping": "开始购物", "@startShopping": {"description": "But<PERSON> text to start shopping"}, "authenticationRequired": "需要身份验证", "@authenticationRequired": {"description": "Title for authentication required error"}, "pleaseSignInToCart": "请登录以访问您的购物车", "@pleaseSignInToCart": {"description": "Message for authentication required"}, "unableToLoadCart": "无法加载购物车", "@unableToLoadCart": {"description": "Error message when cart fails to load"}, "failedToLoadCart": "加载购物车失败", "@failedToLoadCart": {"description": "Generic cart loading error message"}, "tryAgain": "重试", "@tryAgain": {"description": "Button text to retry an action"}, "total": "总计：", "@total": {"description": "Label for total price"}, "addToCart": "加入购物车", "@addToCart": {"description": "Button text to add item to cart"}, "buyNow": "立即购买", "@buyNow": {"description": "Button text to buy item immediately"}, "hideDetails": "隐藏详情", "@hideDetails": {"description": "Button text to hide product details"}, "remove": "移除", "@remove": {"description": "Button text to remove item"}, "itemRemovedFromCart": "商品已从购物车中移除", "@itemRemovedFromCart": {"description": "Success message when item is removed from cart"}, "noVariantsAvailable": "此产品没有可用的变体", "@noVariantsAvailable": {"description": "Error message when product has no variants"}, "failedToAddToCart": "添加到购物车失败：{error}", "@failedToAddToCart": {"description": "Error message when adding to cart fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "gb": "GB", "@gb": {"description": "Gigabyte unit abbreviation"}, "day": "天", "@day": {"description": "Day unit for duration"}, "days": "天", "@days": {"description": "Days unit for duration (plural)"}, "signedInSuccessfully": "登录成功！", "@signedInSuccessfully": {"description": "Success message when user signs in"}, "signInFailed": "登录失败：{error}", "@signInFailed": {"description": "Error message when sign-in fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "retry": "重试", "@retry": {"description": "Button text to retry an action"}, "orderCompletedSuccessfully": "🎉 订单完成成功！", "@orderCompletedSuccessfully": {"description": "Success message when order is completed"}, "checkoutFailed": "❌ 结账失败：{error}", "@checkoutFailed": {"description": "Error message when checkout fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "buyNowFailed": "❌ 立即购买失败：{error}", "@buyNowFailed": {"description": "Error message when buy now fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "processingCheckout": "正在处理结账...", "@processingCheckout": {"description": "Message shown while processing checkout"}, "checkOut": "结账", "@checkOut": {"description": "Checkout button text"}, "proceedToCheckout": "进行结账", "@proceedToCheckout": {"description": "Proceed to checkout button text"}, "home": "首页", "@home": {"description": "Home tab label in navigation bar"}, "account": "账户", "@account": {"description": "Account tab label in navigation bar"}, "sims": "SIM卡", "@sims": {"description": "SIMs tab label in navigation bar"}, "travelgator": "TravelGator", "@travelgator": {"description": "TravelGator tab label and screen title"}, "rewards": "奖励", "@rewards": {"description": "Rewards tab label in navigation bar"}, "travelgatorScreen": "TravelGator 屏幕", "@travelgatorScreen": {"description": "TravelGator screen content title"}, "welcomeToTravelgator": "欢迎使用\nTravelGator", "@welcomeToTravelgator": {"description": "Welcome title in onboarding"}, "stayConnected": "保持连接", "@stayConnected": {"description": "Stay connected title in onboarding"}, "stayConnectedGlobally": "全球\n保持连接", "@stayConnectedGlobally": {"description": "Stay connected globally title"}, "discoverExplore": "轻松\n发现与探索", "@discoverExplore": {"description": "Discover and explore title"}, "discoverAmazingDestinations": "发现令人惊叹的\n目的地", "@discoverAmazingDestinations": {"description": "Discover amazing destinations title"}, "details": "详情", "@details": {"description": "Details screen title"}, "yourPurchase": "您的购买", "@yourPurchase": {"description": "Your purchase section title"}, "yourData": "您的数据", "@yourData": {"description": "Your data section title"}, "newESim": "新 eSIM", "@newESim": {"description": "New eSIM option label"}, "topUp": "充值", "@topUp": {"description": "Top up option label"}, "totalPayment": "总付款", "@totalPayment": {"description": "Total payment label"}, "seeDetails": "查看详情", "@seeDetails": {"description": "See details button text"}, "selectYourTravelgatorESim": "选择您的 TravelGator eSIM", "@selectYourTravelgatorESim": {"description": "Default option for eSIM selection"}, "dataAmount": "数据量", "@dataAmount": {"description": "Data amount selection label"}, "duration": "持续时间", "@duration": {"description": "Duration selection label"}, "quantity": "数量", "@quantity": {"description": "Quantity selection label"}, "eSimType": "eSIM 类型", "@eSimType": {"description": "eSIM type selection label"}, "reviewYourPurchase": "查看您的购买", "@reviewYourPurchase": {"description": "Title for purchase review popup"}, "yourDataLabel": "您的数据", "@yourDataLabel": {"description": "Label for data amount dropdown"}, "dayLabel": "天", "@dayLabel": {"description": "Label for day duration dropdown"}, "oneDay": "1 天", "@oneDay": {"description": "One day option"}, "threeDays": "3 天", "@threeDays": {"description": "Three days option"}, "sevenDays": "7 天", "@sevenDays": {"description": "Seven days option"}, "fifteenDays": "15 天", "@fifteenDays": {"description": "Fifteen days option"}, "thirtyDays": "30 天", "@thirtyDays": {"description": "Thirty days option"}, "removeItemConfirmation": "您确定要从购物车中移除 \"{itemTitle}\" 吗？", "@removeItemConfirmation": {"description": "Confirmation message for removing item from cart", "placeholders": {"itemTitle": {"type": "String", "description": "The title of the item being removed"}}}, "checkoutFailedTitle": "结账失败", "@checkoutFailedTitle": {"description": "Title for checkout failed error"}, "checkoutFailedMessage": "处理您的结账时出现问题。这可能是 Shopify 后端配置问题。", "@checkoutFailedMessage": {"description": "Detailed message for checkout failure"}, "checkoutErrorTitle": "结账错误", "@checkoutErrorTitle": {"description": "Title for checkout error"}, "deleteMyAccount": "删除我的账户", "@deleteMyAccount": {"description": "Text for delete account button"}, "deleteAccountConfirmTitle": "删除账户", "@deleteAccountConfirmTitle": {"description": "Title for delete account confirmation dialog"}, "deleteAccountConfirmMessage": "您确定要删除您的账户吗？这将永久删除您在我们系统中的所有数据。", "@deleteAccountConfirmMessage": {"description": "Message for delete account confirmation dialog"}, "delete": "删除", "@delete": {"description": "Text for delete button"}, "accountDeletionFailed": "账户删除失败", "@accountDeletionFailed": {"description": "Error message when account deletion fails"}, "accountDeletedSuccessfully": "账户删除成功", "@accountDeletedSuccessfully": {"description": "Success message when account is deleted"}, "cannotDeleteAccount": "无法删除账户", "@cannotDeleteAccount": {"description": "Error message when account cannot be deleted"}, "checkingAccountDeletion": "正在检查是否可以删除账户...", "@checkingAccountDeletion": {"description": "Loading message when checking account deletion eligibility"}, "deletingAccount": "正在删除账户...", "@deletingAccount": {"description": "Loading message when deleting account"}, "serverErrorContactSupport": "服务器错误。请稍后重试或联系客服寻求帮助。", "@serverErrorContactSupport": {"description": "Error message for server errors during account deletion"}, "errorLoadingProducts": "加载产品时出错", "@errorLoadingProducts": {"description": "Error message when products fail to load"}, "noProductsFound": "未找到产品", "@noProductsFound": {"description": "Message when no products match the filter"}, "tryAdjustingFilter": "请尝试调整您的筛选条件", "@tryAdjustingFilter": {"description": "Suggestion when no products are found"}, "productAddedToCart": "{productTitle} 已添加到购物车！", "@productAddedToCart": {"description": "Success message when product is added to cart", "placeholders": {"productTitle": {"type": "String", "description": "The title of the product added"}}}, "cartOperationMayHaveFailed": "购物车操作可能失败。请检查您的购物车。", "@cartOperationMayHaveFailed": {"description": "Warning message when cart operation status is unclear"}, "processingAddToCart": "正在添加到购物车...", "@processingAddToCart": {"description": "Loading message when adding item to cart"}, "from": "从", "@from": {"description": "Prefix for price display, e.g. 'From $10.99'"}, "minPrice": "最低价格", "@minPrice": {"description": "Label for minimum price filter"}, "maxPrice": "最高价格", "@maxPrice": {"description": "Label for maximum price filter"}, "applyFilter": "应用筛选", "@applyFilter": {"description": "Button text to apply price filter"}, "clearFilter": "清除筛选", "@clearFilter": {"description": "Button text to clear price filter"}, "priceFilter": "价格筛选", "@priceFilter": {"description": "Title for price filter section"}, "under": "低于", "@under": {"description": "Prefix for price range, e.g. 'Under $5'"}, "over": "高于", "@over": {"description": "Prefix for price range, e.g. 'Over $50'"}, "orSetCustomRange": "或设置自定义范围：", "@orSetCustomRange": {"description": "Label for custom price range section"}, "to": "至", "@to": {"description": "Connector word between min and max price, e.g. '$5 to $10'"}, "countryVietnam": "越南", "@countryVietnam": {"description": "Country name: Vietnam"}, "countryChina": "中国", "@countryChina": {"description": "Country name: China"}, "countryUnitedStates": "美国", "@countryUnitedStates": {"description": "Country name: United States"}, "countryNewZealand": "新西兰", "@countryNewZealand": {"description": "Country name: New Zealand"}, "regionAsean": "东盟", "@regionAsean": {"description": "Region name: ASEAN"}, "regionEastAsia": "东亚", "@regionEastAsia": {"description": "Region name: East Asia"}, "regionNorthAmerica": "北美", "@regionNorthAmerica": {"description": "Region name: North America"}, "regionOceania": "大洋洲", "@regionOceania": {"description": "Region name: Oceania"}, "eSIMRoamingData": "eSIM漫游数据", "@eSIMRoamingData": {"description": "Description text for eSIM products"}, "countries": "国家", "@countries": {"description": "Tab label for countries filter"}, "regions": "地区", "@regions": {"description": "Tab label for regions filter"}, "authenticationSuccessful": "认证成功！", "@authenticationSuccessful": {"description": "Message shown when user successfully authenticates"}}