import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/discount_stats.dart';
import '../../domain/repositories/order_repository.dart';
import '../../data/repositories/order_repository_impl.dart';
import '../../data/datasources/order_data_source.dart';
import '../../../../core/network/api_config.dart';

/// Provider for order data source
final orderDataSourceProvider = Provider<OrderDataSource>((ref) {
  return OrderDataSourceImpl(
    dio: ApiConfig.createDioClient(),
    ref: ref,
  );
});

/// Provider for order repository
final orderRepositoryProvider = Provider<OrderRepository>((ref) {
  return OrderRepositoryImpl(
    dataSource: ref.watch(orderDataSourceProvider),
  );
});

/// Provider for discount statistics
final discountStatsProvider = FutureProvider<DiscountStats>((ref) async {
  final repository = ref.watch(orderRepositoryProvider);
  final result = await repository.getDiscountStats();
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (stats) => stats,
  );
});
