import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../l10n/app_localizations.dart';
import '../../domain/entities/legal_document_type.dart';
import '../providers/legal_documents_providers.dart';
import 'legal_document_content.dart';
import 'legal_document_tabs.dart';

/// Modal widget for displaying legal documents with tab navigation
class LegalDocumentsModal extends ConsumerStatefulWidget {
  final LegalDocumentType? initialDocumentType;

  const LegalDocumentsModal({
    super.key,
    this.initialDocumentType,
  });

  @override
  ConsumerState<LegalDocumentsModal> createState() => _LegalDocumentsModalState();
}

class _LegalDocumentsModalState extends ConsumerState<LegalDocumentsModal>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  bool _isClosing = false;

  @override
  void initState() {
    super.initState();
    
    // Set initial document type if provided
    if (widget.initialDocumentType != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(selectedLegalDocumentTypeProvider.notifier).state = 
            widget.initialDocumentType!;
      });
    }

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _closeModal() async {
    if (_isClosing) return;
    _isClosing = true;

    try {
      await _animationController.reverse();
      if (mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('[LegalDocumentsModal] Error closing modal: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: _closeModal,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black.withValues(alpha: 0.8 * _fadeAnimation.value),
              child: Align(
                alignment: Alignment.bottomCenter,
                child: GestureDetector(
                  onTap: () {}, // Prevent tap from propagating to background
                  child: Transform.translate(
                    offset: Offset(0, MediaQuery.of(context).size.height * _slideAnimation.value),
                    child: Container(
                      width: double.infinity,
                      constraints: BoxConstraints(
                        maxHeight: MediaQuery.of(context).size.height * 0.85,
                        minHeight: MediaQuery.of(context).size.height * 0.6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 20,
                            offset: const Offset(0, -10),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: Semantics(
                          label: 'Legal Documents Modal',
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Handle bar
                              Semantics(
                                label: 'Drag handle',
                                child: Container(
                                  margin: const EdgeInsets.only(top: 12),
                                  width: 40,
                                  height: 4,
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFE5E7EB),
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                              ),

                              // Header with title and close button
                              _buildHeader(),

                              // Tab navigation
                              const LegalDocumentTabs(),

                              // Content area
                              const Expanded(
                                child: LegalDocumentContent(),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    final l10n = AppLocalizations.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Row(
        children: [
          Expanded(
            child: Text(
              l10n.legalDocuments,
              style: GoogleFonts.inter(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF111827),
              ),
            ),
          ),
          // Close button
          Semantics(
            label: 'Close legal documents modal',
            button: true,
            child: IconButton(
              onPressed: _closeModal,
              icon: const Icon(
                Icons.close,
                color: Color(0xFF6B7280),
                size: 24,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 24,
                minHeight: 24,
              ),
              splashRadius: 20,
            ),
          ),
        ],
      ),
    );
  }
}
