import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/network/api_config.dart';
import '../../data/datasources/stripe_data_source.dart';
import '../../data/repositories/stripe_repository_impl.dart';
import '../../domain/repositories/stripe_repository.dart';
import '../../domain/entities/stripe_config.dart';

/// Provider for Stripe data source
final stripeDataSourceProvider = Provider<StripeDataSource>((ref) {
  return StripeDataSourceImpl(dio: ApiConfig.createDioClient());
});

/// Provider for Stripe repository
final stripeRepositoryProvider = Provider<StripeRepository>((ref) {
  return StripeRepositoryImpl(
    dataSource: ref.read(stripeDataSourceProvider),
    ref: ref,
  );
});

/// Provider for Stripe configuration
final stripeConfigProvider = FutureProvider<StripeConfig>((ref) async {
  final repository = ref.read(stripeRepositoryProvider);
  final result = await repository.initializeStripe();
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (config) => config,
  );
});

/// Provider for Stripe initialization status
final stripeInitializationProvider = StateNotifierProvider<StripeInitializationNotifier, StripeInitializationState>((ref) {
  return StripeInitializationNotifier(ref);
});

/// State for Stripe initialization
class StripeInitializationState {
  final bool isInitialized;
  final bool isLoading;
  final String? error;
  final StripeConfig? config;

  const StripeInitializationState({
    this.isInitialized = false,
    this.isLoading = false,
    this.error,
    this.config,
  });

  StripeInitializationState copyWith({
    bool? isInitialized,
    bool? isLoading,
    String? error,
    StripeConfig? config,
  }) {
    return StripeInitializationState(
      isInitialized: isInitialized ?? this.isInitialized,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      config: config ?? this.config,
    );
  }
}

/// Notifier for Stripe initialization
class StripeInitializationNotifier extends StateNotifier<StripeInitializationState> {
  final Ref _ref;

  StripeInitializationNotifier(this._ref) : super(const StripeInitializationState());

  /// Initialize Stripe
  Future<void> initialize() async {
    if (state.isInitialized) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final repository = _ref.read(stripeRepositoryProvider);
      final result = await repository.initializeStripe();

      result.fold(
        (failure) {
          debugPrint('[StripeInitialization] ❌ Initialization failed: ${failure.message}');
          state = state.copyWith(
            isLoading: false,
            error: failure.message,
            isInitialized: false,
          );
        },
        (config) {
          debugPrint('[StripeInitialization] ✅ Initialization successful');
          state = state.copyWith(
            isLoading: false,
            error: null,
            isInitialized: true,
            config: config,
          );
        },
      );
    } catch (e) {
      debugPrint('[StripeInitialization] ❌ Unexpected error: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to initialize Stripe: $e',
        isInitialized: false,
      );
    }
  }

  /// Force re-initialization (ignores current state)
  Future<void> forceInitialize() async {
    debugPrint('[StripeInitialization] 🔄 Force re-initializing Stripe...');
    state = state.copyWith(isLoading: true, error: null, isInitialized: false);

    try {
      final repository = _ref.read(stripeRepositoryProvider);
      final result = await repository.initializeStripe();

      result.fold(
        (failure) {
          debugPrint('[StripeInitialization] ❌ Force initialization failed: ${failure.message}');
          state = state.copyWith(
            isLoading: false,
            error: failure.message,
            isInitialized: false,
          );
        },
        (config) {
          debugPrint('[StripeInitialization] ✅ Force initialization successful');
          state = state.copyWith(
            isLoading: false,
            error: null,
            isInitialized: true,
            config: config,
          );
        },
      );
    } catch (e) {
      debugPrint('[StripeInitialization] ❌ Force initialization unexpected error: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to initialize Stripe: $e',
        isInitialized: false,
      );
    }
  }

  /// Refresh Stripe configuration
  Future<void> refresh() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final repository = _ref.read(stripeRepositoryProvider);
      final result = await repository.refreshStripeConfig();

      result.fold(
        (failure) {
          state = state.copyWith(
            isLoading: false,
            error: failure.message,
          );
        },
        (config) {
          state = state.copyWith(
            isLoading: false,
            error: null,
            config: config,
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to refresh Stripe config: $e',
      );
    }
  }

  /// Reset initialization state
  void reset() {
    state = const StripeInitializationState();
  }
}
