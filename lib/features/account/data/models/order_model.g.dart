// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderModel _$OrderModelFromJson(Map<String, dynamic> json) => OrderModel(
  id: json['id'] as String,
  totalPrice: (json['total_price'] as num?)?.toDouble() ?? 0.0,
  originalTotal: (json['original_total'] as num?)?.toDouble(),
  currency: json['currency'] as String? ?? 'USD',
  status: json['status'] as String? ?? 'pending',
  paymentStatus: json['payment_status'] as String? ?? 'pending',
  voucherCode: json['voucher_code'] as String?,
  discountAmount: (json['discount_amount'] as num?)?.toDouble() ?? 0.0,
  items:
      (json['items'] as List<dynamic>?)
          ?.map((e) => OrderItemModel.fromJson(e as Map<String, dynamic>))
          .toList() ??
      [],
  createdAt: json['created_at'] as String,
);

Map<String, dynamic> _$OrderModelToJson(OrderModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'total_price': instance.totalPrice,
      'original_total': instance.originalTotal,
      'currency': instance.currency,
      'status': instance.status,
      'payment_status': instance.paymentStatus,
      'voucher_code': instance.voucherCode,
      'discount_amount': instance.discountAmount,
      'items': instance.items,
      'created_at': instance.createdAt,
    };

OrderItemModel _$OrderItemModelFromJson(Map<String, dynamic> json) =>
    OrderItemModel(
      variantId: json['variant_id'] as String? ?? '',
      quantity: (json['quantity'] as num?)?.toInt() ?? 1,
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
      title: json['title'] as String? ?? 'Unknown Product',
      imageUrl: json['image_url'] as String?,
    );

Map<String, dynamic> _$OrderItemModelToJson(OrderItemModel instance) =>
    <String, dynamic>{
      'variant_id': instance.variantId,
      'quantity': instance.quantity,
      'price': instance.price,
      'title': instance.title,
      'image_url': instance.imageUrl,
    };

OrdersResponse _$OrdersResponseFromJson(Map<String, dynamic> json) =>
    OrdersResponse(
      success: json['success'] as bool? ?? true,
      data:
          (json['data'] as List<dynamic>)
              .map((e) => OrderModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$OrdersResponseToJson(OrdersResponse instance) =>
    <String, dynamic>{'success': instance.success, 'data': instance.data};

OrderResponse _$OrderResponseFromJson(Map<String, dynamic> json) =>
    OrderResponse(
      success: json['success'] as bool? ?? true,
      data: OrderModel.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$OrderResponseToJson(OrderResponse instance) =>
    <String, dynamic>{'success': instance.success, 'data': instance.data};
