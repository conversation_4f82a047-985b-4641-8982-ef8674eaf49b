import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../domain/models/purchase_config.dart';
import '../../../../l10n/app_localizations.dart';

class ESimTypeSelector extends StatelessWidget {
  final ESimType selectedType;
  final ValueChanged<ESimType> onChanged;

  const ESimTypeSelector({
    super.key,
    required this.selectedType,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          _buildOption(context, ESimType.newESim),
          const SizedBox(width: 24),
          // _buildOption(context, ESimType.topUp),
        ],
      ),
    );
  }

  Widget _buildOption(BuildContext context, ESimType type) {
    final isSelected = selectedType == type;

    return GestureDetector(
      onTap: () => onChanged(type),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? const Color(0xFFFF6982) : const Color(0xFFD1D5DB),
                width: 2,
              ),
              color: Colors.white,
            ),
            child: isSelected
                ? Center(
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Color(0xFFFF6982),
                      ),
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 8),
          Text(
            _getLocalizedDisplayName(context, type),
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF111827),
              height: 1.43,
            ),
          ),
        ],
      ),
    );
  }

  String _getLocalizedDisplayName(BuildContext context, ESimType type) {
    final l10n = AppLocalizations.of(context);
    switch (type) {
      case ESimType.newESim:
        return l10n.newESim;
      case ESimType.topUp:
        return l10n.topUp;
    }
  }
}
