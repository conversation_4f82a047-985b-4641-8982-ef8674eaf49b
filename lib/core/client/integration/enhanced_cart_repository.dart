import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/errors/failures.dart';
import '../../../features/cart/domain/entities/cart.dart';
import '../../../features/cart/domain/entities/checkout_item.dart';
import '../../../features/cart/domain/repositories/cart_repository.dart';
import '../models/client_models.dart';
import '../performance/cart_manager.dart';

/// Enhanced cart repository using the new client helper library
class EnhancedCartRepository implements CartRepository {
  final CartManager _cartManager;

  EnhancedCartRepository({required CartManager cartManager})
      : _cartManager = cartManager;

  @override
  Future<Either<Failure, Cart>> getCart() async {
    try {
      if (kDebugMode) {
        debugPrint('[EnhancedCartRepository] Getting cart');
      }

      final cartResponse = await _cartManager.getCart();
      final cart = _convertToCartEntity(cartResponse);
      return Right(cart);
    } on ClientException catch (e) {
      return Left(_convertClientExceptionToFailure(e));
    } catch (e) {
      if (kDebugMode) {
        debugPrint('[EnhancedCartRepository] Unexpected error: $e');
      }
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> addItem({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
    bool replaceIfExists = false,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('[EnhancedCartRepository] Adding item: $variantId');
      }

      final cartResponse = await _cartManager.scheduleUpdate(
        items: [
          CartItemUpdate(
            variantId: variantId,
            productId: productId,
            quantity: quantity,
            selected: true,
          ),
        ],
      );

      final cart = _convertToCartEntity(cartResponse);
      return Right(cart);
    } on ClientException catch (e) {
      return Left(_convertClientExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> updateItem({
    required String variantId,
    required int quantity,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('[EnhancedCartRepository] Updating item: $variantId to quantity $quantity');
      }

      final cartResponse = await _cartManager.updateQuantity(variantId, quantity);
      final cart = _convertToCartEntity(cartResponse);
      return Right(cart);
    } on ClientException catch (e) {
      return Left(_convertClientExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> updateItemComplete({
    required String oldVariantId,
    required String newVariantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('[EnhancedCartRepository] Updating item complete: $oldVariantId -> $newVariantId');
      }

      // First remove the old item
      await _cartManager.updateQuantity(oldVariantId, 0);

      // Then add the new item
      final cartResponse = await _cartManager.scheduleUpdate(
        items: [
          CartItemUpdate(
            variantId: newVariantId,
            productId: productId,
            quantity: quantity,
            selected: true,
          ),
        ],
      );

      final cart = _convertToCartEntity(cartResponse);
      return Right(cart);
    } on ClientException catch (e) {
      return Left(_convertClientExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> removeItem({required String variantId}) async {
    try {
      if (kDebugMode) {
        debugPrint('[EnhancedCartRepository] Removing item: $variantId');
      }

      final cartResponse = await _cartManager.updateQuantity(variantId, 0);
      final cart = _convertToCartEntity(cartResponse);
      return Right(cart);
    } on ClientException catch (e) {
      return Left(_convertClientExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> setPaymentMethod({required String paymentMethod}) async {
    try {
      if (kDebugMode) {
        debugPrint('[EnhancedCartRepository] Setting payment method: $paymentMethod');
      }

      final cartResponse = await _cartManager.setPaymentMethod(paymentMethod);
      final cart = _convertToCartEntity(cartResponse);
      return Right(cart);
    } on ClientException catch (e) {
      return Left(_convertClientExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  /// Convert CartResponse to Cart entity
  Cart _convertToCartEntity(CartResponse cartResponse) {
    final items = cartResponse.items.map((item) => CartItem(
      variantId: item.variantId,
      productId: item.productId,
      title: item.title,
      price: item.price,
      quantity: item.quantity,
      selected: item.selected,
      imageUrl: null, // Not provided in new API response
    )).toList();

    return Cart(
      id: cartResponse.id,
      items: items,
      totalPrice: cartResponse.totalPrice,
      currency: cartResponse.currency,
      itemsCount: cartResponse.itemsCount,
      selectedItemsCount: cartResponse.items.where((item) => item.selected).length,
      selectedTotalPrice: cartResponse.items
          .where((item) => item.selected)
          .fold<double>(0.0, (sum, item) => sum + (item.price * item.quantity)),
      allItemsSelected: cartResponse.items.isNotEmpty && 
          cartResponse.items.every((item) => item.selected),
      anyItemsSelected: cartResponse.items.any((item) => item.selected),
      // Enhanced discount fields
      originalTotal: cartResponse.subtotal,
      totalDiscount: cartResponse.discountAmount,
      hasDiscount: cartResponse.hasDiscount,
      // Voucher compatibility fields for older payloads
      voucherCode: cartResponse.voucherCode,
      discountAmount: cartResponse.discountAmount,
      discountedTotal: cartResponse.totalPrice,
      hasVoucher: cartResponse.hasDiscount && cartResponse.voucherCode != null,
      // Payment preferences
      paymentPreferences: cartResponse.preferredPaymentMethod != null
          ? PaymentPreferences(
              preferredMethod: cartResponse.preferredPaymentMethod!,
              availableMethods: const ['stripe', 'shopify'], // Default available methods
            )
          : null,
    );
  }

  /// Convert ClientException to Failure
  Failure _convertClientExceptionToFailure(ClientException e) {
    switch (e.type) {
      case ClientErrorType.authentication:
        return AuthFailure(message: e.message);
      case ClientErrorType.network:
        return NetworkFailure(message: e.message);
      case ClientErrorType.validation:
        return ValidationFailure(message: e.message);
      case ClientErrorType.server:
        return ServerFailure(message: e.message);
      case ClientErrorType.unknown:
        return UnknownFailure(message: e.message);
    }
  }

  // Compatibility stubs for retired checkout flows
  @override
  Future<Either<Failure, String>> checkout() async {
    // Method intentionally disabled in favor of unified checkout
    return const Left(UnknownFailure(message: 'This checkout method is no longer available. Use unified checkout instead.'));
  }

  @override
  Future<Either<Failure, String>> checkoutSelected({
    required List<String> selectedVariantIds,
  }) async {
    // Method intentionally disabled in favor of unified checkout
    return const Left(UnknownFailure(message: 'This checkout method is no longer available. Use unified checkout instead.'));
  }

  @override
  Future<Either<Failure, String>> checkoutSelectedWithQuantities({
    required List<CheckoutItem> items,
  }) async {
    // Method intentionally disabled in favor of unified checkout
    return const Left(UnknownFailure(message: 'This checkout method is no longer available. Use unified checkout instead.'));
  }

  @override
  Future<Either<Failure, PaymentMethodsResponse>> getPaymentMethods() async {
    // Return default payment methods
    return Right(PaymentMethodsResponse(
      paymentMethods: [
        const PaymentMethod(
          key: 'stripe',
          name: 'stripe',
          displayName: 'Stripe',
          available: true,
        ),
        const PaymentMethod(
          key: 'shopify',
          name: 'shopify',
          displayName: 'Shopify Payments',
          available: true,
        ),
      ],
      currentPreference: 'stripe',
    ));
  }

  @override
  Future<Either<Failure, CheckoutResponse>> unifiedCheckout({String? voucherCode}) async {
    // This would need to be implemented using the unified checkout endpoint
    return const Left(UnknownFailure(message: 'Unified checkout not implemented in this repository. Use payment service instead.'));
  }

  @override
  Future<Either<Failure, CheckoutResponse>> checkoutWithMethod({
    required String paymentMethod,
    String? voucherCode,
  }) async {
    // This would need to be implemented using the unified checkout endpoint
    return const Left(UnknownFailure(message: 'Checkout with method not implemented in this repository. Use payment service instead.'));
  }

  @override
  Future<Either<Failure, Cart>> updateItemSelection({
    required String variantId,
    required bool selected,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('[EnhancedCartRepository] Updating item selection: $variantId -> $selected');
      }

      // First get current cart to get the current quantity for this item
      final currentCart = await _cartManager.getCart();
      final currentItem = currentCart.items.firstWhere(
        (item) => item.variantId == variantId,
        orElse: () => throw ClientException(
          type: ClientErrorType.validation,
          message: 'Item not found in cart: $variantId',
        ),
      );

      // Use scheduleUpdate with selection parameter and current quantity
      final cartResponse = await _cartManager.scheduleUpdate(
        items: [
          CartItemUpdate(
            variantId: variantId,
            quantity: currentItem.quantity,
            selected: selected,
          ),
        ],
      );
      final cart = _convertToCartEntity(cartResponse);
      return Right(cart);
    } on ClientException catch (e) {
      return Left(_convertClientExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> updateMultipleItemsSelection({
    required List<String> variantIds,
    required bool selected,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('[EnhancedCartRepository] Updating multiple items selection: ${variantIds.length} items -> $selected');
      }

      // First get current cart to get current quantities
      final currentCart = await _cartManager.getCart();
      final items = variantIds.map((variantId) {
        final currentItem = currentCart.items.firstWhere(
          (item) => item.variantId == variantId,
          orElse: () => throw ClientException(
            type: ClientErrorType.validation,
            message: 'Item not found in cart: $variantId',
          ),
        );

        return CartItemUpdate(
          variantId: variantId,
          quantity: currentItem.quantity,
          selected: selected,
        );
      }).toList();

      final cartResponse = await _cartManager.scheduleUpdate(items: items);
      final cart = _convertToCartEntity(cartResponse);
      return Right(cart);
    } on ClientException catch (e) {
      return Left(_convertClientExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> selectAllItems() async {
    try {
      if (kDebugMode) {
        debugPrint('[EnhancedCartRepository] Selecting all items');
      }

      // First get current cart to get all variant IDs and quantities
      final currentCart = await _cartManager.getCart();
      final items = currentCart.items.map((item) => CartItemUpdate(
        variantId: item.variantId,
        quantity: item.quantity,
        selected: true,
      )).toList();

      final cartResponse = await _cartManager.scheduleUpdate(items: items);
      final cart = _convertToCartEntity(cartResponse);
      return Right(cart);
    } on ClientException catch (e) {
      return Left(_convertClientExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> unselectAllItems() async {
    try {
      if (kDebugMode) {
        debugPrint('[EnhancedCartRepository] Unselecting all items');
      }

      // First get current cart to get all variant IDs and quantities
      final currentCart = await _cartManager.getCart();
      final items = currentCart.items.map((item) => CartItemUpdate(
        variantId: item.variantId,
        quantity: item.quantity,
        selected: false,
      )).toList();

      final cartResponse = await _cartManager.scheduleUpdate(items: items);
      final cart = _convertToCartEntity(cartResponse);
      return Right(cart);
    } on ClientException catch (e) {
      return Left(_convertClientExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }
}

/// Provider for enhanced cart repository
final enhancedCartRepositoryProvider = Provider<EnhancedCartRepository>((ref) {
  final cartManager = ref.watch(cartManagerProvider);
  return EnhancedCartRepository(cartManager: cartManager);
});
