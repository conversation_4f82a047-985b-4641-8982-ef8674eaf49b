import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/utils/modal_manager.dart';
import '../providers/auth_providers.dart';
import '../widgets/auth_modal.dart';
import '../../data/models/enhanced_user_model.dart';

/// Service for handling authentication guards and modal presentation
class AuthGuardService {
  static final AuthGuardService _instance = AuthGuardService._internal();
  factory AuthGuardService() => _instance;
  AuthGuardService._internal();

  /// Check if user is authenticated and show modal if not
  /// Returns true if user is authenticated, false if modal was shown
  static Future<bool> requireAuthentication(
    BuildContext context,
    WidgetRef ref, {
    String? title,
    String? subtitle,
    VoidCallback? onSuccess,
  }) async {
    final authState = ref.read(authNotifierProvider);

    final isAuthenticated = authState.maybeMap(
      authenticated: (_) => true,
      orElse: () => false,
    );

    if (isAuthenticated) {
      return true;
    }

    // Show authentication modal
    await showAuthModal(
      context,
      title: title,
      subtitle: subtitle,
      onSuccess: onSuccess,
    );

    return false;
  }

  /// Check if user has backend authentication and show modal if not
  /// Returns true if user has backend auth, false if modal was shown
  /// Now includes token validation to ensure token is still valid
  static Future<bool> requireBackendAuthentication(
    BuildContext context,
    WidgetRef ref, {
    String? title,
    String? subtitle,
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] requireBackendAuthentication called');
    final authState = ref.read(authNotifierProvider);
    debugPrint('[AuthGuard] Current auth state type: ${authState.runtimeType}');

    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;

          // Debug logging to understand the issue
          debugPrint('[AuthGuard] Enhanced user details:');
          debugPrint('[AuthGuard] - hasBackendAuth: ${enhancedUser.hasBackendAuth}');
          debugPrint('[AuthGuard] - backendToken: ${enhancedUser.backendToken}');
          debugPrint('[AuthGuard] - backendToken != null: ${enhancedUser.backendToken != null}');
          debugPrint('[AuthGuard] - backendToken.isNotEmpty: ${enhancedUser.backendToken?.isNotEmpty ?? false}');
          debugPrint('[AuthGuard] - backendAuthResult: ${enhancedUser.backendAuthResult}');
          debugPrint('[AuthGuard] - backendAuthResult.isSuccess: ${enhancedUser.backendAuthResult?.isSuccess}');
          debugPrint('[AuthGuard] - backendAuthResult.backendToken: ${enhancedUser.backendAuthResult?.backendToken}');

          // Check both hasBackendAuth and token existence
          final hasAuth = enhancedUser.hasBackendAuth &&
                          enhancedUser.backendToken != null &&
                          enhancedUser.backendToken!.isNotEmpty;
          debugPrint('[AuthGuard] Final hasAuth result: $hasAuth');
          return hasAuth;
        }
        debugPrint('[AuthGuard] User is not EnhancedUserModel');
        return false;
      },
      orElse: () {
        debugPrint('[AuthGuard] User is not authenticated');
        return false;
      },
    );

    if (hasBackendAuth) {
      debugPrint('[AuthGuard] User has backend auth, now validating token...');

      // Validate the backend token to ensure it's still valid
      try {
        final authNotifier = ref.read(authNotifierProvider.notifier);
        final isTokenValid = await authNotifier.verifyBackendToken();

        if (isTokenValid) {
          debugPrint('[AuthGuard] Token is valid, calling success callback');
          onSuccess?.call();
          return true;
        } else {
          debugPrint('[AuthGuard] Token is invalid/expired, showing authentication modal');
          // Token is invalid/expired, show auth modal for reauthentication
          if (context.mounted) {
            await showAuthModal(
              context,
              title: title,
              subtitle: subtitle,
              onSuccess: onSuccess,
              onError: onError,
            );
          }
          return false;
        }
      } catch (e) {
        debugPrint('[AuthGuard] Error during token validation: $e');
        // On error, show auth modal
        if (context.mounted) {
          await showAuthModal(
            context,
            title: title,
            subtitle: subtitle,
            onSuccess: onSuccess,
            onError: onError,
          );
        }
        return false;
      }
    }

    // Double-check by watching the provider (in case of race condition)
    final watchedAuthState = ref.watch(authNotifierProvider);
    final doubleCheckHasAuth = watchedAuthState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth &&
                 enhancedUser.backendToken != null &&
                 enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (doubleCheckHasAuth) {
      debugPrint('[AuthGuard] Double-check confirmed user has backend auth, validating token...');

      // Validate token for double-check case as well
      try {
        final authNotifier = ref.read(authNotifierProvider.notifier);
        final isTokenValid = await authNotifier.verifyBackendToken();

        if (isTokenValid) {
          debugPrint('[AuthGuard] Double-check token validation successful');
          onSuccess?.call();
          return true;
        } else {
          debugPrint('[AuthGuard] Double-check token validation failed');
          // Fall through to show auth modal
        }
      } catch (e) {
        debugPrint('[AuthGuard] Error during double-check token validation: $e');
        // Fall through to show auth modal
      }
    }

    debugPrint('[AuthGuard] Showing authentication modal');
    // Show authentication modal
    if (context.mounted) {
      await showAuthModal(
        context,
        title: title,
        subtitle: subtitle,
        onSuccess: onSuccess,
        onError: onError,
      );
    }

    return false;
  }

  /// Show the authentication modal with slide-up animation
  static Future<void> showAuthModal(
    BuildContext context, {
    String? title,
    String? subtitle,
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] Showing authentication modal');
    debugPrint('[AuthGuard] Current context: ${context.runtimeType}');

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      useSafeArea: false,
      builder: (context) {
        return ManagedModal(
          child: AuthModal(
            title: title,
            subtitle: subtitle,
            onSuccess: onSuccess,
            onError: onError,
          ),
        );
      },
    );

    debugPrint('[AuthGuard] Authentication modal dialog completed');
    debugPrint('[AuthGuard] Returning to previous screen - parent modal should still be open');
  }

  /// Show the authentication modal that cannot be dismissed (for account screen)
  static Future<void> showNonDismissibleAuthModal(
    BuildContext context, {
    String? title,
    String? subtitle,
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] Showing non-dismissible authentication modal');
    debugPrint('[AuthGuard] Current context: ${context.runtimeType}');

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      useSafeArea: false,
      isDismissible: false, // Cannot be dismissed by tapping outside
      enableDrag: false, // Cannot be dismissed by dragging
      builder: (context) {
        return ManagedModal(
          child: AuthModal(
            title: title,
            subtitle: subtitle,
            onSuccess: onSuccess,
            onError: onError,
            isDismissible: false,
            redirectToHome: false,
          ),
        );
      },
    );

    debugPrint('[AuthGuard] Non-dismissible authentication modal dialog completed');
  }

  /// Check authentication for cart operations - fast version
  /// This checks local auth state first for speed, then verifies token in background
  static Future<bool> requireAuthForCart(
    BuildContext context,
    WidgetRef ref, {
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] requireAuthForCart called (fast mode)');

    // First, do a quick local check without API calls
    final authState = ref.read(authNotifierProvider);
    final hasLocalAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth &&
                 enhancedUser.backendToken != null &&
                 enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (hasLocalAuth) {
      debugPrint('[AuthGuard] Local auth check passed - allowing immediate access');
      onSuccess?.call();
      return true;
    }

    debugPrint('[AuthGuard] No local auth - showing authentication modal');
    // No local auth, show modal
    await showAuthModal(
      context,
      title: 'Sign in to access cart',
      subtitle: 'Create an account or sign in to save items to your cart',
      onSuccess: onSuccess,
      onError: onError,
    );

    return false;
  }

  /// Check authentication for cart operations with full backend verification
  /// Use this when you need guaranteed valid tokens (e.g., before API calls)
  static Future<bool> requireAuthForCartWithVerification(
    BuildContext context,
    WidgetRef ref, {
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] requireAuthForCartWithVerification called');
    return requireBackendAuthentication(
      context,
      ref,
      title: 'Sign in to access cart',
      subtitle: 'Create an account or sign in to save items to your cart',
      onSuccess: onSuccess,
      onError: onError,
    );
  }

  /// Check authentication for purchase operations
  static Future<bool> requireAuthForPurchase(
    BuildContext context,
    WidgetRef ref, {
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] requireAuthForPurchase called');
    return requireBackendAuthentication(
      context,
      ref,
      title: 'Sign in to purchase',
      subtitle: 'Create an account or sign in to complete your purchase',
      onSuccess: onSuccess,
      onError: onError,
    );
  }

  /// Check authentication for review purchase flow
  static Future<bool> requireAuthForReviewPurchase(
    BuildContext context,
    WidgetRef ref, {
    VoidCallback? onSuccess,
  }) async {
    return requireAuthentication(
      context,
      ref,
      title: 'Sign in to review purchase',
      subtitle: 'Create an account or sign in to review and complete your order',
      onSuccess: onSuccess,
    );
  }

  /// Check authentication for account access - fast version like cart
  /// This checks local auth state first for speed, then verifies token in background
  static Future<bool> requireAuthForAccount(
    BuildContext context,
    WidgetRef ref, {
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] requireAuthForAccount called (fast mode)');

    // First, do a quick local check without API calls
    final authState = ref.read(authNotifierProvider);
    final hasLocalAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth &&
                 enhancedUser.backendToken != null &&
                 enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (hasLocalAuth) {
      debugPrint('[AuthGuard] Local auth check passed - allowing immediate account access');
      onSuccess?.call();
      return true;
    }

    debugPrint('[AuthGuard] No local auth - showing authentication modal');
    // No local auth, show modal
    await showAuthModal(
      context,
      title: 'Sign in to your account',
      subtitle: 'Access your profile, orders, and preferences',
      onSuccess: onSuccess,
      onError: onError,
    );

    return false;
  }

  /// Check authentication for account access with full backend verification
  /// Use this when you need guaranteed valid tokens (e.g., when clicking account tab)
  static Future<bool> requireAuthForAccountWithVerification(
    BuildContext context,
    WidgetRef ref, {
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] requireAuthForAccountWithVerification called');
    return requireBackendAuthentication(
      context,
      ref,
      title: 'Sign in to your account',
      subtitle: 'Access your profile, orders, and preferences',
      onSuccess: onSuccess,
      onError: onError,
    );
  }

  /// Check authentication for account screen with non-dismissible modal
  /// This modal cannot be closed by tapping outside or dragging
  static Future<bool> requireAuthForAccountNonDismissible(
    BuildContext context,
    WidgetRef ref, {
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] requireAuthForAccountNonDismissible called');
    final authState = ref.read(authNotifierProvider);
    debugPrint('[AuthGuard] Current auth state type: ${authState.runtimeType}');

    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          final hasAuth = enhancedUser.hasBackendAuth &&
                          enhancedUser.backendToken != null &&
                          enhancedUser.backendToken!.isNotEmpty;
          debugPrint('[AuthGuard] Final hasAuth result: $hasAuth');
          return hasAuth;
        }
        debugPrint('[AuthGuard] User is not EnhancedUserModel');
        return false;
      },
      orElse: () {
        debugPrint('[AuthGuard] User is not authenticated');
        return false;
      },
    );

    if (hasBackendAuth) {
      debugPrint('[AuthGuard] User has backend auth, now validating token...');

      // Validate the backend token to ensure it's still valid
      try {
        final authNotifier = ref.read(authNotifierProvider.notifier);
        final isTokenValid = await authNotifier.verifyBackendToken();

        if (isTokenValid) {
          debugPrint('[AuthGuard] Token is valid, calling success callback');
          onSuccess?.call();
          return true;
        } else {
          debugPrint('[AuthGuard] Token is invalid/expired, showing non-dismissible authentication modal');
          // Token is invalid/expired, show non-dismissible auth modal for reauthentication
          if (context.mounted) {
            await showNonDismissibleAuthModal(
              context,
              title: 'Sign in to your account',
              subtitle: 'Access your profile, orders, and preferences',
              onSuccess: onSuccess,
              onError: onError,
            );
          }
          return false;
        }
      } catch (e) {
        debugPrint('[AuthGuard] Error during token validation: $e');
        // On error, show non-dismissible auth modal
        if (context.mounted) {
          await showNonDismissibleAuthModal(
            context,
            title: 'Sign in to your account',
            subtitle: 'Access your profile, orders, and preferences',
            onSuccess: onSuccess,
            onError: onError,
          );
        }
        return false;
      }
    }

    debugPrint('[AuthGuard] Showing non-dismissible authentication modal');
    // Show non-dismissible authentication modal
    if (context.mounted) {
      await showNonDismissibleAuthModal(
        context,
        title: 'Sign in to your account',
        subtitle: 'Access your profile, orders, and preferences',
        onSuccess: onSuccess,
        onError: onError,
      );
    }

    return false;
  }

  /// Check backend authentication for account features that need API access
  /// Use this for operations like delete account, profile updates, etc.
  static Future<bool> requireBackendAuthForAccountFeatures(
    BuildContext context,
    WidgetRef ref, {
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] requireBackendAuthForAccountFeatures called');
    return requireBackendAuthentication(
      context,
      ref,
      title: 'Verify account access',
      subtitle: 'Verifying your account credentials...',
      onSuccess: onSuccess,
      onError: onError,
    );
  }
}
