import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/product_model.dart';
import '../../domain/models/purchase_config.dart';
import '../../../cart/presentation/providers/cart_provider.dart';

class CartService {
  static Future<void> handleAddToCart({
    required BuildContext context,
    required WidgetRef ref,
    required Product product,
    required PurchaseConfig purchaseState,
    required Function(bool) setHandlingOperation,
    required DateTime? lastAddToCartClick,
    required Function(DateTime?) setLastAddToCartClick,
  }) async {
    // Debounce check
    final now = DateTime.now();
    if (lastAddToCartClick != null &&
        now.difference(lastAddToCartClick).inSeconds < 2) {
      return;
    }
    setLastAddToCartClick(now);

    // Prevent duplicate operations
    setHandlingOperation(true);

    // Capture root context and messenger before any operations
    final rootContext = Navigator.of(context, rootNavigator: true).context;
    final rootMessenger = ScaffoldMessenger.of(rootContext);

    try {
      // Get the selected variant or fallback to first variant
      final selectedVariantId = purchaseState.selectedVariantId;
      final variant = selectedVariantId != null
          ? product.variants.edges
              .map((edge) => edge.node)
              .firstWhere(
                (v) => v.id == selectedVariantId,
                orElse: () => product.variants.edges.first.node,
              )
          : product.variants.edges.first.node;

      // Show loading snackbar (clear any existing notifications first)
      if (context.mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Adding to cart...'),
            duration: const Duration(seconds: 1),
            backgroundColor: Colors.blue,
          ),
        );
      }

      // Close the modal immediately before starting the operation
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Send unit price - cart will handle quantity multiplication
      final unitPrice = double.tryParse(variant.price.amount) ?? 0.0;

      await ref.read(cartProvider.notifier).addItem(
        variantId: variant.id,
        productId: product.id,
        quantity: purchaseState.quantity,
        price: unitPrice,
        title: '${product.title} - ${purchaseState.selectedData} for ${purchaseState.selectedDays} (${purchaseState.eSimType.displayName})',
        currency: variant.price.currencyCode,
        imageUrl: product.featuredImage?.url,
      );

      // Show success message immediately since addItem completed without throwing
      // (If there was an error, addItem would have thrown an exception)
      rootMessenger.clearSnackBars();
      rootMessenger.showSnackBar(
        SnackBar(
          content: Text('Added to cart successfully'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    } on StateError catch (e) {
      // Handle "bad state" errors (widget disposal, ref access after disposal)
      debugPrint('[CartService] State error (likely widget disposal): $e');
      // Don't show error message for disposal errors - the operation likely succeeded
      // The API logs show successful responses even when this error occurs
    } catch (e) {
      // Handle other errors
      debugPrint('[CartService] Add to cart error: $e');
      rootMessenger.clearSnackBars();
      rootMessenger.showSnackBar(
        SnackBar(
          content: Text('Failed to add to cart: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 2),
        ),
      );
    } finally {
      setHandlingOperation(false);
    }
  }

  static Future<void> handleBuyNow({
    required BuildContext context,
    required WidgetRef ref,
    required Product product,
    required PurchaseConfig purchaseState,
    required Function(bool) setHandlingOperation,
    required DateTime? lastBuyNowClick,
    required Function(DateTime?) setLastBuyNowClick,
  }) async {
    // For now, just use add to cart functionality
    // Buy now can be implemented later when the cart provider supports it
    await handleAddToCart(
      context: context,
      ref: ref,
      product: product,
      purchaseState: purchaseState,
      setHandlingOperation: setHandlingOperation,
      lastAddToCartClick: lastBuyNowClick,
      setLastAddToCartClick: setLastBuyNowClick,
    );
  }
}