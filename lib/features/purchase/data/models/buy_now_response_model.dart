import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/buy_now_response.dart';

part 'buy_now_response_model.g.dart';

/// Helper function to parse double from string or number (nullable)
double? _parseDouble(dynamic value) {
  if (value == null) return null;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.parse(value);
  throw ArgumentError('Cannot parse $value as double');
}

/// Data model for Buy Now API response
@JsonSerializable()
class BuyNowResponseModel {
  final BuyNowDataModel data;

  const BuyNowResponseModel({
    required this.data,
  });

  factory BuyNowResponseModel.fromJson(Map<String, dynamic> json) =>
      _$BuyNowResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$BuyNowResponseModelToJson(this);

  /// Convert to domain entity
  BuyNowResponse toDomain() {
    // Determine if this is a successful response
    final isSuccess = data.isPaymentIntentResponse || (data.success == true);

    return BuyNowResponse(
      message: isSuccess ? 'Buy now successful' : 'Buy now failed',
      checkoutUrl: data.checkoutUrl ?? '', // Default to empty string for payment intent responses
      orderId: data.orderId,
      shopifyCartId: data.shopifyCartId ?? '', // Default to empty string for payment intent responses
      totalPrice: data.effectiveTotalPrice,
      currency: data.currency,
      status: isSuccess ? 'success' : 'failed',
      orderType: 'buy_now', // Default order type
      item: BuyNowItem(
        variantId: '', // Not provided in this API response
        productId: '', // Not provided in this API response
        quantity: 1, // Default quantity
        price: data.effectiveTotalPrice,
        title: '', // Not provided in this API response
      ),
      // Add payment intent data for in-app payment flow
      clientSecret: data.clientSecret,
      paymentIntentId: data.paymentIntentId,
    );
  }
}

/// Data model for the nested data object in Buy Now API response
/// Supports both checkout URL format and payment intent format
@JsonSerializable()
class BuyNowDataModel {
  // Common fields
  @JsonKey(name: 'order_id')
  final String orderId;
  final String currency;

  // Payment intent format fields (new)
  @JsonKey(name: 'client_secret')
  final String? clientSecret;
  @JsonKey(name: 'payment_intent_id')
  final String? paymentIntentId;
  @JsonKey(name: 'amount', fromJson: _parseDouble)
  final double? amount;

  // Checkout URL format fields (old)
  final bool? success;
  @JsonKey(name: 'checkout_url')
  final String? checkoutUrl;
  @JsonKey(name: 'shopify_cart_id')
  final String? shopifyCartId;
  @JsonKey(name: 'total_price', fromJson: _parseDouble)
  final double? totalPrice;
  final bool? authenticated;
  @JsonKey(name: 'local_order_created')
  final bool? localOrderCreated;

  const BuyNowDataModel({
    required this.orderId,
    required this.currency,
    this.clientSecret,
    this.paymentIntentId,
    this.amount,
    this.success,
    this.checkoutUrl,
    this.shopifyCartId,
    this.totalPrice,
    this.authenticated,
    this.localOrderCreated,
  });

  factory BuyNowDataModel.fromJson(Map<String, dynamic> json) =>
      _$BuyNowDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$BuyNowDataModelToJson(this);

  /// Check if this is a payment intent response
  bool get isPaymentIntentResponse => clientSecret != null && paymentIntentId != null;

  /// Check if this is a checkout URL response
  bool get isCheckoutUrlResponse => checkoutUrl != null;

  /// Get the total price regardless of response format
  double get effectiveTotalPrice => amount ?? totalPrice ?? 0.0;
}


