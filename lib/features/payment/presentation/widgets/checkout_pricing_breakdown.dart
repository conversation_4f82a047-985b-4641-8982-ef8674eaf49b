import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Checkout pricing breakdown widget that matches Figma design
/// Shows original price, credits discount, voucher discount, and final total
/// Based on Figma node-id=2174-4481
class CheckoutPricingBreakdown extends StatelessWidget {
  final double originalPrice;
  final double creditsDiscount;
  final double voucherDiscount;
  final String? voucherCode;
  final double finalPrice;
  final bool isLoading;
  final VoidCallback? onPayPressed;

  const CheckoutPricingBreakdown({
    super.key,
    required this.originalPrice,
    this.creditsDiscount = 0.0,
    this.voucherDiscount = 0.0,
    this.voucherCode,
    required this.finalPrice,
    this.isLoading = false,
    this.onPayPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 375,
      height: 162,
      decoration: const BoxDecoration(
        color: Color(0xFFFFFFFF),
        border: Border(
          top: BorderSide(
            color: Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
        child: Column(
          children: [
            // Pricing breakdown section
            Row(
              children: [
                // Grand Total label
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(8, 8, 16, 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Grand Total:',
                          style: GoogleFonts.inter(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFF1F2937),
                            height: 1.21,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                // Vertical divider line
                Container(
                  width: 1,
                  height: 23,
                  color: const Color(0xFFE5E7EB),
                ),
                
                // Pricing breakdown
                Expanded(
                  child: Container(
                    height: 46,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFFFFF),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: _buildPricingText(),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Pay Now button
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: isLoading ? null : onPayPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF6982),
                  foregroundColor: Colors.white,
                  elevation: 4,
                  shadowColor: Colors.black.withValues(alpha: 0.25),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  padding: const EdgeInsets.all(12),
                ),
                child: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Pay now',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          letterSpacing: 0.005,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the multi-line pricing text matching Figma design
  Widget _buildPricingText() {
    List<String> lines = [];
    
    // Original price
    lines.add('\$${originalPrice.toStringAsFixed(2)}');
    
    // Credits discount (if applied)
    if (creditsDiscount > 0) {
      lines.add('Credits (-\$${creditsDiscount.toStringAsFixed(2)})');
    }
    
    // Voucher discount (if applied)
    if (voucherDiscount > 0 && voucherCode != null && voucherCode!.isNotEmpty) {
      lines.add('${voucherCode!.toUpperCase()} (-\$${voucherDiscount.toStringAsFixed(2)})');
    }
    
    // Final price
    lines.add('\$  ${finalPrice.toStringAsFixed(2)}');
    
    return Text(
      lines.join('\n'),
      style: GoogleFonts.inter(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: const Color(0xFF1F2937),
        height: 1.21,
      ),
      textAlign: TextAlign.right,
    );
  }
}
