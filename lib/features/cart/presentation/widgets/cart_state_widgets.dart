import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import '../../../../l10n/app_localizations.dart';

/// Widget for displaying loading state
class CartLoadingWidget extends StatelessWidget {
  const CartLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(
        color: Color(0xFFFF6982),
      ),
    );
  }
}

/// Widget for displaying empty cart state
class CartEmptyWidget extends StatelessWidget {
  const CartEmptyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            l10n.cartIsEmpty,
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            l10n.addSomeProducts,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: const Color(0xFF9CA3AF),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.go('/home'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF6982),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              l10n.startShopping,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget for displaying error state
class CartErrorWidget extends StatelessWidget {
  final String? error;
  final bool isAuthenticated;
  final VoidCallback onRetry;
  final VoidCallback onAuthRequired;

  const CartErrorWidget({
    super.key,
    required this.error,
    required this.isAuthenticated,
    required this.onRetry,
    required this.onAuthRequired,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final isAuthError = !isAuthenticated ||
                       (error != null && (error!.contains('Backend authentication required') ||
                                         error!.contains('Authentication required') ||
                                         error!.contains('Unauthorized')));

    // Check if it's a connection error
    final isConnectionError = error != null && (
      error!.contains('Connection refused') ||
      error!.contains('Connection error') ||
      error!.contains('Backend server is not available') ||
      error!.contains('check your internet connection') ||
      (error!.contains('Backend authentication required') && !isAuthenticated)
    );

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isAuthError
              ? Icons.lock_outline
              : isConnectionError
                ? Icons.wifi_off_outlined
                : Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            isAuthError
              ? l10n.authenticationRequired
              : isConnectionError
                ? 'Connection Error'
                : l10n.unableToLoadCart,
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isAuthError
              ? l10n.pleaseSignInToCart
              : isConnectionError
                ? 'Unable to connect to server. Please check your internet connection and try again.'
                : error ?? l10n.failedToLoadCart,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: const Color(0xFF9CA3AF),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          // Show different buttons based on error type
          if (isConnectionError) ...[
            // For connection errors, show both retry and go home buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: onRetry,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFF6982),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    l10n.tryAgain,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                OutlinedButton(
                  onPressed: () => context.go('/home'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFFFF6982),
                    side: const BorderSide(color: Color(0xFFFF6982)),
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Go Home',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ] else ...[
            // For other errors, show single button
            ElevatedButton(
              onPressed: isAuthError ? onAuthRequired : onRetry,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF6982),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                isAuthError ? l10n.signIn : l10n.tryAgain,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
