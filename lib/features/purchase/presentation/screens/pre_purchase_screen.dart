import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/models/product_model.dart';
import '../../../../core/utils/price_formatter.dart';
import '../../domain/models/purchase_config.dart';
import '../providers/purchase_provider.dart';
import '../services/cart_service.dart';
import '../widgets/purchase_header.dart';
import '../widgets/selection_dropdown.dart';
import '../widgets/esim_type_selector.dart';
import '../widgets/quantity_selector.dart';
import '../widgets/purchase_actions.dart';
import '../widgets/product_details_view.dart';


import '../../../../l10n/app_localizations.dart';

class PrePurchaseScreen extends ConsumerStatefulWidget {
  final Product product;

  const PrePurchaseScreen({
    super.key,
    required this.product,
  });

  @override
  ConsumerState<PrePurchaseScreen> createState() => _PrePurchaseScreenState();
}

class _PrePurchaseScreenState extends ConsumerState<PrePurchaseScreen> {
  bool _showDetails = false;
  bool _variantsInitialized = false;
  DateTime? _lastAddToCartClick;

  // Cache the initial config to prevent recreation on rebuilds
  late final PurchaseConfig _initialConfig;
  late final StateNotifierProvider<PurchaseNotifier, PurchaseConfig> _purchaseStateProvider;

  @override
  void initState() {
    super.initState();
    final productPrice = _getProductPrice(widget.product);

    // Get first available variant
    final firstVariant = widget.product.variants.edges.isNotEmpty
        ? widget.product.variants.edges.first.node
        : null;

    // Extract initial data and days from first variant
    String initialData = '1GB';
    String initialDays = '1 day';

    if (firstVariant != null) {
      for (final option in firstVariant.selectedOptions) {
        if (option.name.toLowerCase() == 'gb' || option.name.toLowerCase() == 'data') {
          initialData = option.value;
        } else if (option.name.toLowerCase() == 'days' || option.name.toLowerCase() == 'validity') {
          initialDays = option.value;
        }
      }
    }

    // Create initial config once and cache it
    _initialConfig = PurchaseConfig(
      productId: widget.product.id,
      productTitle: widget.product.title,
      productImageUrl: widget.product.featuredImage?.url,
      unitPrice: productPrice,
      selectedVariantId: firstVariant?.id,
      selectedVariantTitle: firstVariant?.displayName ?? 'Default',
      selectedData: initialData,
      selectedDays: initialDays,
    );

    // Create provider once and cache it
    _purchaseStateProvider = purchaseProvider(_initialConfig);

    // Initialize variants after the frame is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_variantsInitialized) {
        final variants = widget.product.variants.edges.map((edge) => edge.node).toList();
        ref.read(_purchaseStateProvider.notifier).setAvailableVariants(variants);
        _variantsInitialized = true;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final productCurrency = _getProductCurrency(widget.product);

    return Consumer(
      builder: (context, ref, child) {
        final purchaseState = ref.watch(_purchaseStateProvider);
        final purchaseNotifier = ref.read(_purchaseStateProvider.notifier);
        final iccidOptions = ref.watch(iccidOptionsProvider);

        // Get variants for dynamic dropdown options
        final variants = widget.product.variants.edges.map((edge) => edge.node).toList();

        // Show only available variants that can be chosen
        // debugPrint('🔍 === AVAILABLE VARIANTS FOR: ${widget.product.title} ===');
        final availableVariants = variants.where((variant) => variant.availableForSale).toList();
        // debugPrint('🔍 Total choosable variants: ${availableVariants.length}');

        // for (int i = 0; i < availableVariants.length; i++) {
        //   final variant = availableVariants[i];
        //   String dataAmount = '';
        //   String validityPeriod = '';

        //   for (final option in variant.selectedOptions) {
        //     if (option.name.toLowerCase() == 'gb') {
        //       dataAmount = option.value;
        //     } else if (option.name.toLowerCase() == 'days') {
        //       validityPeriod = option.value;
        //     }
        //   }

        //   // debugPrint('🔍 ✅ $dataAmount / $validityPeriod - \$${variant.price.amount}');
        // }
        // debugPrint('🔍 === END AVAILABLE VARIANTS ===');

        final dataOptions = ref.watch(smartDataOptionsProvider(availableVariants));
        final daysOptions = ref.watch(smartDaysOptionsProvider((variants: availableVariants, selectedData: purchaseState.selectedData)));

        // debugPrint('🎨 UI Rebuild - Quantity: ${purchaseState.quantity}, Total: ${purchaseState.totalPrice}');

        return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.9,
            minHeight: MediaQuery.of(context).size.height * 0.3,
          ),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: IntrinsicHeight(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                PurchaseHeader(showDetails: _showDetails),
                // Bottom section (matching Figma structure)
                Flexible(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Content section (white background)
                      Flexible(
                        child: SingleChildScrollView(
                          child: Container(
                            color: Colors.white,
                            padding: const EdgeInsets.fromLTRB(20, 20, 20, 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Product title (matching Figma exactly)
                                Text(
                                  purchaseState.productTitle,
                                  style: GoogleFonts.inter(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xFF1F2937),
                                    height: 1.21,
                                  ),
                                ),
                                const SizedBox(height: 16),

                                // Show details view or form based on state
                                if (_showDetails) ...[
                                  ProductDetailsView(
                                    product: widget.product,
                                    onHideDetails: () => setState(() => _showDetails = false),
                                  ),
                                ] else ...[
                                  _buildPurchaseForm(purchaseState, purchaseNotifier, dataOptions, daysOptions, iccidOptions),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ),
                      // Show quantity/price and buttons only when not in details view
                      if (!_showDetails) ...[
                        // Gray section for quantity and price (matching Figma Frame 5)
                        Container(
                          color: const Color(0xFFF9FAFB),
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Quantity selector with fixed width
                              SizedBox(
                                width: 120,
                                child: QuantitySelector(
                                  quantity: purchaseState.quantity,
                                  onIncrement: () {
                                    // debugPrint('🔢 Incrementing quantity from ${purchaseState.quantity} to ${purchaseState.quantity + 1}');
                                    purchaseNotifier.incrementQuantity();
                                  },
                                  onDecrement: () {
                                    debugPrint('🔢 Decrementing quantity from ${purchaseState.quantity} to ${purchaseState.quantity - 1}');
                                    purchaseNotifier.decrementQuantity();
                                  },
                                ),
                              ),
                              // Flexible total payment section
                              Expanded(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Text(
                                      AppLocalizations.of(context).totalPayment,
                                      style: GoogleFonts.inter(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        color: const Color(0xFF1F2937),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Flexible(
                                      child: FittedBox(
                                        fit: BoxFit.scaleDown,
                                        alignment: Alignment.centerRight,
                                        child: Text(
                                          _formatPrice(purchaseState.totalPrice, productCurrency),
                                          style: GoogleFonts.inter(
                                            fontSize: 18,
                                            fontWeight: FontWeight.w600,
                                            color: const Color(0xFF111827),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Button section (matching Figma Button frame)
                        Container(
                          color: Colors.white,
                          padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
                          child: PurchaseActions(
                            onAddToCart: () async {
                              await CartService.handleAddToCart(
                                context: context,
                                ref: ref,
                                product: widget.product,
                                purchaseState: purchaseState,
                                setHandlingOperation: (bool handling) {
                                  // No-op: CartService handles its own loading feedback
                                },
                                lastAddToCartClick: _lastAddToCartClick,
                                setLastAddToCartClick: (DateTime? time) {
                                  setState(() {
                                    _lastAddToCartClick = time;
                                  });
                                },
                              );
                            },
                            // Pass product data for Buy Now checkout
                            variantId: purchaseState.selectedVariantId ?? (widget.product.variants.edges.isNotEmpty ? widget.product.variants.edges.first.node.id : null),
                            productId: widget.product.id,
                            quantity: purchaseState.quantity,
                            price: purchaseState.unitPrice,
                            title: '${widget.product.title} - ${purchaseState.selectedVariantTitle} - ${purchaseState.selectedData} for ${purchaseState.selectedDays} (${purchaseState.eSimType.displayName})',
                            imageUrl: widget.product.featuredImage?.url,
                            onCloseModal: () => Navigator.of(context).pop(),
                          ),
                        ),
                      ] else ...[
                        // Button section for details view
                        Container(
                          color: Colors.white,
                          padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
                          child: PurchaseActions(
                            onAddToCart: () async {
                              await CartService.handleAddToCart(
                                context: context,
                                ref: ref,
                                product: widget.product,
                                purchaseState: purchaseState,
                                setHandlingOperation: (bool handling) {
                                  // No-op: CartService handles its own loading feedback
                                },
                                lastAddToCartClick: _lastAddToCartClick,
                                setLastAddToCartClick: (DateTime? time) {
                                  setState(() {
                                    _lastAddToCartClick = time;
                                  });
                                },
                              );
                            },
                            variantId: purchaseState.selectedVariantId ?? (widget.product.variants.edges.isNotEmpty ? widget.product.variants.edges.first.node.id : null),
                            productId: widget.product.id,
                            quantity: purchaseState.quantity,
                            price: purchaseState.unitPrice,
                            title: '${widget.product.title} - ${purchaseState.selectedVariantTitle} - ${purchaseState.selectedData} for ${purchaseState.selectedDays} (${purchaseState.eSimType.displayName})',
                            imageUrl: widget.product.featuredImage?.url,
                            onCloseModal: () => Navigator.of(context).pop(),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
      },
    );
  }



  Widget _buildPurchaseForm(
    PurchaseConfig purchaseState,
    PurchaseNotifier purchaseNotifier,
    List<String> dataOptions,
    List<String> daysOptions,
    List<String> iccidOptions,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // See details button (positioned after product title)
        Align(
          alignment: Alignment.center,
          child: TextButton(
            onPressed: () {
              setState(() {
                _showDetails = true;
              });
            },
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              AppLocalizations.of(context).seeDetails,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xFFFF6982),
                decoration: TextDecoration.underline,
                decorationColor: const Color(0xFFFF6982),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Selection dropdowns row
        Row(
          children: [
            Expanded(
              child: SelectionDropdown(
                label: AppLocalizations.of(context).yourDataLabel,
                value: purchaseState.selectedData,
                options: dataOptions,
                onChanged: (value) {
                  if (value != null) {
                    purchaseNotifier.updateData(value);
                  }
                },
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: SelectionDropdown(
                label: AppLocalizations.of(context).dayLabel,
                value: purchaseState.selectedDays,
                options: daysOptions,
                onChanged: (value) {
                  if (value != null) {
                    purchaseNotifier.updateDays(value);
                  }
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),



        // eSIM type selector
        ESimTypeSelector(
          selectedType: purchaseState.eSimType,
          onChanged: (type) {
            debugPrint('🔄 eSIM type changed to: ${type.displayName}');
            purchaseNotifier.updateESimType(type);
          },
        ),
        const SizedBox(height: 16),

        // Conditional ICCID dropdown - COMMENTED OUT
        // if (purchaseState.eSimType == ESimType.topUp) ...[
        //   const SizedBox(height: 16),
        //   SelectionDropdown(
        //     label: 'ICCID for Top Up',
        //     value: purchaseState.selectedIccid ?? 'Select your TravelGator eSIM',
        //     options: iccidOptions,
        //     onChanged: (value) {
        //       debugPrint('🔄 ICCID changed to: $value');
        //       purchaseNotifier.updateIccid(value);
        //     },
        //   ),
        //   // const SizedBox(height: 8), // Add spacing before gray section
        // ],
      ],
    );
  }











  double _getProductPrice(Product product) {
    return PriceFormatter.getProductPriceValue(product);
  }

  String _getProductCurrency(Product product) {
    try {
      if (product.variants.edges.isNotEmpty) {
        final variant = product.variants.edges.first.node;
        return variant.price.currencyCode;
      }
    } catch (e) {
      debugPrint('Error getting product currency: $e');
    }
    return 'USD'; // Default fallback
  }

  String _formatPrice(double amount, String currency) {
    return PriceFormatter.formatPrice(amount.toStringAsFixed(2), currency);
  }


}
