import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../domain/entities/order.dart' as entities;
import 'transaction_type_badge.dart';
import 'payment_status_badge.dart';
import 'money_status_display.dart';

/// Card widget for displaying order information in the transaction history
class OrderCard extends StatelessWidget {
  final entities.Order order;
  final VoidCallback? onTap;

  const OrderCard({
    super.key,
    required this.order,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap ?? () => _navigateToDetail(context),
        borderRadius: BorderRadius.circular(8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date and transaction type
                  Row(
                    children: [
                      Text(
                        order.formattedDate,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: Color(0xFF9CA3AF),
                        ),
                      ),
                      const SizedBox(width: 8),
                      TransactionTypeBadge(type: order.transactionType),
                    ],
                  ),
                  const SizedBox(height: 4),
                  
                  // Order ID
                  Text(
                    order.formattedOrderId,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF1F2937),
                    ),
                  ),
                  const SizedBox(height: 4),
                  
                  // Product title
                  Text(
                    order.productTitle,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF1F2937),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  
                  // SKU (placeholder for now)
                  Text(
                    'SKU: ${_generateSKU()}',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF9CA3AF),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(width: 8),
            
            // Right content - Transaction info
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                PaymentStatusBadge(status: order.paymentStatus),
                const SizedBox(height: 4),
                MoneyStatusDisplay(
                  amount: order.totalPrice,
                  currency: order.currency,
                  status: order.paymentStatus,
                  showPlus: order.transactionType == entities.TransactionType.earn,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToDetail(BuildContext context) {
    // Navigate to transaction detail screen
    context.push('/account/transactions/${order.id}');
  }

  String _generateSKU() {
    // Generate a placeholder SKU based on order ID
    final shortId = order.id.length > 8 ? order.id.substring(0, 8) : order.id;
    return 'MK${shortId.toUpperCase()}';
  }
}
