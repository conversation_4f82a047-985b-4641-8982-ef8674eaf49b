import 'package:equatable/equatable.dart';

/// Order entity representing a transaction/order from the API
class Order extends Equatable {
  final String id;
  final double totalPrice;
  final double? originalTotal; // New field for enhanced discount info
  final String currency;
  final OrderStatus status;
  final String? voucherCode;
  final double discountAmount;
  final List<OrderItem> items;
  final DateTime createdAt;

  const Order({
    required this.id,
    required this.totalPrice,
    this.originalTotal,
    required this.currency,
    required this.status,
    this.voucherCode,
    required this.discountAmount,
    required this.items,
    required this.createdAt,
  });

  /// Get the main product title from the first item
  String get productTitle {
    if (items.isNotEmpty) {
      return items.first.title;
    }
    return 'Unknown Product';
  }

  /// Get the total quantity of all items
  int get totalQuantity {
    return items.fold(0, (sum, item) => sum + item.quantity);
  }

  /// Get formatted order ID for display
  String get formattedOrderId => 'Order $id';

  /// Get formatted date for display
  String get formattedDate {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    final day = createdAt.day;
    final month = months[createdAt.month - 1];
    final year = createdAt.year;
    final hour = createdAt.hour.toString().padLeft(2, '0');
    final minute = createdAt.minute.toString().padLeft(2, '0');
    
    return '$day $month, $year - $hour:$minute PM';
  }

  /// Get transaction type based on order characteristics
  TransactionType get transactionType {
    // For now, assume all orders are purchases
    // This can be extended based on business logic
    return TransactionType.purchase;
  }

  /// Get payment status based on order status
  PaymentStatus get paymentStatus {
    switch (status) {
      case OrderStatus.confirmed:
        return PaymentStatus.success;
      case OrderStatus.pending:
        return PaymentStatus.pending;
      case OrderStatus.failed:
      case OrderStatus.cancelled:
        return PaymentStatus.failed;
    }
  }

  /// Check if order has discount applied
  bool get hasDiscount => discountAmount > 0;

  /// Get the original total price (before discount)
  double get effectiveOriginalTotal => originalTotal ?? totalPrice;

  /// Get formatted discount amount for display
  String get formattedDiscountAmount => '\$${discountAmount.toStringAsFixed(2)}';

  /// Get formatted original total for display
  String get formattedOriginalTotal => '\$${effectiveOriginalTotal.toStringAsFixed(2)}';

  /// Get formatted total price for display
  String get formattedTotalPrice => '\$${totalPrice.toStringAsFixed(2)}';

  @override
  List<Object?> get props => [
        id,
        totalPrice,
        originalTotal,
        currency,
        status,
        voucherCode,
        discountAmount,
        items,
        createdAt,
      ];
}

/// Order item entity representing individual items in an order
class OrderItem extends Equatable {
  final String variantId;
  final int quantity;
  final double price;
  final String title;
  final String? imageUrl;

  const OrderItem({
    required this.variantId,
    required this.quantity,
    required this.price,
    required this.title,
    this.imageUrl,
  });

  /// Get formatted price for display
  String get formattedPrice => '\$${price.toStringAsFixed(2)}';

  /// Get total price for this item
  double get totalPrice => price * quantity;

  /// Get formatted total price for display
  String get formattedTotalPrice => '\$${totalPrice.toStringAsFixed(2)}';

  @override
  List<Object?> get props => [variantId, quantity, price, title, imageUrl];
}

/// Order status enum
enum OrderStatus {
  pending,
  confirmed,
  failed,
  cancelled;

  /// Get display name for the status
  String get displayName {
    switch (this) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.failed:
        return 'Failed';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Create from string value
  static OrderStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'pending':
        return OrderStatus.pending;
      case 'confirmed':
        return OrderStatus.confirmed;
      case 'failed':
        return OrderStatus.failed;
      case 'cancelled':
        return OrderStatus.cancelled;
      default:
        return OrderStatus.pending;
    }
  }
}

/// Transaction type enum for UI display
enum TransactionType {
  purchase,
  redeem,
  earn;

  /// Get display name for the transaction type
  String get displayName {
    switch (this) {
      case TransactionType.purchase:
        return 'Purchase';
      case TransactionType.redeem:
        return 'Redeem';
      case TransactionType.earn:
        return 'Earn';
    }
  }

  /// Get background color for the badge
  String get backgroundColor {
    switch (this) {
      case TransactionType.purchase:
        return '#DBEAFE'; // Blue background
      case TransactionType.redeem:
        return '#FFEDD5'; // Orange background
      case TransactionType.earn:
        return '#ECFCCB'; // Green background
    }
  }

  /// Get text color for the badge
  String get textColor {
    switch (this) {
      case TransactionType.purchase:
        return '#2563EB'; // Blue text
      case TransactionType.redeem:
        return '#EA580C'; // Orange text
      case TransactionType.earn:
        return '#65A30D'; // Green text
    }
  }
}

/// Payment status enum for UI display
enum PaymentStatus {
  success,
  failed,
  pending;

  /// Get display name for the payment status
  String get displayName {
    switch (this) {
      case PaymentStatus.success:
        return 'Success';
      case PaymentStatus.failed:
        return 'Fail';
      case PaymentStatus.pending:
        return 'Pending';
    }
  }

  /// Get text color for the status
  String get textColor {
    switch (this) {
      case PaymentStatus.success:
        return '#84CC16'; // Green
      case PaymentStatus.failed:
        return '#EF4444'; // Red
      case PaymentStatus.pending:
        return '#9CA3AF'; // Gray
    }
  }
}
