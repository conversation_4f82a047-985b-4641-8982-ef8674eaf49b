import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/repositories/payment_repository.dart';
import '../../domain/services/payment_polling_service.dart';
import '../../data/repositories/payment_repository_impl.dart';
import '../../data/datasources/cart_payment_data_source.dart';
import '../../data/datasources/buy_now_payment_data_source.dart';
import '../../../../core/network/api_config.dart';

/// Provider for cart payment data source
final cartPaymentDataSourceProvider = Provider<CartPaymentDataSource>((ref) {
  final dio = ApiConfig.createDioClient();
  return CartPaymentDataSourceImpl(dio: dio);
});

/// Provider for buy now payment data source
final buyNowPaymentDataSourceProvider = Provider<BuyNowPaymentDataSource>((ref) {
  final dio = ApiConfig.createDioClient();
  return BuyNowPaymentDataSourceImpl(dio: dio);
});

/// Provider for payment repository
final paymentRepositoryProvider = Provider<PaymentRepository>((ref) {
  return PaymentRepositoryImpl(
    cartDataSource: ref.read(cartPaymentDataSourceProvider),
    buyNowDataSource: ref.read(buyNowPaymentDataSourceProvider),
    ref: ref,
  );
});

/// Provider for payment polling service
final paymentPollingServiceProvider = Provider<PaymentPollingService>((ref) {
  final repository = ref.read(paymentRepositoryProvider);
  return PaymentPollingService(repository: repository);
});
