import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../l10n/app_localizations.dart';
import 'presentation/screens/account_screen.dart';
import 'presentation/screens/account_information_screen.dart';
import 'presentation/screens/transaction_history_screen.dart';
import 'presentation/screens/transaction_detail_screen.dart';
import 'presentation/screens/languages_screen.dart';
import '../../shared/widgets/bottom_nav_container.dart';

/// Routes for the account feature
class AccountRoutes {
  // Base path for all account routes
  static const String account = '/account';

  // Tab index for account in the bottom navigation
  static const int accountTabIndex = 4;

  // Sub-routes
  static const String accountInformation = 'information';
  static const String transactionHistory = 'transactions';
  static const String paymentSettings = 'payment-settings';
  static const String currency = 'currency';
  static const String languages = 'languages';
  static const String helpCenter = 'help-center';
  static const String privacyPolicy = 'privacy-policy';
  static const String termsConditions = 'terms-conditions';

  // Helper methods to get full paths
  static String getAccountInformationPath() => '$account/$accountInformation';
  static String getTransactionHistoryPath() => '$account/$transactionHistory';
  static String getPaymentSettingsPath() => '$account/$paymentSettings';
  static String getCurrencyPath() => '$account/$currency';
  static String getLanguagesPath() => '$account/$languages';
  static String getHelpCenterPath() => '$account/$helpCenter';
  static String getPrivacyPolicyPath() => '$account/$privacyPolicy';
  static String getTermsConditionsPath() => '$account/$termsConditions';

  // Define routes for GoRouter
  static List<RouteBase> get routes => [
    GoRoute(
      path: account,
      pageBuilder: (context, state) {
        return NoTransitionPage(
          child: BottomNavContainer(
            tabIndex: accountTabIndex,
            child: const AccountScreen(),
          ),
        );
      },
      routes: [
        GoRoute(
          path: accountInformation,
          builder: (context, state) => const AccountInformationScreen(),
        ),
        GoRoute(
          path: transactionHistory,
          builder: (context, state) => const TransactionHistoryScreen(),
          routes: [
            GoRoute(
              path: ':orderId',
              builder: (context, state) {
                final orderId = state.pathParameters['orderId']!;
                return TransactionDetailScreen(orderId: orderId);
              },
            ),
          ],
        ),
        // Placeholder routes for other sections
        GoRoute(
          path: paymentSettings,
          builder:
              (context, state) {
                final l10n = AppLocalizations.of(context);
                return Scaffold(
                  appBar: AppBar(title: Text(l10n.paymentSettings)),
                  body: Center(child: Text('${l10n.paymentSettings} Screen')),
                );
              },
        ),
        GoRoute(
          path: currency,
          builder:
              (context, state) {
                final l10n = AppLocalizations.of(context);
                return Scaffold(
                  appBar: AppBar(title: Text(l10n.currency)),
                  body: Center(child: Text('${l10n.currency} Screen')),
                );
              },
        ),
        GoRoute(
          path: languages,
          builder: (context, state) => const LanguagesScreen(),
        ),
        GoRoute(
          path: helpCenter,
          builder:
              (context, state) {
                final l10n = AppLocalizations.of(context);
                return Scaffold(
                  appBar: AppBar(title: Text(l10n.helpCenter)),
                  body: Center(child: Text('${l10n.helpCenter} Screen')),
                );
              },
        ),
        GoRoute(
          path: privacyPolicy,
          builder:
              (context, state) {
                final l10n = AppLocalizations.of(context);
                return Scaffold(
                  appBar: AppBar(title: Text(l10n.privacyPolicy)),
                  body: Center(child: Text('${l10n.privacyPolicy} Screen')),
                );
              },
        ),
        GoRoute(
          path: termsConditions,
          builder:
              (context, state) {
                final l10n = AppLocalizations.of(context);
                return Scaffold(
                  appBar: AppBar(title: Text(l10n.termsAndConditions)),
                  body: Center(child: Text('${l10n.termsAndConditions} Screen')),
                );
              },
        ),
      ],
    ),
  ];

  // Prevent instantiation
  AccountRoutes._();
}
