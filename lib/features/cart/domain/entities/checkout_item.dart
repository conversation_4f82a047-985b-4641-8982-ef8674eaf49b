import 'package:equatable/equatable.dart';

/// Represents an item to be checked out with specific quantity
class CheckoutItem extends Equatable {
  /// Product variant ID
  final String variantId;
  
  /// Quantity to checkout
  final int quantity;

  const CheckoutItem({
    required this.variantId,
    required this.quantity,
  });

  /// Create a copy of this CheckoutItem with the given fields replaced
  CheckoutItem copyWith({
    String? variantId,
    int? quantity,
  }) {
    return CheckoutItem(
      variantId: variantId ?? this.variantId,
      quantity: quantity ?? this.quantity,
    );
  }

  /// Convert to JSON for API requests
  Map<String, dynamic> toJson() {
    return {
      'variant_id': variantId,
      'quantity': quantity,
    };
  }

  /// Create from JSON
  factory CheckoutItem.fromJson(Map<String, dynamic> json) {
    return CheckoutItem(
      variantId: json['variant_id'] as String,
      quantity: json['quantity'] as int,
    );
  }

  @override
  List<Object?> get props => [variantId, quantity];

  @override
  String toString() => 'CheckoutItem(variantId: $variantId, quantity: $quantity)';
}
