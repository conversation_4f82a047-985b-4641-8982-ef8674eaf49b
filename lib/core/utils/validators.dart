class Validators {
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }

    final emailRegExp = RegExp(r'^[a-zA-Z0-9.]+@[a-zA-Z0-9]+\.[a-zA-Z]+');
    if (!emailRegExp.hasMatch(value)) {
      return 'Please enter a valid email';
    }

    return null;
  }

  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }

    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }

    return null;
  }

  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Name is required';
    }

    if (value.length < 2) {
      return 'Name must be at least 2 characters';
    }

    return null;
  }

  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }

    return null;
  }

  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Phone might be optional
    }

    final phoneRegExp = RegExp(r'^\+?[0-9]{10,15}$');
    if (!phoneRegExp.hasMatch(value)) {
      return 'Please enter a valid phone number';
    }

    return null;
  }

  /// Validate credit card number using Luhn algorithm
  static String? validateCardNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Card number is required';
    }

    // Remove spaces and non-digits
    final cleanValue = value.replaceAll(RegExp(r'\D'), '');

    if (cleanValue.length < 13 || cleanValue.length > 19) {
      return 'Card number must be 13-19 digits';
    }

    // Luhn algorithm validation
    if (!_isValidLuhn(cleanValue)) {
      return 'Invalid card number';
    }

    return null;
  }

  /// Validate expiry date (MM/YY format)
  static String? validateExpiryDate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Expiry date is required';
    }

    // Remove spaces and non-digits
    final cleanValue = value.replaceAll(RegExp(r'\D'), '');

    if (cleanValue.length != 4) {
      return 'Enter MM/YY format';
    }

    final month = int.tryParse(cleanValue.substring(0, 2));
    final year = int.tryParse(cleanValue.substring(2, 4));

    if (month == null || year == null) {
      return 'Invalid date format';
    }

    if (month < 1 || month > 12) {
      return 'Invalid month';
    }

    // Check if card is expired
    final now = DateTime.now();
    final currentYear = now.year % 100; // Get last 2 digits of current year
    final currentMonth = now.month;

    if (year < currentYear || (year == currentYear && month < currentMonth)) {
      return 'Card has expired';
    }

    return null;
  }

  /// Validate security code (CVV/CVC)
  static String? validateSecurityCode(String? value) {
    if (value == null || value.isEmpty) {
      return 'Security code is required';
    }

    // Remove spaces and non-digits
    final cleanValue = value.replaceAll(RegExp(r'\D'), '');

    if (cleanValue.length < 3 || cleanValue.length > 4) {
      return 'Security code must be 3-4 digits';
    }

    return null;
  }

  /// Validate name on card
  static String? validateNameOnCard(String? value) {
    if (value == null || value.isEmpty) {
      return 'Name on card is required';
    }

    if (value.trim().length < 2) {
      return 'Name must be at least 2 characters';
    }

    // Check if name contains only letters, spaces, hyphens, and apostrophes
    final nameRegExp = RegExp(r"^[a-zA-Z\s\-']+$");
    if (!nameRegExp.hasMatch(value.trim())) {
      return 'Name can only contain letters, spaces, hyphens, and apostrophes';
    }

    return null;
  }

  /// Check if all card fields are valid for enabling payment button
  static bool isCardFormValid({
    required String? cardNumber,
    required String? expiryDate,
    required String? securityCode,
    required String? nameOnCard,
  }) {
    return validateCardNumber(cardNumber) == null &&
           validateExpiryDate(expiryDate) == null &&
           validateSecurityCode(securityCode) == null &&
           validateNameOnCard(nameOnCard) == null;
  }

  /// Luhn algorithm implementation for card number validation
  static bool _isValidLuhn(String cardNumber) {
    int sum = 0;
    bool alternate = false;

    // Process digits from right to left
    for (int i = cardNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cardNumber[i]);

      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit ~/ 10) + (digit % 10);
        }
      }

      sum += digit;
      alternate = !alternate;
    }

    return sum % 10 == 0;
  }
}
