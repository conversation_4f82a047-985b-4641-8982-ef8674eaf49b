import 'legal_document_type.dart';
import 'legal_document_section.dart';

/// Entity representing a legal document with its content
class LegalDocument {
  final LegalDocumentType type;
  final String title;
  final String lastUpdated;
  final List<LegalDocumentSection> sections;

  const LegalDocument({
    required this.type,
    required this.title,
    required this.lastUpdated,
    required this.sections,
  });

  /// Create a copy of this document with updated fields
  LegalDocument copyWith({
    LegalDocumentType? type,
    String? title,
    String? lastUpdated,
    List<LegalDocumentSection>? sections,
  }) {
    return LegalDocument(
      type: type ?? this.type,
      title: title ?? this.title,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      sections: sections ?? this.sections,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LegalDocument &&
        other.type == type &&
        other.title == title &&
        other.lastUpdated == lastUpdated &&
        _listEquals(other.sections, sections);
  }

  @override
  int get hashCode {
    return type.hashCode ^
        title.hashCode ^
        lastUpdated.hashCode ^
        sections.hashCode;
  }

  /// Helper method to compare lists
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'LegalDocument(type: $type, title: $title, lastUpdated: $lastUpdated, sections: ${sections.length})';
  }
}
