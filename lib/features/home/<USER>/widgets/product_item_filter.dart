import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/country.dart';
import 'travel_sim_card.dart';
import '../../../../../core/providers/countries_provider.dart';
import '../screens/filtered_products_screen.dart';

class ProductItemFilter extends ConsumerStatefulWidget {
  const ProductItemFilter({super.key});

  @override
  ConsumerState<ProductItemFilter> createState() => _ProductItemFilterState();
}

class _ProductItemFilterState extends ConsumerState<ProductItemFilter> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final countriesState = ref.watch(countriesStateProvider);
    // Display all countries without any active selection
    
    if (countriesState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFFFF6982)),
      );
    }

    if (countriesState.error != null) {
      return Center(
        child: Text(
          'Error loading countries: ${countriesState.error}',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    if (countriesState.countries.isEmpty) {
      return const Center(child: Text('No countries available'));
    }

    return _buildCountriesList(
      context,
      ref,
      countriesState.countries,
      countriesState,
    );
  }

  Widget _buildCountriesList(
    BuildContext context,
    WidgetRef ref,
    List<Country> countries,
    CountriesState countriesState,
  ) {
    // debugPrint('🌍 Countries count: ${countries.length}');
    // for (var country in countries) {
    //   debugPrint('  - ${country.name} (${country.code})');
    // }
    return Column(
      children:
          countries.map((country) {
            // All cards are non-active
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: TravelSimCard(
                country: country,
                isActive: countriesState.activeCountry == country.name,
                onTap: () async {
                  // debugPrint('🔥 Country tapped: ${country.name} (code: ${country.code})');

                  // Don't set active country here - just navigate directly
                  // FilteredProductsScreen will handle the filtering

                  // Show loading indicator
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) => const Center(
                      child: CircularProgressIndicator(color: Color(0xFFFF6982)),
                    ),
                  );

                  // Small delay to show loading
                  await Future.delayed(const Duration(milliseconds: 300)); // Reduced delay

                  if (context.mounted) {
                    Navigator.of(context).pop(); // Close loading dialog
                    debugPrint('🚀 Navigating to FilteredProductsScreen for country: ${country.name}');
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => FilteredProductsScreen(
                          countryCode: country.code, // Still pass code for backend filtering
                          filterTitle: country.name, // Use country name for display
                        ),
                      ),
                    );
                    // No need for .then() callback since we're not setting filter state
                  }
                },
              ),
            );
          }).toList(),
    );
  }
}
