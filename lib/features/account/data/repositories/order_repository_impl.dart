import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/order.dart' as entities;
import '../../domain/entities/discount_stats.dart';
import '../../domain/repositories/order_repository.dart';
import '../datasources/order_data_source.dart';

/// Implementation of order repository
class OrderRepositoryImpl implements OrderRepository {
  final OrderDataSource dataSource;

  OrderRepositoryImpl({required this.dataSource});

  @override
  Future<Either<Failure, List<entities.Order>>> getOrders() async {
    try {
      final response = await dataSource.getOrders();
      final orders = response.toDomain();
      debugPrint('[OrderRepository] Successfully fetched ${orders.length} orders');
      return Right(orders);
    } catch (e) {
      debugPrint('[OrderRepository] Error fetching orders: $e');
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, entities.Order>> getOrderById(String orderId) async {
    try {
      final response = await dataSource.getOrderById(orderId);
      final order = response.toDomain();
      debugPrint('[OrderRepository] Successfully fetched order: $orderId');
      return Right(order);
    } catch (e) {
      debugPrint('[OrderRepository] Error fetching order $orderId: $e');
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, DiscountStats>> getDiscountStats() async {
    try {
      final response = await dataSource.getDiscountStats();
      final stats = response.toDomain();
      debugPrint('[OrderRepository] Successfully fetched discount stats');
      return Right(stats);
    } catch (e) {
      debugPrint('[OrderRepository] Error fetching discount stats: $e');
      return Left(_mapExceptionToFailure(e));
    }
  }

  Failure _mapExceptionToFailure(dynamic exception) {
    final message = exception.toString();
    
    if (message.contains('Unauthorized')) {
      return const AuthFailure(message: 'Authentication required');
    } else if (message.contains('Forbidden')) {
      return const AuthFailure(message: 'Access denied');
    } else if (message.contains('not found')) {
      return const NotFoundFailure(message: 'Order not found');
    } else if (message.contains('Network error')) {
      return NetworkFailure(message: message);
    } else {
      return ServerFailure(message: message);
    }
  }
}
