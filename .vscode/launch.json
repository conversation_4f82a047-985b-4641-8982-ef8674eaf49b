{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "flutter-travelgator",
      "request": "launch",
      "type": "dart",
      "deviceId": "00008140-00186D620286801C"
    },
    {
      "name": "flutter-travelgator (iPhone)",
      "request": "launch",
      "type": "dart",
      "deviceId": "00008140-00186D620286801C"
    },
    {
      "name": "flutter-travelgator (iPad)",
      "request": "launch",
      "type": "dart",
      "deviceId": "00008130-001170A90EA2001C"
    },
    {
      "name": "flutter-travelgator (profile mode)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "deviceId": "00008140-00186D620286801C"
    },
    {
      "name": "flutter-travelgator (release mode)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "deviceId": "00008140-00186D620286801C"
    }
  ]
}