import '../../domain/entities/payment_intent.dart';

/// Data model for payment intent response from backend
class PaymentIntentModel {
  final String clientSecret;
  final String paymentIntentId;
  final String orderId;
  final double amount;
  final String currency;

  const PaymentIntentModel({
    required this.clientSecret,
    required this.paymentIntentId,
    required this.orderId,
    required this.amount,
    required this.currency,
  });

  /// Create from JSON response
  factory PaymentIntentModel.fromJson(Map<String, dynamic> json) {
    return PaymentIntentModel(
      clientSecret: json['client_secret'] as String,
      paymentIntentId: json['payment_intent_id'] as String,
      orderId: json['order_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
    );
  }

  /// Create from checkout session response (fallback for hosted checkout)
  factory PaymentIntentModel.fromCheckoutSession(Map<String, dynamic> json) {
    return PaymentIntentModel(
      clientSecret: json['stripe_session_id'] as String, // Use session ID as fallback
      paymentIntentId: json['stripe_session_id'] as String, // Use session ID as fallback
      orderId: json['order_id'] as String,
      amount: (json['total_amount'] as num).toDouble(),
      currency: json['currency'] as String,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'client_secret': clientSecret,
      'payment_intent_id': paymentIntentId,
      'order_id': orderId,
      'amount': amount,
      'currency': currency,
    };
  }

  /// Convert to domain entity
  PaymentIntent toDomain() {
    return PaymentIntent(
      clientSecret: clientSecret,
      paymentIntentId: paymentIntentId,
      orderId: orderId,
      amount: amount,
      currency: currency,
    );
  }

  @override
  String toString() {
    return 'PaymentIntentModel(clientSecret: $clientSecret, paymentIntentId: $paymentIntentId, orderId: $orderId, amount: $amount, currency: $currency)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentIntentModel &&
        other.clientSecret == clientSecret &&
        other.paymentIntentId == paymentIntentId &&
        other.orderId == orderId &&
        other.amount == amount &&
        other.currency == currency;
  }

  @override
  int get hashCode {
    return clientSecret.hashCode ^
        paymentIntentId.hashCode ^
        orderId.hashCode ^
        amount.hashCode ^
        currency.hashCode;
  }
}
