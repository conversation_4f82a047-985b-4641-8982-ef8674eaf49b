import 'package:flutter/material.dart';
import '../../../../l10n/app_localizations.dart';

class TravelgatorScreen extends StatelessWidget {
  const TravelgatorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF3F4F6),
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).travelgator),
        backgroundColor: const Color(0xFFFF6982),
      ),
      body: Center(
        child: Text(
          AppLocalizations.of(context).travelgatorScreen,
          style: Theme.of(context).textTheme.headlineMedium,
        ),
      ),
    );
  }
}
