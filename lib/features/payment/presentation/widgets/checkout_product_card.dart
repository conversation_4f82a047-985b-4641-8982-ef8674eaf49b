import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_travelgator/features/cart/domain/entities/cart.dart';
import 'package:flutter_travelgator/core/providers/product_provider.dart';
import 'package:flutter_travelgator/core/models/product_model.dart';

/// Product card widget for checkout screen following Figma design
class CheckoutProductCard extends ConsumerWidget {
  final CartItem item;

  const CheckoutProductCard({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Try to get image URL from cart item first, then from products provider
    String? productImageUrl = item.imageUrl;

    // If no image URL from cart, try to find it in the products provider
    if (productImageUrl == null || productImageUrl.isEmpty) {
      final productsState = ref.watch(productsProvider);

      // Try to find matching product by ID
      final cartProductId = item.productId;
      final matchingProduct = productsState.products.where((product) {
        return _isMatchingProductId(product.id, cartProductId);
      }).firstOrNull;

      if (matchingProduct?.featuredImage?.url != null) {
        productImageUrl = matchingProduct!.featuredImage!.url;
      }
    }

    // Cart container - full width, responsive
    return Container(
      width: double.infinity, // Full width instead of fixed 375px
      padding: const EdgeInsets.all(8),
      decoration: const BoxDecoration(
        color: Color(0xFFFFFFFF), // Cart background
      ),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFFF9FAFB), // Card background
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            // Carousel (Product Image) - 108x108
            _buildProductImage(productImageUrl),

            const SizedBox(width: 8),

            // Name section - flexible width
            Expanded(
              child: _buildProductDetails(ref),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductImage(String? imageUrl) {
    return Container(
      width: 108,
      height: 108,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          // Rectangle 1 - Pink background
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFFFF6982),
                borderRadius: BorderRadius.circular(6),
              ),
            ),
          ),
          // Rectangle 3 - Product image
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: imageUrl != null && imageUrl.isNotEmpty
                  ? Image.network(
                      imageUrl,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Center(
                          child: CircularProgressIndicator(
                            value: loadingProgress.expectedTotalBytes != null
                                ? loadingProgress.cumulativeBytesLoaded /
                                    loadingProgress.expectedTotalBytes!
                                : null,
                            color: const Color(0xFFFF6982),
                            strokeWidth: 2,
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return _buildFallbackImage();
                      },
                    )
                  : _buildFallbackImage(),
            ),
          ),
          // Slide indicators removed - not needed for single images
        ],
      ),
    );
  }



  Widget _buildProductDetails(WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Product title - flexible height
        Text(
          item.title.isNotEmpty
              ? item.title
              : 'Malaysia eSIM Roaming Data 1- 12GB, 1- 15 days | true no...',
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            height: 17/14, // line-height: 17px
            color: const Color(0xFF1F2937),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        const SizedBox(height: 8),

        // Detail 1 - GB row
        _buildDetailRow('GB', _getDataAmount(ref)),

        const SizedBox(height: 8),

        // Detail 2 - Day row
        _buildDetailRow('Day', _getDuration(ref)),

        const SizedBox(height: 8),

        // Quantity display
        Row(
          children: [
            Text(
              'Qty: ${item.quantity}',
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF6B7280),
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // Price section - centered
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '\$${(item.price * item.quantity).toStringAsFixed(2)}', // Total price for quantity
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                height: 19/16,
                color: const Color(0xFF1F2937),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      children: [
        Expanded(
          child: Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              height: 17/14,
              color: const Color(0xFF9CA3AF),
            ),
          ),
        ),
        Text(
          value,
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            height: 17/14,
            color: const Color(0xFF1F2937),
          ),
        ),
      ],
    );
  }

  /// Get variant from products provider using variant ID
  Variant? _getVariantFromId(WidgetRef ref) {
    // Get all products from the provider
    final productsState = ref.read(productsProvider);

    // debugPrint('🔍 CheckoutProductCard: Looking for variant with ID: ${item.variantId}');
    // debugPrint('🔍 CheckoutProductCard: Available products: ${productsState.products.length}');

    // Find the product that contains this variant
    for (final product in productsState.products) {
      for (final variantEdge in product.variants.edges) {
        final variant = variantEdge.node;

        // Check multiple ID format possibilities:
        // 1. Exact match
        if (variant.id == item.variantId) {
          // debugPrint('🔍 CheckoutProductCard: Found exact match variant: ${variant.id}');
          return variant;
        }

        // 2. GID format vs numeric format
        // Extract numeric part from GID (e.g., "gid://shopify/ProductVariant/123" -> "123")
        final variantNumericId = variant.id.split('/').last;
        final cartItemNumericId = item.variantId.split('/').last;

        if (variantNumericId == cartItemNumericId) {
          // debugPrint('🔍 CheckoutProductCard: Found numeric match variant: ${variant.id} ($variantNumericId)');
          return variant;
        }

        // 3. Check if one contains the other
        if (variant.id.contains(item.variantId) ||
            item.variantId.contains(variant.id)) {
          // debugPrint('🔍 CheckoutProductCard: Found partial match variant: ${variant.id}');
          return variant;
        }
      }
    }

    // debugPrint('🔍 CheckoutProductCard: No variant found for ID: ${item.variantId}');
    return null;
  }

  /// Get data amount from variant options or fallback to title parsing
  String _getDataAmount(WidgetRef ref) {
    // Get variant data from product provider using variant_id
    final variant = _getVariantFromId(ref);
    if (variant != null) {
      // debugPrint('🔍 CheckoutProductCard: Found variant for data extraction: ${variant.id}');
      // debugPrint('🔍 CheckoutProductCard: Variant selectedOptions: ${variant.selectedOptions.map((o) => '${o.name}=${o.value}').join(', ')}');

      // Look for GB option in selectedOptions
      final gbOption = variant.getOptionValue('gb') ?? variant.getOptionValue('data');
      if (gbOption != null) {
        // debugPrint('🔍 CheckoutProductCard: Found GB option: $gbOption');
        // Extract numeric value from option (e.g., "1GB" -> "1", "5" -> "5")
        final regex = RegExp(r'(\d+)');
        final match = regex.firstMatch(gbOption);
        final result = match?.group(1) ?? '1';
        // debugPrint('🔍 CheckoutProductCard: Extracted data amount: $result');
        return '${result}GB';
      } else {
        // debugPrint('🔍 CheckoutProductCard: No GB/data option found in variant');
      }
    } else {
      // debugPrint('🔍 CheckoutProductCard: No variant found, using title fallback');
    }

    // Fallback to title parsing if variant not found
    final title = item.title;
    final regex = RegExp(r'(\d+(?:-\d+)?)\s*GB', caseSensitive: false);
    final match = regex.firstMatch(title);
    if (match != null) {
      final amount = match.group(1);
      if (amount!.contains('-')) {
        // If it's a range like "1-12GB", take the higher value
        final parts = amount.split('-');
        return '${parts.last}GB';
      }
      return '${amount}GB';
    }
    // debugPrint('🔍 CheckoutProductCard: Fallback data amount from title: 1GB');
    return '1GB'; // Default fallback
  }

  /// Get duration from variant options or fallback to title parsing
  String _getDuration(WidgetRef ref) {
    // Get variant data from product provider using variant_id
    final variant = _getVariantFromId(ref);
    if (variant != null) {
      // Look for days option in selectedOptions
      final daysOption = variant.getOptionValue('days') ?? variant.getOptionValue('validity');
      if (daysOption != null) {
        // debugPrint('🔍 CheckoutProductCard: Found days option: $daysOption');
        // Extract numeric value from option (e.g., "1 day" -> "1", "7 Days" -> "7")
        final regex = RegExp(r'(\d+)');
        final match = regex.firstMatch(daysOption);
        final duration = match?.group(1) ?? '1';
        // debugPrint('🔍 CheckoutProductCard: Extracted duration: $duration');
        return '$duration Days';
      }
    }

    // Fallback to title parsing if variant not found
    final title = item.title;
    final regex = RegExp(r'(\d+(?:-\d+)?)\s*days?', caseSensitive: false);
    final match = regex.firstMatch(title);
    if (match != null) {
      final duration = match.group(1);
      if (duration!.contains('-')) {
        // If it's a range like "1-15 days", take the higher value
        final parts = duration.split('-');
        return '${parts.last} Days';
      }
      return '$duration Days';
    }
    // debugPrint('🔍 CheckoutProductCard: Fallback duration from title: 1 Days');
    return '1 Days'; // Default fallback
  }

  /// Check if product IDs match (handle both numeric and GID formats)
  bool _isMatchingProductId(String productId, String cartProductId) {
    // Direct match
    if (productId == cartProductId) return true;

    // Extract numeric ID from GID format (e.g., "gid://shopify/Product/123" -> "123")
    final productNumericId = productId.split('/').last;
    final cartNumericId = cartProductId.split('/').last;

    return productNumericId == cartNumericId;
  }

  /// Build fallback image with eSIM icon and type
  Widget _buildFallbackImage() {
    return Container(
      width: 108,
      height: 108,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFF6982),
            Color(0xFFFF8FA3),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.sim_card,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(height: 4),
            Text(
              _getESimType(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Get eSIM type from product title
  String _getESimType() {
    final title = item.title;
    if (title.contains('(') && title.contains(')')) {
      final startIndex = title.lastIndexOf('(');
      final endIndex = title.lastIndexOf(')');
      if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
        return title.substring(startIndex + 1, endIndex);
      }
    }
    return 'New eSIM'; // Default fallback
  }
}
