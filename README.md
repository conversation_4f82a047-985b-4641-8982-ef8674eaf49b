# TravelGator - Flutter App

A travel application built with Flutter and Riverpod, following clean architecture principles.

## Project Structure

```
lib/
├── main.dart               # Entry point
├── app.dart                # App configuration
├── core/                   # Core functionality
│   ├── constants/          # App constants
│   ├── exceptions/         # Custom exceptions
│   ├── theme/              # App theming
│   └── utils/              # Utility functions
├── features/               # Feature modules
│   ├── auth/               # Authentication feature
│   │   ├── data/           # Data layer (repositories, models)
│   │   ├── domain/         # Domain layer (entities, usecases)
│   │   ├── presentation/   # UI layer (providers, screens, widgets)
│   │   └── auth_route.dart # Feature routes
│   ├── home/               # Home feature
│   │   └── ...
├── l10n/                   # Localization
├── routes/                 # App routing
│   └── app_router.dart
└── shared/                 # Shared code
    ├── widgets/            # Common widgets
    └── providers/          # Global providers
```

## Architecture

This project follows a clean architecture approach with a feature-based organization, ensuring separation of concerns and maintainable code. The architecture is divided into three main layers:

### 1. Data Layer (`data/`)
- **Repositories**: Implements the repository interfaces defined in the domain layer
- **Data Sources**:
  - Remote Data Sources: API clients, network calls
  - Local Data Sources: SharedPreferences, SQLite, Hive
- **Data Models**: DTOs (Data Transfer Objects) for API and local storage
- **Mappers**: Convert between data models and domain entities

### 2. Domain Layer (`domain/`)
The domain layer is the core of the application, containing business logic and rules. It is independent of any framework or external concerns.

#### Key Components:
- **Entities**: Core business objects (e.g., `User`, `Trip`, `Destination`)
- **Repository Interfaces**: Abstract contracts defining data operations
- **Use Cases**: Business logic implementation
  - Single Responsibility Principle: Each use case handles one specific action
  - Examples:
    - `GetUserProfileUseCase`
    - `CreateTripUseCase`
    - `SearchDestinationsUseCase`
- **Value Objects**: Immutable objects representing domain concepts
- **Failures**: Custom error types for domain-specific errors

#### Domain Layer Benefits:
1. **Independence**: Framework and UI agnostic
2. **Testability**: Business logic can be tested in isolation
3. **Maintainability**: Clear separation of concerns
4. **Reusability**: Domain logic can be reused across different UIs

### 3. Presentation Layer (`presentation/`)
- **Screens**: UI pages and views
- **Widgets**: Reusable UI components
- **State Management**:
  - Providers: Using Riverpod for state management
  - Controllers: Business logic for UI
  - State Classes: Immutable state objects

### Layer Communication Flow:
```
UI (Presentation) → Use Cases (Domain) → Repositories (Domain) → Data Sources (Data)
```

### Dependency Rule:
- Inner layers (Domain) have no knowledge of outer layers
- Dependencies point inward
- Domain layer has no dependencies on Data or Presentation layers

## State Management

The app uses Riverpod for state management with the following provider types:

- **Provider**: For simple dependencies and computations
- **StateProvider**: For simple state that can be modified from outside
- **StateNotifierProvider**: For complex state with reducers
- **FutureProvider**: For async data fetching

## Getting Started

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Run the app with `flutter run`

## Features

### Core Features
- **Authentication**: Firebase + Backend composite authentication
- **Product Catalog**: Browse and search eSIM products
- **Shopping Cart**: Add, update, and manage cart items
- **Checkout System**: Comprehensive payment processing
- **Buy Now**: Direct purchase without cart
- **Multi-language Support**: Vietnamese, English, Chinese
- **Theme Management**: Light/dark mode support

### Payment & Checkout
- **Stripe Integration**: Secure payment processing
- **In-App Payments**: Payment Intent flow with Stripe Elements
- **Web Checkout**: Fallback to Stripe hosted checkout
- **Payment Status Tracking**: Real-time payment monitoring
- **Error Handling**: Comprehensive error recovery with localization
- **Security**: PCI DSS compliant through Stripe

### Architecture
- **Clean Architecture**: Domain-driven design with clear separation
- **Feature-based Organization**: Modular code structure
- **State Management**: Riverpod for reactive state management
- **Testing**: Comprehensive unit and integration tests

## Payment Architecture

### Checkout Flows
1. **Cart Checkout**: Traditional multi-item checkout
   - Unified `/cart/checkout` endpoint
   - Support for selected items or full cart
   - In-app payment with Stripe Payment Element

2. **Buy Now**: Direct single-item purchase
   - Streamlined `/buy_now` endpoint
   - Immediate payment processing
   - Choice between in-app and web payment

### API Endpoints
- `POST /cart/checkout` - Unified cart checkout
- `POST /buy_now` - Direct purchase
- `GET /stripe/config` - Stripe configuration
- `GET /cart/payment_status` - Payment status polling

### Error Handling
- Localized error messages (Vietnamese/English)
- Smart retry mechanisms
- Graceful degradation for network issues
- Comprehensive logging for debugging

## Dependencies

### Core Dependencies
- **flutter_riverpod**: State management and dependency injection
- **equatable**: Value equality for entities
- **dartz**: Functional programming with Either types
- **google_fonts**: Typography and font management
- **go_router**: Declarative routing

### Payment Dependencies
- **flutter_stripe**: Stripe SDK integration
- **dio**: HTTP client for API calls
- **json_annotation**: JSON serialization

### UI Dependencies
- **font_awesome_flutter**: Icon library
- **flutter_svg**: SVG image support
- **cached_network_image**: Efficient image loading

### Development Dependencies
- **build_runner**: Code generation
- **json_serializable**: JSON model generation
- **flutter_test**: Testing framework

## Getting Started

### Prerequisites
- Flutter SDK (3.0+)
- Dart SDK (3.0+)
- iOS development: Xcode 14+
- Android development: Android Studio with API 21+

### Installation
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd flutter-travelgator
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Environment Setup**
   - Copy `.env.example` to `.env`
   - Configure API endpoints and Stripe keys
   - Set up Firebase configuration

4. **Run the app**
   ```bash
   # Development mode
   flutter run

   # Release mode
   flutter run --release
   ```

### Configuration

#### Environment Variables
```env
# API Configuration
BACKEND_API_URL=https://api.travelgator.com
API_TIMEOUT_SECONDS=30

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY_TEST=pk_test_...
STRIPE_PUBLISHABLE_KEY_LIVE=pk_live_...
STRIPE_TEST_MODE=true

# Feature Flags
ENABLE_BUY_NOW=true
ENABLE_IN_APP_PAYMENTS=true
```

#### Firebase Setup
1. Add `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
2. Configure Firebase Authentication providers
3. Set up Firebase Analytics (optional)

## Testing

### Running Tests
```bash
# Unit tests
flutter test

# Integration tests
flutter test integration_test/

# Test coverage
flutter test --coverage
```

### Test Structure
- **Unit Tests**: Domain entities, use cases, repositories
- **Widget Tests**: UI components and screens
- **Integration Tests**: End-to-end user flows
- **Payment Tests**: Stripe integration and error handling

## Documentation

### Architecture Documentation
- [Checkout Architecture](docs/checkout_architecture.md) - Comprehensive payment system documentation
- [Buy Now Documentation](docs/buy_now_documentation.md) - Direct purchase flow details
- [Authentication Flow](docs/authentication_flow.md) - Firebase + Backend auth integration

### API Documentation
- [Unified Checkout Verification](test_scenarios/unified_checkout_verification.md) - API testing scenarios
- [Postman Collection](knowledge_base/references/postman.json) - API endpoint documentation

## Contributing

### Development Workflow
1. Create feature branch from `main`
2. Follow clean architecture principles
3. Add comprehensive tests
4. Update documentation
5. Submit pull request

### Code Standards
- Follow [Effective Dart](https://dart.dev/guides/language/effective-dart) guidelines
- Use meaningful commit messages
- Maintain test coverage above 80%
- Document public APIs

### Architecture Guidelines
- Respect layer boundaries (Presentation → Domain → Data)
- Use dependency injection with Riverpod
- Implement proper error handling
- Follow feature-based organization

## Deployment

### Build Configuration
```bash
# Android APK
flutter build apk --release

# Android App Bundle
flutter build appbundle --release

# iOS
flutter build ios --release
```

### Environment-Specific Builds
- **Development**: Test Stripe keys, debug logging
- **Staging**: Test environment with production-like data
- **Production**: Live Stripe keys, optimized performance

## Monitoring & Analytics

### Key Metrics
- Payment success rates
- Checkout abandonment rates
- App performance metrics
- Error frequency and types

### Logging
- Comprehensive debug logging in development
- Error tracking with context information
- User journey analytics for optimization

## Support

### Troubleshooting
- Check [Issues](link-to-issues) for common problems
- Review logs for error details
- Verify environment configuration

### Contact
- Technical Support: [support-email]
- Development Team: [team-email]
- Documentation: [docs-link]
