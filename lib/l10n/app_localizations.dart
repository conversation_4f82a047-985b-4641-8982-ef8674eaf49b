import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_vi.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('vi'),
    Locale('zh')
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'TravelGator'**
  String get appTitle;

  /// Welcome message on the home screen
  ///
  /// In en, this message translates to:
  /// **'Welcome to TravelGator'**
  String get welcomeMessage;

  /// Text for sign in button
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// Text for sign up button
  ///
  /// In en, this message translates to:
  /// **'SIGN UP'**
  String get signUp;

  /// Label for email field
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// Label for password field
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// Label for name field
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// Text for forgot password link
  ///
  /// In en, this message translates to:
  /// **'Forgot Password'**
  String get forgotPassword;

  /// Text for social login separator
  ///
  /// In en, this message translates to:
  /// **'or continue with'**
  String get orContinueWith;

  /// Text for sign up prompt
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get dontHaveAccount;

  /// Text for sign in prompt
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get alreadyHaveAccount;

  /// Title for account creation screen
  ///
  /// In en, this message translates to:
  /// **'Create Account'**
  String get createAccount;

  /// Text for get started button
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get getStarted;

  /// Title for legal documents modal
  ///
  /// In en, this message translates to:
  /// **'Legal Documents'**
  String get legalDocuments;

  /// Title for user agreement document
  ///
  /// In en, this message translates to:
  /// **'User Agreement'**
  String get userAgreement;

  /// Title for privacy policy document
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// Title for terms and conditions document
  ///
  /// In en, this message translates to:
  /// **'Terms & Conditions'**
  String get termsAndConditions;

  /// Text showing when document was last updated
  ///
  /// In en, this message translates to:
  /// **'Last updated: {date}'**
  String lastUpdated(String date);

  /// Text for close button
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// Title for languages screen
  ///
  /// In en, this message translates to:
  /// **'Languages'**
  String get languages;

  /// Header text for language selection
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// Description text for language selection
  ///
  /// In en, this message translates to:
  /// **'Choose your preferred language for the app interface.'**
  String get languageDescription;

  /// Note about language change behavior
  ///
  /// In en, this message translates to:
  /// **'The app interface will update immediately when you select a new language.'**
  String get languageChangeNote;

  /// Confirmation message when language is changed
  ///
  /// In en, this message translates to:
  /// **'Language changed to {language}'**
  String languageChanged(String language);

  /// Title for account information section
  ///
  /// In en, this message translates to:
  /// **'Account Information'**
  String get accountInformation;

  /// Title for transaction history section
  ///
  /// In en, this message translates to:
  /// **'Transaction History'**
  String get transactionHistory;

  /// Title for payment settings section
  ///
  /// In en, this message translates to:
  /// **'Payment Settings'**
  String get paymentSettings;

  /// Title for currency section
  ///
  /// In en, this message translates to:
  /// **'Currency'**
  String get currency;

  /// Title for help center section
  ///
  /// In en, this message translates to:
  /// **'Help Center'**
  String get helpCenter;

  /// Text for logout button
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// Title for logout confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logoutConfirmTitle;

  /// Message for logout confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to logout?'**
  String get logoutConfirmMessage;

  /// Text for cancel button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// App version text
  ///
  /// In en, this message translates to:
  /// **'TravelGator version {version}'**
  String appVersion(String version);

  /// Cart screen title
  ///
  /// In en, this message translates to:
  /// **'Cart'**
  String get cart;

  /// Message when cart has no items
  ///
  /// In en, this message translates to:
  /// **'Cart is empty'**
  String get cartIsEmpty;

  /// Subtitle for empty cart
  ///
  /// In en, this message translates to:
  /// **'Add some products to get started'**
  String get addSomeProducts;

  /// Button text to start shopping
  ///
  /// In en, this message translates to:
  /// **'Start Shopping'**
  String get startShopping;

  /// Title for authentication required error
  ///
  /// In en, this message translates to:
  /// **'Authentication Required'**
  String get authenticationRequired;

  /// Message for authentication required
  ///
  /// In en, this message translates to:
  /// **'Please sign in to access your cart'**
  String get pleaseSignInToCart;

  /// Error message when cart fails to load
  ///
  /// In en, this message translates to:
  /// **'Unable to load cart'**
  String get unableToLoadCart;

  /// Generic cart loading error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load cart'**
  String get failedToLoadCart;

  /// Button text to retry an action
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// Label for total price
  ///
  /// In en, this message translates to:
  /// **'Total:'**
  String get total;

  /// Button text to add item to cart
  ///
  /// In en, this message translates to:
  /// **'Add to Cart'**
  String get addToCart;

  /// Button text to buy item immediately
  ///
  /// In en, this message translates to:
  /// **'Buy Now'**
  String get buyNow;

  /// Button text to hide product details
  ///
  /// In en, this message translates to:
  /// **'Hide details'**
  String get hideDetails;

  /// Button text to remove item
  ///
  /// In en, this message translates to:
  /// **'Remove'**
  String get remove;

  /// Success message when item is removed from cart
  ///
  /// In en, this message translates to:
  /// **'Item removed from cart'**
  String get itemRemovedFromCart;

  /// Label for variant selection
  ///
  /// In en, this message translates to:
  /// **'Variant'**
  String get variant;

  /// Error message when product has no variants
  ///
  /// In en, this message translates to:
  /// **'No variants available for this product'**
  String get noVariantsAvailable;

  /// Error message when adding to cart fails
  ///
  /// In en, this message translates to:
  /// **'Failed to add to cart: {error}'**
  String failedToAddToCart(String error);

  /// Gigabyte unit abbreviation
  ///
  /// In en, this message translates to:
  /// **'GB'**
  String get gb;

  /// Day unit for duration
  ///
  /// In en, this message translates to:
  /// **'Day'**
  String get day;

  /// Days unit for duration (plural)
  ///
  /// In en, this message translates to:
  /// **'Days'**
  String get days;

  /// Success message when user signs in
  ///
  /// In en, this message translates to:
  /// **'Successfully signed in!'**
  String get signedInSuccessfully;

  /// Error message when sign-in fails
  ///
  /// In en, this message translates to:
  /// **'Sign-in failed: {error}'**
  String signInFailed(String error);

  /// Button text to retry an action
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Success message when order is completed
  ///
  /// In en, this message translates to:
  /// **'🎉 Order completed successfully!'**
  String get orderCompletedSuccessfully;

  /// Error message when checkout fails
  ///
  /// In en, this message translates to:
  /// **'❌ Checkout failed: {error}'**
  String checkoutFailed(String error);

  /// Error message when buy now fails
  ///
  /// In en, this message translates to:
  /// **'❌ Buy Now failed: {error}'**
  String buyNowFailed(String error);

  /// Loading message during checkout
  ///
  /// In en, this message translates to:
  /// **'Processing checkout...'**
  String get processingCheckout;

  /// Checkout button text
  ///
  /// In en, this message translates to:
  /// **'Check out'**
  String get checkOut;

  /// Proceed to checkout button text
  ///
  /// In en, this message translates to:
  /// **'Proceed to Checkout'**
  String get proceedToCheckout;

  /// Home tab label in navigation bar
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// Account tab label in navigation bar
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get account;

  /// SIMs tab label in navigation bar
  ///
  /// In en, this message translates to:
  /// **'SIMs'**
  String get sims;

  /// TravelGator tab label and screen title
  ///
  /// In en, this message translates to:
  /// **'TravelGator'**
  String get travelgator;

  /// Rewards tab label in navigation bar
  ///
  /// In en, this message translates to:
  /// **'Rewards'**
  String get rewards;

  /// TravelGator screen content title
  ///
  /// In en, this message translates to:
  /// **'TravelGator Screen'**
  String get travelgatorScreen;

  /// Welcome title in onboarding
  ///
  /// In en, this message translates to:
  /// **'Welcome to\nTravelGator'**
  String get welcomeToTravelgator;

  /// Stay connected title in onboarding
  ///
  /// In en, this message translates to:
  /// **'Stay Connected'**
  String get stayConnected;

  /// Stay connected globally title
  ///
  /// In en, this message translates to:
  /// **'Stay Connected\nGlobally'**
  String get stayConnectedGlobally;

  /// Discover and explore title
  ///
  /// In en, this message translates to:
  /// **'Discover & Explore\nWith Ease'**
  String get discoverExplore;

  /// Discover amazing destinations title
  ///
  /// In en, this message translates to:
  /// **'Discover Amazing\nDestinations'**
  String get discoverAmazingDestinations;

  /// Details screen title
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get details;

  /// Your purchase section title
  ///
  /// In en, this message translates to:
  /// **'Your Purchase'**
  String get yourPurchase;

  /// Your data section title
  ///
  /// In en, this message translates to:
  /// **'Your Data'**
  String get yourData;

  /// New eSIM option label
  ///
  /// In en, this message translates to:
  /// **'New eSIM'**
  String get newESim;

  /// Top up option label
  ///
  /// In en, this message translates to:
  /// **'Top Up'**
  String get topUp;

  /// Total payment label
  ///
  /// In en, this message translates to:
  /// **'Total Payment'**
  String get totalPayment;

  /// See details button text
  ///
  /// In en, this message translates to:
  /// **'See details'**
  String get seeDetails;

  /// Default option for eSIM selection
  ///
  /// In en, this message translates to:
  /// **'Select your TravelGator eSIM'**
  String get selectYourTravelgatorESim;

  /// Data amount selection label
  ///
  /// In en, this message translates to:
  /// **'Data Amount'**
  String get dataAmount;

  /// Duration selection label
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get duration;

  /// Quantity selection label
  ///
  /// In en, this message translates to:
  /// **'Quantity'**
  String get quantity;

  /// eSIM type selection label
  ///
  /// In en, this message translates to:
  /// **'eSIM Type'**
  String get eSimType;

  /// Title for purchase review popup
  ///
  /// In en, this message translates to:
  /// **'Review your purchase'**
  String get reviewYourPurchase;

  /// Label for data amount dropdown
  ///
  /// In en, this message translates to:
  /// **'Your data'**
  String get yourDataLabel;

  /// Label for day duration dropdown
  ///
  /// In en, this message translates to:
  /// **'Day'**
  String get dayLabel;

  /// One day option
  ///
  /// In en, this message translates to:
  /// **'1 day'**
  String get oneDay;

  /// Three days option
  ///
  /// In en, this message translates to:
  /// **'3 days'**
  String get threeDays;

  /// Seven days option
  ///
  /// In en, this message translates to:
  /// **'7 days'**
  String get sevenDays;

  /// Fifteen days option
  ///
  /// In en, this message translates to:
  /// **'15 days'**
  String get fifteenDays;

  /// Thirty days option
  ///
  /// In en, this message translates to:
  /// **'30 days'**
  String get thirtyDays;

  /// Confirmation message for removing item from cart
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to remove \"{itemTitle}\" from your cart?'**
  String removeItemConfirmation(String itemTitle);

  /// Title for checkout failed error
  ///
  /// In en, this message translates to:
  /// **'Checkout Failed'**
  String get checkoutFailedTitle;

  /// Detailed message for checkout failure
  ///
  /// In en, this message translates to:
  /// **'There was an issue processing your checkout. This is likely a backend configuration issue with Shopify.'**
  String get checkoutFailedMessage;

  /// Title for checkout error
  ///
  /// In en, this message translates to:
  /// **'Checkout Error'**
  String get checkoutErrorTitle;

  /// Text for delete account button
  ///
  /// In en, this message translates to:
  /// **'Delete My Account'**
  String get deleteMyAccount;

  /// Title for delete account confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get deleteAccountConfirmTitle;

  /// Message for delete account confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete your account? This will permanently remove all your data from our system.'**
  String get deleteAccountConfirmMessage;

  /// Text for delete button
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Error message when account deletion fails
  ///
  /// In en, this message translates to:
  /// **'Account deletion failed'**
  String get accountDeletionFailed;

  /// Success message when account is deleted
  ///
  /// In en, this message translates to:
  /// **'Account deleted successfully'**
  String get accountDeletedSuccessfully;

  /// Error message when account cannot be deleted
  ///
  /// In en, this message translates to:
  /// **'Cannot delete account'**
  String get cannotDeleteAccount;

  /// Loading message when checking account deletion eligibility
  ///
  /// In en, this message translates to:
  /// **'Checking if account can be deleted...'**
  String get checkingAccountDeletion;

  /// Loading message when deleting account
  ///
  /// In en, this message translates to:
  /// **'Deleting account...'**
  String get deletingAccount;

  /// Error message for server errors during account deletion
  ///
  /// In en, this message translates to:
  /// **'Server error occurred. Please try again later or contact support for assistance.'**
  String get serverErrorContactSupport;

  /// Error message when products fail to load
  ///
  /// In en, this message translates to:
  /// **'Error loading products'**
  String get errorLoadingProducts;

  /// Message when no products match the filter
  ///
  /// In en, this message translates to:
  /// **'No products found'**
  String get noProductsFound;

  /// Suggestion when no products are found
  ///
  /// In en, this message translates to:
  /// **'Try adjusting your filter'**
  String get tryAdjustingFilter;

  /// Success message when product is added to cart
  ///
  /// In en, this message translates to:
  /// **'{productTitle} added to cart!'**
  String productAddedToCart(String productTitle);

  /// Warning message when cart operation status is unclear
  ///
  /// In en, this message translates to:
  /// **'Cart operation may have failed. Please check your cart.'**
  String get cartOperationMayHaveFailed;

  /// Loading message when adding item to cart
  ///
  /// In en, this message translates to:
  /// **'Adding to cart...'**
  String get processingAddToCart;

  /// Prefix for price display, e.g. 'From $10.99'
  ///
  /// In en, this message translates to:
  /// **'From'**
  String get from;

  /// Label for minimum price filter
  ///
  /// In en, this message translates to:
  /// **'Min Price'**
  String get minPrice;

  /// Label for maximum price filter
  ///
  /// In en, this message translates to:
  /// **'Max Price'**
  String get maxPrice;

  /// Button text to apply price filter
  ///
  /// In en, this message translates to:
  /// **'Apply Filter'**
  String get applyFilter;

  /// Button text to clear price filter
  ///
  /// In en, this message translates to:
  /// **'Clear Filter'**
  String get clearFilter;

  /// Title for price filter section
  ///
  /// In en, this message translates to:
  /// **'Price Filter'**
  String get priceFilter;

  /// Prefix for price range, e.g. 'Under $5'
  ///
  /// In en, this message translates to:
  /// **'Under'**
  String get under;

  /// Prefix for price range, e.g. 'Over $50'
  ///
  /// In en, this message translates to:
  /// **'Over'**
  String get over;

  /// Label for custom price range section
  ///
  /// In en, this message translates to:
  /// **'Or set custom range:'**
  String get orSetCustomRange;

  /// Connector word between min and max price, e.g. '$5 to $10'
  ///
  /// In en, this message translates to:
  /// **'to'**
  String get to;

  /// Country name: Vietnam
  ///
  /// In en, this message translates to:
  /// **'Vietnam'**
  String get countryVietnam;

  /// Country name: China
  ///
  /// In en, this message translates to:
  /// **'China'**
  String get countryChina;

  /// Country name: United States
  ///
  /// In en, this message translates to:
  /// **'United States'**
  String get countryUnitedStates;

  /// Country name: New Zealand
  ///
  /// In en, this message translates to:
  /// **'New Zealand'**
  String get countryNewZealand;

  /// Region name: ASEAN
  ///
  /// In en, this message translates to:
  /// **'ASEAN'**
  String get regionAsean;

  /// Region name: East Asia
  ///
  /// In en, this message translates to:
  /// **'East Asia'**
  String get regionEastAsia;

  /// Region name: North America
  ///
  /// In en, this message translates to:
  /// **'North America'**
  String get regionNorthAmerica;

  /// Region name: Oceania
  ///
  /// In en, this message translates to:
  /// **'Oceania'**
  String get regionOceania;

  /// Description text for eSIM products
  ///
  /// In en, this message translates to:
  /// **'eSIM Roaming Data'**
  String get eSIMRoamingData;

  /// Tab label for countries filter
  ///
  /// In en, this message translates to:
  /// **'Countries'**
  String get countries;

  /// Tab label for regions filter
  ///
  /// In en, this message translates to:
  /// **'Regions'**
  String get regions;

  /// Message shown when user successfully authenticates
  ///
  /// In en, this message translates to:
  /// **'Authentication successful!'**
  String get authenticationSuccessful;

  /// Success message when payment is completed
  ///
  /// In en, this message translates to:
  /// **'🎉 Payment successful!'**
  String get paymentSuccessful;

  /// Message when payment is canceled by user
  ///
  /// In en, this message translates to:
  /// **'Payment canceled'**
  String get paymentCanceled;

  /// Generic payment failure message
  ///
  /// In en, this message translates to:
  /// **'Payment failed'**
  String get paymentFailed;

  /// Message for unknown payment errors
  ///
  /// In en, this message translates to:
  /// **'Unknown payment error'**
  String get paymentErrorUnknown;

  /// Error when payment intent creation fails
  ///
  /// In en, this message translates to:
  /// **'Unable to create payment request. Please try again.'**
  String get paymentIntentCreationFailed;

  /// Error when backend payment verification fails
  ///
  /// In en, this message translates to:
  /// **'Payment verification failed. Please contact support.'**
  String get paymentVerificationFailed;

  /// Error when payment succeeds but order completion fails
  ///
  /// In en, this message translates to:
  /// **'Payment successful but unable to complete order. Please contact support.'**
  String get paymentSucceededOrderFailed;

  /// Error for backend system issues
  ///
  /// In en, this message translates to:
  /// **'Backend system error. Please contact technical support.'**
  String get backendSystemError;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'vi', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'vi': return AppLocalizationsVi();
    case 'zh': return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
